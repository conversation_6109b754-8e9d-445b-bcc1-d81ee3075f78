"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ToolExecutionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutionService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const client_1 = require("@prisma/client");
const event_emitter_mock_1 = require("../../agents/mocks/event-emitter.mock");
const tool_cache_service_1 = require("./tool-cache.service");
const crypto = require("crypto");
let ToolExecutionService = ToolExecutionService_1 = class ToolExecutionService {
    constructor(prisma, cacheManager, configService, eventEmitter, cacheService) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.cacheService = cacheService;
        this.logger = new common_1.Logger(ToolExecutionService_1.name);
        this.executors = new Map();
        this.activeExecutions = new Map();
        this.initializeExecutors();
    }
    async executeTool(context) {
        const executionId = this.generateExecutionId();
        const startTime = Date.now();
        try {
            const tool = await this.getToolDefinition(context.toolId, context.organizationId);
            if (!tool.isActive) {
                throw new common_1.BadRequestException('Tool is not active');
            }
            await this.validateInput(tool, context.input);
            const cachedResult = await this.checkCache(tool, context.input);
            if (cachedResult) {
                return {
                    success: true,
                    output: cachedResult.output,
                    duration: Date.now() - startTime,
                    cached: true,
                    metadata: {
                        executionId,
                        cacheHit: true,
                    },
                };
            }
            const execution = await this.createExecutionRecord(executionId, tool, context);
            this.activeExecutions.set(executionId, {
                startTime,
                tool,
                context,
                status: 'running',
            });
            this.eventEmitter.emit('tool.execution.start', {
                executionId,
                toolId: context.toolId,
                inputs: context.input,
                sessionId: context.sessionId,
            });
            const result = await this.executeWithRetryAndTimeout(executionId, tool, context);
            await this.updateExecutionRecord(executionId, {
                status: client_1.ToolExecutionStatus.COMPLETED,
                output: result.output,
                duration: result.duration,
                tokensUsed: result.usage?.tokensUsed || 0,
                cost: result.usage?.cost || 0,
                memoryUsed: result.usage?.memoryUsed,
                cpuTime: result.usage?.cpuTime,
                completedAt: new Date(),
            });
            await this.cacheResult(tool, context.input, result.output);
            await this.updateToolStats(context.toolId, true, result.duration);
            this.eventEmitter.emit('tool.execution.complete', {
                executionId,
                outputs: result.output,
                duration: result.duration,
                cached: false,
            });
            this.activeExecutions.delete(executionId);
            return {
                success: true,
                output: result.output,
                duration: result.duration,
                cached: false,
                metadata: {
                    executionId,
                    toolVersion: tool.version,
                },
                usage: result.usage,
            };
        }
        catch (error) {
            const duration = Date.now() - startTime;
            try {
                await this.updateExecutionRecord(executionId, {
                    status: client_1.ToolExecutionStatus.FAILED,
                    error: error.message,
                    duration,
                    completedAt: new Date(),
                });
            }
            catch (dbError) {
                this.logger.error('Failed to update execution record', dbError);
            }
            await this.updateToolStats(context.toolId, false, duration);
            this.eventEmitter.emit('tool.execution.error', {
                executionId,
                error: error.message,
                retryable: this.isRetryableError(error),
            });
            this.activeExecutions.delete(executionId);
            return {
                success: false,
                error: error.message,
                duration,
                cached: false,
                metadata: {
                    executionId,
                    errorType: error.constructor.name,
                },
            };
        }
    }
    async executeWithRetryAndTimeout(executionId, tool, context) {
        const timeout = context.timeout || tool.timeout || 30000;
        const retryPolicy = context.retryPolicy || tool.retryPolicy || {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            initialDelay: 1000,
        };
        let lastError;
        let attempt = 0;
        while (attempt <= retryPolicy.maxRetries) {
            try {
                const result = await Promise.race([
                    this.executeToolInternal(tool, context.input, context),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Tool execution timeout')), timeout)),
                ]);
                return result;
            }
            catch (error) {
                lastError = error;
                attempt++;
                if (attempt <= retryPolicy.maxRetries && this.isRetryableError(error)) {
                    const delay = this.calculateRetryDelay(attempt, retryPolicy);
                    this.logger.warn(`Tool execution attempt ${attempt} failed, retrying in ${delay}ms: ${tool.id}`, error.message);
                    this.eventEmitter.emit('tool.execution.progress', {
                        executionId,
                        progress: (attempt / (retryPolicy.maxRetries + 1)) * 100,
                        message: `Retry attempt ${attempt}/${retryPolicy.maxRetries}`,
                    });
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                else {
                    break;
                }
            }
        }
        throw lastError;
    }
    async executeToolInternal(tool, input, context) {
        const executor = this.executors.get(tool.type);
        if (!executor) {
            throw new common_1.BadRequestException(`No executor found for tool type: ${tool.type}`);
        }
        if (executor.validate) {
            await executor.validate(tool, input);
        }
        const startTime = Date.now();
        const output = await executor.execute(tool, input, context);
        const duration = Date.now() - startTime;
        return {
            output,
            duration,
            usage: {},
        };
    }
    initializeExecutors() {
        this.executors.set(client_1.ToolType.API_FETCH, {
            type: client_1.ToolType.API_FETCH,
            execute: this.executeAPIFetch.bind(this),
            validate: this.validateAPIFetchInput.bind(this),
            healthCheck: this.healthCheckAPIFetch.bind(this),
        });
        this.executors.set(client_1.ToolType.FUNCTION_CALL, {
            type: client_1.ToolType.FUNCTION_CALL,
            execute: this.executeFunctionCall.bind(this),
            validate: this.validateFunctionCallInput.bind(this),
        });
        this.executors.set(client_1.ToolType.RAG, {
            type: client_1.ToolType.RAG,
            execute: this.executeRAG.bind(this),
            validate: this.validateRAGInput.bind(this),
        });
        this.logger.log('Initialized tool executors');
    }
    async executeAPIFetch(tool, input, context) {
        const config = tool.config;
        const { endpoint, method, headers = {}, authentication } = config;
        const authHeaders = await this.applyAuthentication(authentication, tool.id);
        const finalHeaders = { ...headers, ...authHeaders };
        const requestData = this.mapInputToRequest(input, config.requestMapping);
        const requestOptions = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...finalHeaders,
            },
        };
        if (['POST', 'PUT', 'PATCH'].includes(method) && requestData) {
            requestOptions.body = JSON.stringify(requestData);
        }
        const url = method === 'GET' && requestData
            ? `${endpoint}?${new URLSearchParams(requestData)}`
            : endpoint;
        const response = await fetch(url, requestOptions);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const responseData = await response.json();
        return this.mapResponseToOutput(responseData, config.responseMapping);
    }
    async validateAPIFetchInput(tool, input) {
        if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
        }
    }
    async healthCheckAPIFetch(tool) {
        try {
            const config = tool.config;
            const response = await fetch(config.endpoint, {
                method: 'HEAD',
                headers: await this.applyAuthentication(config.authentication, tool.id),
            });
            return response.ok;
        }
        catch {
            return false;
        }
    }
    async executeFunctionCall(tool, input, context) {
        const config = tool.config;
        const { code, runtime, environment = {} } = config;
        switch (runtime) {
            case 'javascript':
                return this.executeJavaScript(code, input, environment);
            case 'python':
                return this.executePython(code, input, environment);
            case 'shell':
                return this.executeShell(code, input, environment);
            default:
                throw new common_1.BadRequestException(`Unsupported runtime: ${runtime}`);
        }
    }
    async executeJavaScript(code, input, environment) {
        const sandbox = {
            input,
            environment,
            console: {
                log: (...args) => this.logger.debug('Tool JS Log:', ...args),
            },
            fetch,
            Buffer,
            setTimeout,
            clearTimeout,
        };
        const func = new Function(...Object.keys(sandbox), `
      "use strict";
      ${code}
      
      // If no return statement, return the result variable if it exists
      if (typeof result !== 'undefined') {
        return result;
      }
    `);
        return func(...Object.values(sandbox));
    }
    async executePython(code, input, environment) {
        throw new common_1.BadRequestException('Python execution not implemented in this environment');
    }
    async executeShell(code, input, environment) {
        throw new common_1.BadRequestException('Shell execution not implemented for security reasons');
    }
    async validateFunctionCallInput(tool, input) {
        if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
        }
    }
    async executeRAG(tool, input, context) {
        const config = tool.config;
        const { vectorStore, embedding, retrieval } = config;
        return {
            query: input.query,
            results: [
                {
                    content: 'Mock RAG result',
                    score: 0.95,
                    metadata: {},
                },
            ],
            totalResults: 1,
        };
    }
    async validateRAGInput(tool, input) {
        if (!input.query || typeof input.query !== 'string') {
            throw new common_1.BadRequestException('RAG tools require a query string');
        }
    }
    async getToolDefinition(toolId, organizationId) {
        const tool = await this.prisma.toolDefinition.findFirst({
            where: {
                id: toolId,
                isActive: true,
                OR: [
                    { isPublic: true },
                    { organizationId },
                ],
            },
        });
        if (!tool) {
            throw new common_1.NotFoundException('Tool not found or not accessible');
        }
        return tool;
    }
    async validateInput(tool, input) {
        if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
            try {
                if (tool.inputSchema.required) {
                    for (const field of tool.inputSchema.required) {
                        if (!(field in input)) {
                            throw new Error(`Required field '${field}' is missing`);
                        }
                    }
                }
            }
            catch (error) {
                throw new common_1.BadRequestException(`Input validation failed: ${error.message}`);
            }
        }
    }
    async checkCache(tool, input) {
        if (tool.cacheStrategy === client_1.ToolCacheStrategy.NONE) {
            return null;
        }
        return this.cacheService.get(tool.id, input);
    }
    async cacheResult(tool, input, output) {
        if (tool.cacheStrategy !== client_1.ToolCacheStrategy.NONE) {
            await this.cacheService.set(tool.id, input, output, tool.cacheTTL);
        }
    }
    async createExecutionRecord(executionId, tool, context) {
        return this.prisma.toolExecution.create({
            data: {
                id: executionId,
                toolId: tool.id,
                organizationId: context.organizationId,
                executorType: context.executorType,
                executorId: context.executorId,
                sessionId: context.sessionId,
                status: client_1.ToolExecutionStatus.RUNNING,
                input: context.input,
                metadata: context.metadata || {},
                startedAt: new Date(),
            },
        });
    }
    async updateExecutionRecord(executionId, updates) {
        await this.prisma.toolExecution.update({
            where: { id: executionId },
            data: updates,
        });
    }
    async updateToolStats(toolId, success, duration) {
        const updates = {
            usageCount: { increment: 1 },
            avgLatency: duration,
        };
        if (success) {
            updates.successRate = { increment: 0.01 };
        }
        await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: updates,
        });
    }
    generateExecutionId() {
        return `exec_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
    }
    isRetryableError(error) {
        const retryablePatterns = [
            /timeout/i,
            /network/i,
            /connection/i,
            /rate limit/i,
            /temporary/i,
        ];
        return retryablePatterns.some(pattern => pattern.test(error.message));
    }
    calculateRetryDelay(attempt, retryPolicy) {
        const { backoffStrategy, initialDelay } = retryPolicy;
        if (backoffStrategy === 'exponential') {
            return initialDelay * Math.pow(2, attempt - 1);
        }
        else {
            return initialDelay * attempt;
        }
    }
    async applyAuthentication(authConfig, toolId) {
        if (!authConfig || authConfig.type === 'none') {
            return {};
        }
        switch (authConfig.type) {
            case 'bearer':
                const apiKey = await this.getToolAPIKey(toolId, 'bearer');
                return {
                    'Authorization': `Bearer ${apiKey}`,
                };
            case 'api_key':
                const key = await this.getToolAPIKey(toolId, 'api_key');
                const headerName = authConfig.config?.headerName || 'X-API-Key';
                return {
                    [headerName]: key,
                };
            case 'oauth':
                return {};
            default:
                return {};
        }
    }
    async getToolAPIKey(toolId, keyType) {
        return 'mock_api_key';
    }
    mapInputToRequest(input, mapping) {
        if (!mapping) {
            return input;
        }
        const result = {};
        for (const [targetKey, sourceKey] of Object.entries(mapping)) {
            if (typeof sourceKey === 'string' && sourceKey in input) {
                result[targetKey] = input[sourceKey];
            }
        }
        return result;
    }
    mapResponseToOutput(response, mapping) {
        if (!mapping) {
            return response;
        }
        const result = {};
        for (const [targetKey, sourceKey] of Object.entries(mapping)) {
            if (typeof sourceKey === 'string' && this.getNestedValue(response, sourceKey) !== undefined) {
                result[targetKey] = this.getNestedValue(response, sourceKey);
            }
        }
        return result;
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    async getActiveExecutions(organizationId) {
        return this.prisma.toolExecution.findMany({
            where: {
                organizationId,
                status: {
                    in: [client_1.ToolExecutionStatus.PENDING, client_1.ToolExecutionStatus.RUNNING],
                },
            },
            include: {
                tool: {
                    select: { id: true, name: true, type: true },
                },
            },
            orderBy: { startedAt: 'desc' },
        });
    }
    async getExecutionHistory(toolId, organizationId, limit = 50) {
        const where = {};
        if (toolId) {
            where.toolId = toolId;
        }
        if (organizationId) {
            where.organizationId = organizationId;
        }
        return this.prisma.toolExecution.findMany({
            where,
            include: {
                tool: {
                    select: { id: true, name: true, type: true },
                },
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
    }
    async cancelExecution(executionId, organizationId) {
        const execution = await this.prisma.toolExecution.findFirst({
            where: {
                id: executionId,
                organizationId,
                status: {
                    in: [client_1.ToolExecutionStatus.PENDING, client_1.ToolExecutionStatus.RUNNING],
                },
            },
        });
        if (!execution) {
            throw new common_1.NotFoundException('Execution not found or cannot be cancelled');
        }
        await this.prisma.toolExecution.update({
            where: { id: executionId },
            data: {
                status: client_1.ToolExecutionStatus.CANCELLED,
                completedAt: new Date(),
            },
        });
        this.activeExecutions.delete(executionId);
        this.logger.log(`Cancelled execution: ${executionId}`);
    }
};
exports.ToolExecutionService = ToolExecutionService;
exports.ToolExecutionService = ToolExecutionService = ToolExecutionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, config_1.ConfigService,
        event_emitter_mock_1.MockEventEmitter,
        tool_cache_service_1.ToolCacheService])
], ToolExecutionService);
//# sourceMappingURL=tool-execution.service.js.map