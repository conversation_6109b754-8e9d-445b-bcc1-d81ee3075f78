"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SkillExecutorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SkillExecutorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_mock_1 = require("../mocks/event-emitter.mock");
const z = require("zod");
let SkillExecutorService = SkillExecutorService_1 = class SkillExecutorService {
    constructor(prisma, cacheManager, configService, eventEmitter) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(SkillExecutorService_1.name);
        this.skillRegistry = new Map();
        this.executionStats = new Map();
        this.initializeBuiltInSkills();
    }
    async registerSkill(skill) {
        this.validateSkillDefinition(skill);
        this.skillRegistry.set(skill.id, skill);
        await this.cacheManager.set(`skill:${skill.id}`, skill, 3600000);
        this.logger.log(`Registered skill: ${skill.id} (${skill.name})`);
        this.eventEmitter.emit('skill.registered', {
            skillId: skill.id,
            name: skill.name,
            category: skill.category,
        });
    }
    async unregisterSkill(skillId) {
        this.skillRegistry.delete(skillId);
        await this.cacheManager.del(`skill:${skillId}`);
        this.logger.log(`Unregistered skill: ${skillId}`);
        this.eventEmitter.emit('skill.unregistered', { skillId });
    }
    async getSkill(skillId) {
        let skill = await this.cacheManager.get(`skill:${skillId}`);
        if (!skill) {
            skill = this.skillRegistry.get(skillId) || null;
            if (skill) {
                await this.cacheManager.set(`skill:${skillId}`, skill, 3600000);
            }
        }
        return skill;
    }
    async getSkillsByCategory(category) {
        const skills = Array.from(this.skillRegistry.values());
        return skills.filter(skill => skill.category === category);
    }
    async getSkillsForAgent(agentId) {
        const agent = await this.prisma.agentInstance.findUnique({
            where: { id: agentId },
            select: { skills: true, capabilities: true },
        });
        if (!agent) {
            return [];
        }
        const skills = [];
        for (const skillId of agent.skills) {
            const skill = await this.getSkill(skillId);
            if (skill) {
                skills.push(skill);
            }
        }
        return skills;
    }
    async getAgentTools(agentId) {
        const skills = await this.getSkillsForAgent(agentId);
        return skills.map(skill => ({
            type: 'function',
            definition: {
                name: skill.id,
                description: skill.description,
                parameters: this.zodSchemaToOpenAPISchema(skill.parameters),
            },
            skill,
        }));
    }
    async executeSkill(agentId, skillId, parameters, context) {
        const startTime = Date.now();
        try {
            const skill = await this.getSkill(skillId);
            if (!skill) {
                throw new common_1.NotFoundException(`Skill not found: ${skillId}`);
            }
            await this.validateSkillAccess(agentId, skill);
            const validatedParams = this.validateParameters(skill, parameters);
            await this.checkRateLimits(agentId, skillId, skill);
            const result = await this.performSkillExecution(skill, validatedParams, context || { agentId, sessionId: '', organizationId: '' });
            const validatedResult = this.validateResult(skill, result);
            const executionTime = Date.now() - startTime;
            await this.trackSkillExecution(agentId, skillId, executionTime, true);
            this.eventEmitter.emit('skill.executed', {
                agentId,
                skillId,
                success: true,
                duration: executionTime,
                parameters: validatedParams,
            });
            return {
                success: true,
                result: validatedResult,
                duration: executionTime,
                metadata: {
                    skillId,
                    skillName: skill.name,
                    version: skill.version,
                },
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            this.logger.error(`Skill execution failed: ${skillId}`, error.stack);
            await this.trackSkillExecution(agentId, skillId, executionTime, false);
            this.eventEmitter.emit('skill.error', {
                agentId,
                skillId,
                error: error.message,
                duration: executionTime,
            });
            return {
                success: false,
                error: error.message,
                duration: executionTime,
                metadata: {
                    skillId,
                    errorType: error.constructor.name,
                },
            };
        }
    }
    async performSkillExecution(skill, parameters, context) {
        const timeout = skill.timeout || 30000;
        const executionEnv = {
            prisma: this.prisma,
            cache: this.cacheManager,
            config: this.configService,
            context,
            logger: this.logger,
            emit: (event, data) => this.eventEmitter.emit(event, data),
        };
        return Promise.race([
            this.executeWithRetry(skill, parameters, executionEnv),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Skill execution timeout')), timeout)),
        ]);
    }
    async executeWithRetry(skill, parameters, env) {
        const retryPolicy = skill.retryPolicy || {
            maxRetries: 1,
            backoffStrategy: 'linear',
            initialDelay: 1000,
        };
        let lastError = null;
        for (let attempt = 0; attempt <= retryPolicy.maxRetries; attempt++) {
            try {
                if (typeof skill.implementation === 'function') {
                    return await skill.implementation(parameters, env);
                }
                else {
                    return await this.executeSandboxedCode(skill.implementation, parameters, env);
                }
            }
            catch (error) {
                lastError = error;
                if (attempt < retryPolicy.maxRetries) {
                    const delay = this.calculateBackoffDelay(attempt, retryPolicy.backoffStrategy, retryPolicy.initialDelay);
                    this.logger.warn(`Skill execution attempt ${attempt + 1} failed, retrying in ${delay}ms: ${skill.id}`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
    calculateBackoffDelay(attempt, strategy, initialDelay) {
        if (strategy === 'exponential') {
            return initialDelay * Math.pow(2, attempt);
        }
        else {
            return initialDelay * (attempt + 1);
        }
    }
    initializeBuiltInSkills() {
        this.registerBuiltInSkill({
            id: 'text_analyzer',
            name: 'Text Analyzer',
            description: 'Analyze text for sentiment, entities, and key phrases',
            category: 'DATA_ANALYSIS',
            version: '1.0.0',
            parameters: z.object({
                text: z.string().min(1).max(10000),
                analysis_types: z.array(z.enum(['sentiment', 'entities', 'keywords'])).optional().default(['sentiment']),
            }),
            returns: z.object({
                sentiment: z.object({
                    score: z.number(),
                    label: z.enum(['positive', 'negative', 'neutral']),
                }).optional(),
                entities: z.array(z.object({
                    text: z.string(),
                    type: z.string(),
                    confidence: z.number(),
                })).optional(),
                keywords: z.array(z.string()).optional(),
            }),
            implementation: this.textAnalyzerSkill.bind(this),
            permissions: ['text:analyze'],
            tags: ['nlp', 'analysis'],
            author: 'system',
        });
        this.registerBuiltInSkill({
            id: 'web_scraper',
            name: 'Web Scraper',
            description: 'Extract content from web pages',
            category: 'INTEGRATION',
            version: '1.0.0',
            parameters: z.object({
                url: z.string().url(),
                selectors: z.object({
                    title: z.string().optional(),
                    content: z.string().optional(),
                    links: z.string().optional(),
                }).optional(),
                options: z.object({
                    timeout: z.number().optional().default(10000),
                    user_agent: z.string().optional(),
                }).optional(),
            }),
            returns: z.object({
                title: z.string().optional(),
                content: z.string().optional(),
                links: z.array(z.string()).optional(),
                metadata: z.object({
                    url: z.string(),
                    timestamp: z.string(),
                    status: z.number(),
                }),
            }),
            implementation: this.webScraperSkill.bind(this),
            permissions: ['web:scrape'],
            tags: ['web', 'scraping'],
            author: 'system',
            rateLimits: {
                requestsPerMinute: 10,
                requestsPerHour: 100,
            },
        });
        this.registerBuiltInSkill({
            id: 'calculator',
            name: 'Calculator',
            description: 'Perform mathematical calculations',
            category: 'AUTOMATION',
            version: '1.0.0',
            parameters: z.object({
                expression: z.string().min(1),
                precision: z.number().optional().default(10),
            }),
            returns: z.object({
                result: z.number(),
                expression: z.string(),
                precision: z.number(),
            }),
            implementation: this.calculatorSkill.bind(this),
            permissions: ['math:calculate'],
            tags: ['math', 'calculation'],
            author: 'system',
        });
        this.registerBuiltInSkill({
            id: 'file_processor',
            name: 'File Processor',
            description: 'Process and analyze files',
            category: 'AUTOMATION',
            version: '1.0.0',
            parameters: z.object({
                file_path: z.string(),
                operation: z.enum(['read', 'analyze', 'convert']),
                options: z.object({
                    encoding: z.string().optional().default('utf-8'),
                    max_size: z.number().optional().default(10485760),
                }).optional(),
            }),
            returns: z.object({
                content: z.string().optional(),
                metadata: z.object({
                    size: z.number(),
                    type: z.string(),
                    encoding: z.string(),
                }),
                analysis: z.any().optional(),
            }),
            implementation: this.fileProcessorSkill.bind(this),
            permissions: ['file:process'],
            tags: ['file', 'processing'],
            author: 'system',
        });
        this.logger.log('Initialized built-in skills');
    }
    async registerBuiltInSkill(skill) {
        const fullSkill = {
            ...skill,
            dependencies: [],
            requirements: {},
            documentation: `Built-in skill: ${skill.description}`,
            examples: [],
        };
        await this.registerSkill(fullSkill);
    }
    async textAnalyzerSkill(params, env) {
        const { text, analysis_types } = params;
        const result = {};
        if (analysis_types.includes('sentiment')) {
            result.sentiment = this.analyzeSentiment(text);
        }
        if (analysis_types.includes('entities')) {
            result.entities = this.extractEntities(text);
        }
        if (analysis_types.includes('keywords')) {
            result.keywords = this.extractKeywords(text);
        }
        return result;
    }
    analyzeSentiment(text) {
        const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic'];
        const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing'];
        const words = text.toLowerCase().match(/\b\w+\b/g) || [];
        const positiveCount = words.filter(word => positiveWords.includes(word)).length;
        const negativeCount = words.filter(word => negativeWords.includes(word)).length;
        const score = (positiveCount - negativeCount) / Math.max(words.length, 1);
        let label;
        if (score > 0.1)
            label = 'positive';
        else if (score < -0.1)
            label = 'negative';
        else
            label = 'neutral';
        return { score, label };
    }
    extractEntities(text) {
        const entities = [];
        const namePattern = /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g;
        const nameMatches = text.match(namePattern) || [];
        nameMatches.forEach(match => {
            entities.push({
                text: match,
                type: 'PERSON',
                confidence: 0.8,
            });
        });
        const orgPattern = /\b[A-Z][a-z]+ (Inc|Corp|LLC|Ltd|Company)\b/g;
        const orgMatches = text.match(orgPattern) || [];
        orgMatches.forEach(match => {
            entities.push({
                text: match,
                type: 'ORGANIZATION',
                confidence: 0.7,
            });
        });
        return entities;
    }
    extractKeywords(text) {
        const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
        const words = text.toLowerCase().match(/\b\w+\b/g) || [];
        const wordCount = new Map();
        words.forEach(word => {
            if (!stopWords.has(word) && word.length > 3) {
                wordCount.set(word, (wordCount.get(word) || 0) + 1);
            }
        });
        return Array.from(wordCount.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .map(([word]) => word);
    }
    async webScraperSkill(params, env) {
        const { url, selectors = {}, options = {} } = params;
        try {
            const response = await fetch(url, {
                headers: {
                    'User-Agent': options.user_agent || 'SynapseAI Agent/1.0',
                },
                signal: AbortSignal.timeout(options.timeout || 10000),
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const html = await response.text();
            const result = {
                metadata: {
                    url,
                    timestamp: new Date().toISOString(),
                    status: response.status,
                },
            };
            if (selectors.title) {
                const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
                result.title = titleMatch ? titleMatch[1].trim() : null;
            }
            if (selectors.content) {
                const textContent = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
                result.content = textContent.substring(0, 5000);
            }
            if (selectors.links) {
                const linkMatches = html.match(/href="([^"]+)"/g) || [];
                result.links = linkMatches
                    .map(match => match.match(/href="([^"]+)"/)?.[1])
                    .filter(Boolean)
                    .slice(0, 20);
            }
            return result;
        }
        catch (error) {
            throw new Error(`Web scraping failed: ${error.message}`);
        }
    }
    async calculatorSkill(params, env) {
        const { expression, precision } = params;
        try {
            const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '');
            if (sanitized !== expression) {
                throw new Error('Invalid characters in expression');
            }
            const result = Function(`"use strict"; return (${sanitized})`)();
            if (!Number.isFinite(result)) {
                throw new Error('Result is not a finite number');
            }
            return {
                result: Number(result.toFixed(precision)),
                expression,
                precision,
            };
        }
        catch (error) {
            throw new Error(`Calculation failed: ${error.message}`);
        }
    }
    async fileProcessorSkill(params, env) {
        const { file_path, operation, options = {} } = params;
        throw new Error('File processing skill not implemented in this environment');
    }
    validateSkillDefinition(skill) {
        if (!skill.id || !skill.name || !skill.description) {
            throw new common_1.BadRequestException('Skill must have id, name, and description');
        }
        if (!skill.parameters || !skill.returns) {
            throw new common_1.BadRequestException('Skill must have parameters and returns schemas');
        }
        if (!skill.implementation) {
            throw new common_1.BadRequestException('Skill must have an implementation');
        }
    }
    async validateSkillAccess(agentId, skill) {
        const agent = await this.prisma.agentInstance.findUnique({
            where: { id: agentId },
            select: { skills: true, organizationId: true },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Agent not found');
        }
        if (!agent.skills.includes(skill.id)) {
            throw new common_1.BadRequestException('Agent does not have access to this skill');
        }
    }
    validateParameters(skill, parameters) {
        try {
            return skill.parameters.parse(parameters);
        }
        catch (error) {
            throw new common_1.BadRequestException(`Invalid parameters: ${error.message}`);
        }
    }
    validateResult(skill, result) {
        try {
            return skill.returns.parse(result);
        }
        catch (error) {
            this.logger.warn(`Skill result validation failed for ${skill.id}: ${error.message}`);
            return result;
        }
    }
    async checkRateLimits(agentId, skillId, skill) {
        if (!skill.rateLimits) {
            return;
        }
        const now = Date.now();
        const minuteKey = `rate_limit:${agentId}:${skillId}:${Math.floor(now / 60000)}`;
        const hourKey = `rate_limit:${agentId}:${skillId}:${Math.floor(now / 3600000)}`;
        const [minuteCount, hourCount] = await Promise.all([
            this.cacheManager.get(minuteKey),
            this.cacheManager.get(hourKey),
        ]);
        if ((minuteCount || 0) >= skill.rateLimits.requestsPerMinute) {
            throw new common_1.BadRequestException('Rate limit exceeded: too many requests per minute');
        }
        if ((hourCount || 0) >= skill.rateLimits.requestsPerHour) {
            throw new common_1.BadRequestException('Rate limit exceeded: too many requests per hour');
        }
        await Promise.all([
            this.cacheManager.set(minuteKey, (minuteCount || 0) + 1, 60000),
            this.cacheManager.set(hourKey, (hourCount || 0) + 1, 3600000),
        ]);
    }
    async trackSkillExecution(agentId, skillId, duration, success) {
        const statsKey = `skill_stats:${agentId}:${skillId}`;
        const stats = await this.cacheManager.get(statsKey) || {
            totalExecutions: 0,
            successfulExecutions: 0,
            totalDuration: 0,
            averageDuration: 0,
            lastExecution: null,
        };
        stats.totalExecutions++;
        if (success)
            stats.successfulExecutions++;
        stats.totalDuration += duration;
        stats.averageDuration = stats.totalDuration / stats.totalExecutions;
        stats.lastExecution = new Date().toISOString();
        await this.cacheManager.set(statsKey, stats, 86400000);
    }
    zodSchemaToOpenAPISchema(schema) {
        if (schema instanceof z.ZodObject) {
            const properties = {};
            const shape = schema.shape;
            const required = [];
            for (const [key, value] of Object.entries(shape)) {
                properties[key] = this.zodSchemaToOpenAPISchema(value);
                if (!value.isOptional()) {
                    required.push(key);
                }
            }
            return {
                type: 'object',
                properties,
                required: required.length > 0 ? required : undefined,
            };
        }
        else if (schema instanceof z.ZodString) {
            return { type: 'string' };
        }
        else if (schema instanceof z.ZodNumber) {
            return { type: 'number' };
        }
        else if (schema instanceof z.ZodBoolean) {
            return { type: 'boolean' };
        }
        else if (schema instanceof z.ZodArray) {
            return {
                type: 'array',
                items: this.zodSchemaToOpenAPISchema(schema._def.type),
            };
        }
        else if (schema instanceof z.ZodEnum) {
            return {
                type: 'string',
                enum: schema.options,
            };
        }
        return { type: 'any' };
    }
    async executeSandboxedCode(code, parameters, env) {
        throw new Error('Sandboxed code execution not implemented');
    }
    async getAvailableSkills(category) {
        const skills = Array.from(this.skillRegistry.values());
        if (category) {
            return skills.filter(skill => skill.category === category);
        }
        return skills;
    }
    async getSkillCategories() {
        const skills = Array.from(this.skillRegistry.values());
        const categories = new Set(skills.map(skill => skill.category));
        return Array.from(categories);
    }
    async getSkillStats(agentId, skillId) {
        const statsKey = `skill_stats:${agentId}:${skillId}`;
        return this.cacheManager.get(statsKey) || null;
    }
};
exports.SkillExecutorService = SkillExecutorService;
exports.SkillExecutorService = SkillExecutorService = SkillExecutorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, config_1.ConfigService,
        event_emitter_mock_1.MockEventEmitter])
], SkillExecutorService);
//# sourceMappingURL=skill-executor.service.js.map