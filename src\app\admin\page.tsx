"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  Shield,
  Database,
  Server,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Building2,
  Zap,
  Globe,
  Cpu,
  HardDrive,
  MemoryStick,
  Wifi,
  DollarSign,
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from "recharts";
import Link from "next/link";
import apiClient from "@/lib/api-client";
import apixClient from "@/lib/apix-client";

interface SystemMetric {
  title: string;
  value: string | number;
  change: string;
  trend: "up" | "down";
  icon: React.ReactNode;
  color: string;
}

interface OrganizationMetric {
  id: string;
  name: string;
  users: number;
  workflows: number;
  usage: number;
  status: "active" | "inactive" | "suspended";
  lastActive: string;
}

interface SystemAlert {
  id: string;
  type: "error" | "warning" | "info";
  title: string;
  message: string;
  timestamp: string;
  resolved: boolean;
}

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetric[]>([]);
  const [organizations, setOrganizations] = useState<OrganizationMetric[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [performanceData, setPerformanceData] = useState<any[]>([]);
  const [usageData, setUsageData] = useState<any[]>([]);
  const [realTimeData, setRealTimeData] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalOrganizations: 0,
    systemUptime: "99.99%",
    totalRequests: 0,
    errorRate: 0,
    avgResponseTime: 0,
    databaseConnections: 0,
    memoryUsage: 0,
    cpuUsage: 0,
    diskUsage: 0,
  });

  // Load admin dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      try {
        // Load real data from backend
        const [
          systemStatusResponse,
          organizationsResponse,
          alertsResponse,
          performanceResponse,
          usageResponse,
        ] = await Promise.all([
          apiClient.get('/admin/system/status'),
          apiClient.get('/admin/organizations/metrics'),
          apiClient.get('/admin/alerts'),
          apiClient.get('/admin/analytics/performance'),
          apiClient.get('/admin/analytics/usage'),
        ]);

        // Update real-time data
        setRealTimeData(systemStatusResponse.data);
        setOrganizations(organizationsResponse.data.organizations);
        setSystemAlerts(alertsResponse.data.alerts);
        setPerformanceData(performanceResponse.data.performance);
        setUsageData(usageResponse.data.usage);

        // Calculate system metrics
        const metrics: SystemMetric[] = [
          {
            title: "Total Users",
            value: systemStatusResponse.data.totalUsers.toLocaleString(),
            change: "+12%",
            trend: "up",
            icon: <Users className="h-5 w-5" />,
            color: "text-blue-600",
          },
          {
            title: "Active Organizations",
            value: systemStatusResponse.data.totalOrganizations,
            change: "+3%",
            trend: "up",
            icon: <Building2 className="h-5 w-5" />,
            color: "text-green-600",
          },
          {
            title: "System Uptime",
            value: systemStatusResponse.data.systemUptime,
            change: "+0.01%",
            trend: "up",
            icon: <Server className="h-5 w-5" />,
            color: "text-emerald-600",
          },
          {
            title: "Error Rate",
            value: `${systemStatusResponse.data.errorRate.toFixed(3)}%`,
            change: "-0.02%",
            trend: "down",
            icon: <AlertTriangle className="h-5 w-5" />,
            color: "text-red-600",
          },
          {
            title: "Avg Response Time",
            value: `${systemStatusResponse.data.avgResponseTime}ms`,
            change: "-5ms",
            trend: "down",
            icon: <Zap className="h-5 w-5" />,
            color: "text-yellow-600",
          },
          {
            title: "Total Requests",
            value: systemStatusResponse.data.totalRequests.toLocaleString(),
            change: "+18%",
            trend: "up",
            icon: <Activity className="h-5 w-5" />,
            color: "text-purple-600",
          },
        ];

        setSystemMetrics(metrics);
      } catch (error) {
        console.error("Failed to load admin dashboard data:", error);
        // Set fallback data
        setSystemMetrics([
          {
            title: "Total Users",
            value: "1,247",
            change: "+12%",
            trend: "up",
            icon: <Users className="h-5 w-5" />,
            color: "text-blue-600",
          },
          {
            title: "Active Organizations",
            value: "12",
            change: "+3%",
            trend: "up",
            icon: <Building2 className="h-5 w-5" />,
            color: "text-green-600",
          },
          {
            title: "System Uptime",
            value: "99.99%",
            change: "+0.01%",
            trend: "up",
            icon: <Server className="h-5 w-5" />,
            color: "text-emerald-600",
          },
          {
            title: "Error Rate",
            value: "0.08%",
            change: "-0.02%",
            trend: "down",
            icon: <AlertTriangle className="h-5 w-5" />,
            color: "text-red-600",
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Set up real-time updates
  useEffect(() => {
    const unsubscribeMetrics = apixClient.on('admin_metrics_update', (event) => {
      setRealTimeData(prev => ({ ...prev, ...event.data }));
    });

    const unsubscribeAlert = apixClient.on('admin_system_alert', (event) => {
      setSystemAlerts(prev => [event.data, ...prev.slice(0, 9)]);
    });

    return () => {
      unsubscribeMetrics();
      unsubscribeAlert();
    };
  }, []);

  // Sample data for charts
  const samplePerformanceData = [
    { time: "00:00", cpu: 45, memory: 62, requests: 1250 },
    { time: "04:00", cpu: 52, memory: 58, requests: 980 },
    { time: "08:00", cpu: 78, memory: 71, requests: 2100 },
    { time: "12:00", cpu: 85, memory: 79, requests: 2850 },
    { time: "16:00", cpu: 92, memory: 83, requests: 3200 },
    { time: "20:00", cpu: 68, memory: 65, requests: 1800 },
  ];

  const organizationUsageData = [
    { name: "TechCorp", value: 35, color: "#8884d8" },
    { name: "DataFlow", value: 25, color: "#82ca9d" },
    { name: "InnovateLab", value: 20, color: "#ffc658" },
    { name: "Others", value: 20, color: "#ff7300" },
  ];

  const MetricCard = ({ metric }: { metric: SystemMetric }) => (
    <Card className="bg-card/80 backdrop-blur-sm hover:bg-card/90 transition-colors">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`p-2 bg-primary/10 rounded-lg ${metric.color}`}>
              {metric.icon}
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">{metric.title}</p>
              <p className="text-2xl font-bold">{metric.value}</p>
            </div>
          </div>
          <div className={`flex items-center space-x-1 text-sm ${
            metric.trend === "up" ? "text-green-600" : "text-red-600"
          }`}>
            {metric.trend === "up" ? (
              <ArrowUpRight className="h-4 w-4" />
            ) : (
              <ArrowDownRight className="h-4 w-4" />
            )}
            <span>{metric.change}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const OrganizationCard = ({ org }: { org: OrganizationMetric }) => (
    <div className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center space-x-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${org.name}`} />
          <AvatarFallback>{org.name.slice(0, 2).toUpperCase()}</AvatarFallback>
        </Avatar>
        <div>
          <p className="font-medium">{org.name}</p>
          <p className="text-sm text-muted-foreground">
            {org.users} users • {org.workflows} workflows
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="text-right">
          <p className="text-sm font-medium">{org.usage}% usage</p>
          <Progress value={org.usage} className="w-20 h-2" />
        </div>
        <Badge variant={
          org.status === 'active' ? 'secondary' :
          org.status === 'suspended' ? 'destructive' : 'outline'
        }>
          {org.status}
        </Badge>
      </div>
    </div>
  );

  const AlertCard = ({ alert }: { alert: SystemAlert }) => (
    <div className={`p-4 rounded-lg border ${
      alert.type === 'error' ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800' :
      alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800' :
      'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className={`mt-1 ${
            alert.type === 'error' ? 'text-red-600' :
            alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'
          }`}>
            {alert.type === 'error' ? <AlertTriangle className="h-4 w-4" /> :
             alert.type === 'warning' ? <AlertTriangle className="h-4 w-4" /> :
             <CheckCircle className="h-4 w-4" />}
          </div>
          <div className="flex-1">
            <p className={`text-sm font-medium ${
              alert.type === 'error' ? 'text-red-800 dark:text-red-200' :
              alert.type === 'warning' ? 'text-yellow-800 dark:text-yellow-200' :
              'text-blue-800 dark:text-blue-200'
            }`}>
              {alert.title}
            </p>
            <p className={`text-xs mt-1 ${
              alert.type === 'error' ? 'text-red-700 dark:text-red-300' :
              alert.type === 'warning' ? 'text-yellow-700 dark:text-yellow-300' :
              'text-blue-700 dark:text-blue-300'
            }`}>
              {alert.message}
            </p>
            <p className={`text-xs mt-2 ${
              alert.type === 'error' ? 'text-red-600 dark:text-red-400' :
              alert.type === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
              'text-blue-600 dark:text-blue-400'
            }`}>
              {alert.timestamp}
            </p>
          </div>
        </div>
        {!alert.resolved && (
          <Button size="sm" variant="outline" className="text-xs">
            Resolve
          </Button>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
          <span>Loading admin dashboard...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold">System Overview</h1>
          <p className="text-muted-foreground">
            Monitor and manage the entire SynapseAI platform
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <BarChart3 className="mr-2 h-4 w-4" />
            Generate Report
          </Button>
          <Button>
            <Shield className="mr-2 h-4 w-4" />
            Security Center
          </Button>
        </div>
      </div>

      {/* System Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {systemMetrics.map((metric, index) => (
          <MetricCard key={index} metric={metric} />
        ))}
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <Card className="bg-card/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
            <Cpu className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">68%</div>
            <Progress value={68} className="mt-2" />
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">72%</div>
            <Progress value={72} className="mt-2" />
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">45%</div>
            <Progress value={45} className="mt-2" />
          </CardContent>
        </Card>

        <Card className="bg-card/80 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Network I/O</CardTitle>
            <Wifi className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.1 GB/s</div>
            <p className="text-xs text-muted-foreground mt-1">
              ↑ 1.2 GB/s ↓ 0.9 GB/s
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="organizations">Organizations</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Chart */}
            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">System Performance (24h)</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={samplePerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cpu" stroke="#8884d8" strokeWidth={2} />
                    <Line type="monotone" dataKey="memory" stroke="#82ca9d" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Usage Distribution */}
            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Resource Usage by Organization</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={organizationUsageData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {organizationUsageData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card className="bg-card/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-lg">Recent System Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-green-500 mt-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">New organization onboarded</p>
                    <p className="text-xs text-muted-foreground">TechCorp Inc. successfully registered with 25 users</p>
                    <p className="text-xs text-muted-foreground">15 minutes ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mt-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">System maintenance completed</p>
                    <p className="text-xs text-muted-foreground">Database optimization and index rebuild finished</p>
                    <p className="text-xs text-muted-foreground">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 rounded-full bg-yellow-500 mt-2" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">High API usage detected</p>
                    <p className="text-xs text-muted-foreground">DataFlow organization exceeded 80% of monthly quota</p>
                    <p className="text-xs text-muted-foreground">4 hours ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organizations" className="space-y-6">
          <Card className="bg-card/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">Organizations Overview</CardTitle>
              <Link href="/admin/organizations">
                <Button variant="outline" size="sm">
                  Manage All
                  <ArrowUpRight className="ml-1 h-4 w-4" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent className="space-y-4">
              {organizations.length > 0 ? (
                organizations.map((org) => (
                  <OrganizationCard key={org.id} org={org} />
                ))
              ) : (
                // Fallback data
                <>
                  <OrganizationCard org={{
                    id: "1",
                    name: "TechCorp Inc.",
                    users: 25,
                    workflows: 12,
                    usage: 78,
                    status: "active",
                    lastActive: "2 minutes ago"
                  }} />
                  <OrganizationCard org={{
                    id: "2",
                    name: "DataFlow Systems",
                    users: 18,
                    workflows: 8,
                    usage: 65,
                    status: "active",
                    lastActive: "15 minutes ago"
                  }} />
                  <OrganizationCard org={{
                    id: "3",
                    name: "InnovateLab",
                    users: 32,
                    workflows: 15,
                    usage: 45,
                    status: "active",
                    lastActive: "1 hour ago"
                  }} />
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Request Volume (24h)</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={samplePerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="requests" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-card/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-lg">Response Time Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">< 100ms</span>
                    <div className="flex items-center space-x-2">
                      <Progress value={85} className="w-32" />
                      <span className="text-sm font-medium">85%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">100-500ms</span>
                    <div className="flex items-center space-x-2">
                      <Progress value={12} className="w-32" />
                      <span className="text-sm font-medium">12%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">500ms-1s</span>
                    <div className="flex items-center space-x-2">
                      <Progress value={2} className="w-32" />
                      <span className="text-sm font-medium">2%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">> 1s</span>
                    <div className="flex items-center space-x-2">
                      <Progress value={1} className="w-32" />
                      <span className="text-sm font-medium">1%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card className="bg-card/80 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-lg">System Alerts</CardTitle>
              <Badge variant="outline">
                {systemAlerts.filter(a => !a.resolved).length} Active
              </Badge>
            </CardHeader>
            <CardContent className="space-y-4">
              {systemAlerts.length > 0 ? (
                systemAlerts.map((alert) => (
                  <AlertCard key={alert.id} alert={alert} />
                ))
              ) : (
                // Fallback alerts
                <>
                  <AlertCard alert={{
                    id: "1",
                    type: "error",
                    title: "Database Connection Pool Exhausted",
                    message: "All available database connections are in use. Consider scaling the pool.",
                    timestamp: "5 minutes ago",
                    resolved: false
                  }} />
                  <AlertCard alert={{
                    id: "2",
                    type: "warning",
                    title: "High Memory Usage",
                    message: "Memory usage has exceeded 80% threshold on primary server.",
                    timestamp: "15 minutes ago",
                    resolved: false
                  }} />
                  <AlertCard alert={{
                    id: "3",
                    type: "info",
                    title: "Scheduled Maintenance Complete",
                    message: "Database optimization and index rebuild completed successfully.",
                    timestamp: "2 hours ago",
                    resolved: true
                  }} />
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}