import { NextRequest, NextResponse } from "next/server";

/**
 * 🔐 Frontend Login API Proxy
 * 
 * This route proxies authentication requests to the backend API
 * and handles proper token management with httpOnly cookies.
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, organizationSlug, mfaCode, rememberMe } = body;

    if (!email || !password) {
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }

    // Forward request to backend
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    const response = await fetch(`${backendUrl}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Forwarded-For': request.ip || 'unknown',
        'User-Agent': request.headers.get('user-agent') || 'unknown',
      },
      body: JSON.stringify({
        email,
        password,
        organizationSlug,
        mfaCode,
        rememberMe,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    // Create response with secure cookies
    const nextResponse = NextResponse.json({
      user: data.user,
      expiresAt: data.expiresAt,
      session: data.session,
    });

    // Set secure httpOnly cookies for tokens
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
    };

    // Access token (short-lived)
    nextResponse.cookies.set('access_token', data.accessToken, {
      ...cookieOptions,
      maxAge: 15 * 60, // 15 minutes
    });

    // Refresh token (longer-lived)
    nextResponse.cookies.set('refresh_token', data.refreshToken, {
      ...cookieOptions,
      maxAge: rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60, // 30 days or 7 days
    });

    return nextResponse;

  } catch (error) {
    console.error("Login proxy error:", error);
    return NextResponse.json(
      { message: "Authentication service unavailable" },
      { status: 503 }
    );
  }
}