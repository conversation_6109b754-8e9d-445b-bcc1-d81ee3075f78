import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { MockEventEmitter } from '../mocks/event-emitter.mock';
export interface SessionMemoryConfig {
    type: string;
    systemPrompt?: string;
    instructions?: string;
    capabilities?: any;
    maxMemorySize?: number;
    memoryWindow?: number;
    pruningStrategy?: 'fifo' | 'lifo' | 'importance' | 'sliding_window';
}
export interface MessageData {
    role: string;
    content: string;
    type?: string;
    metadata?: any;
    tokens?: number;
    cost?: number;
    timestamp?: Date;
    importance?: number;
    embedding?: number[];
}
export interface ConversationSummary {
    summary: string;
    keyPoints: string[];
    entities: any[];
    sentiment?: string;
    topics?: string[];
    messageCount: number;
    tokenCount: number;
    timespan: {
        start: Date;
        end: Date;
    };
}
export interface MemoryMetrics {
    totalMessages: number;
    totalTokens: number;
    memorySize: number;
    compressionRatio: number;
    lastActivity: Date;
    averageMessageLength: number;
    topTopics: string[];
    sentimentTrend: number[];
}
export declare class SessionMemoryService {
    private prisma;
    private cacheManager;
    private configService;
    private eventEmitter;
    private readonly logger;
    private readonly maxMemorySize;
    private readonly defaultWindow;
    private readonly compressionThreshold;
    constructor(prisma: PrismaService, cacheManager: Cache, configService: ConfigService, eventEmitter: MockEventEmitter);
    initializeAgentMemory(agentId: string, config: SessionMemoryConfig): Promise<void>;
    updateAgentMemory(agentId: string, updates: Partial<SessionMemoryConfig>): Promise<void>;
    clearAgentMemory(agentId: string): Promise<void>;
    getOrCreateSession(agentId: string, sessionId: string, userId?: string): Promise<any>;
    initializeSessionMemory(sessionId: string, config: {
        agentId: string;
        userId?: string;
        strategy: string;
        maxMessages: number;
    }): Promise<void>;
    updateSessionMemory(sessionId: string, updates: any): Promise<void>;
    addMessage(sessionId: string, messageData: MessageData): Promise<void>;
    getConversationHistory(sessionId: string, limit?: number, includeSystem?: boolean): Promise<any[]>;
    getMessagesByRole(sessionId: string, role: string): Promise<any[]>;
    pruneMemory(sessionId: string, memory: any): Promise<void>;
    private applySlidingWindowPruning;
    private applyImportancePruning;
    private applyFIFOPruning;
    private applyCompressionPruning;
    summarizeMessages(messages: any[]): Promise<ConversationSummary>;
    private generateBasicSummary;
    private extractKeyPoints;
    private extractEntities;
    private extractTopics;
    private mergeSummaries;
    private calculateMessageImportance;
    private calculateMemorySize;
    getMemoryMetrics(sessionId: string): Promise<MemoryMetrics>;
    searchMemory(sessionId: string, query: string, options?: {
        role?: string;
        timeRange?: {
            start: Date;
            end: Date;
        };
        limit?: number;
    }): Promise<any[]>;
}
