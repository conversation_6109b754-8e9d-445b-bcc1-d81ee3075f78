import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { CheckCircle, XCircle, AlertTriangle, <PERSON>ch, Settings, Code, Database } from 'lucide-react';
import apiClient from '@/lib/api-client';
import { useToast } from '@/components/ui/use-toast';

const toolConfigSchema = z.object({
  toolId: z.string().min(1, 'Tool selection is required'),
  parameters: z.record(z.string(), z.any()),
  timeout: z.number().min(1000).max(300000),
  retryPolicy: z.object({
    enabled: z.boolean(),
    maxRetries: z.number().min(0).max(5),
    backoffStrategy: z.enum(['linear', 'exponential']),
    retryDelay: z.number().min(100).max(10000),
  }),
  errorHandling: z.object({
    onError: z.enum(['stop', 'continue', 'retry']),
    fallbackValue: z.any().optional(),
  }),
  inputMapping: z.record(z.string(), z.string()),
  outputMapping: z.record(z.string(), z.string()),
  validation: z.object({
    validateInput: z.boolean(),
    validateOutput: z.boolean(),
    schema: z.string().optional(),
  }),
});

type ToolConfigFormData = z.infer<typeof toolConfigSchema>;

interface ToolNodeConfigPanelProps {
  nodeId: string;
  initialConfig?: Partial<ToolConfigFormData>;
  onConfigChange: (config: ToolConfigFormData) => void;
  onValidationChange: (isValid: boolean) => void;
}

interface Tool {
  id: string;
  name: string;
  type: string;
  description?: string;
  inputSchema?: any;
  outputSchema?: any;
  parameters?: any;
}

export default function ToolNodeConfigPanel({
  nodeId,
  initialConfig = {},
  onConfigChange,
  onValidationChange,
}: ToolNodeConfigPanelProps) {
  const [tools, setTools] = useState<Tool[]>([]);
  const [selectedTool, setSelectedTool] = useState<Tool | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    trigger,
  } = useForm<ToolConfigFormData>({
    resolver: zodResolver(toolConfigSchema),
    defaultValues: {
      toolId: '',
      parameters: {},
      timeout: 30000,
      retryPolicy: {
        enabled: true,
        maxRetries: 3,
        backoffStrategy: 'exponential',
        retryDelay: 1000,
      },
      errorHandling: {
        onError: 'stop',
      },
      inputMapping: {},
      outputMapping: {},
      validation: {
        validateInput: true,
        validateOutput: true,
      },
      ...initialConfig,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();
  const selectedToolId = watch('toolId');

  // Load available tools
  useEffect(() => {
    const loadTools = async () => {
      try {
        setIsLoading(true);
        const response = await apiClient.getTools();
        setTools(response.tools || []);
      } catch (error) {
        console.error('Failed to load tools:', error);
        toast({
          title: 'Error',
          description: 'Failed to load available tools',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadTools();
  }, [toast]);

  // Update selected tool when toolId changes
  useEffect(() => {
    if (selectedToolId) {
      const tool = tools.find(t => t.id === selectedToolId);
      setSelectedTool(tool || null);

      // Auto-populate parameters based on tool schema
      if (tool?.parameters) {
        setValue('parameters', tool.parameters);
      }
    }
  }, [selectedToolId, tools, setValue]);

  // Debounced config change handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isValid) {
        onConfigChange(watchedValues);
        setValidationErrors([]);
      } else {
        const errorMessages = Object.values(errors).map(error => error.message).filter(Boolean) as string[];
        setValidationErrors(errorMessages);
      }
      onValidationChange(isValid);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isValid, errors, onConfigChange, onValidationChange]);

  const handleParameterChange = (key: string, value: any) => {
    const currentParams = watch('parameters') || {};
    setValue('parameters', { ...currentParams, [key]: value });
    trigger('parameters');
  };

  const handleInputMappingChange = (key: string, value: string) => {
    const currentMapping = watch('inputMapping') || {};
    setValue('inputMapping', { ...currentMapping, [key]: value });
    trigger('inputMapping');
  };

  const handleOutputMappingChange = (key: string, value: string) => {
    const currentMapping = watch('outputMapping') || {};
    setValue('outputMapping', { ...currentMapping, [key]: value });
    trigger('outputMapping');
  };

  const getToolIcon = (toolType: string) => {
    switch (toolType) {
      case 'DATABASE':
        return <Database className="h-4 w-4" />;
      case 'API_FETCH':
        return <Code className="h-4 w-4" />;
      case 'FUNCTION_CALL':
        return <Settings className="h-4 w-4" />;
      default:
        return <Wrench className="h-4 w-4" />;
    }
  };

  const renderParameterInput = (paramKey: string, paramConfig: any) => {
    const currentValue = watch('parameters')?.[paramKey];

    switch (paramConfig.type) {
      case 'string':
        return (
          <Input
            value={currentValue || ''}
            onChange={(e) => handleParameterChange(paramKey, e.target.value)}
            placeholder={paramConfig.placeholder || `Enter ${paramKey}`}
          />
        );
      case 'number':
        return (
          <Input
            type="number"
            value={currentValue || ''}
            onChange={(e) => handleParameterChange(paramKey, parseFloat(e.target.value))}
            placeholder={paramConfig.placeholder || `Enter ${paramKey}`}
            min={paramConfig.min}
            max={paramConfig.max}
          />
        );
      case 'boolean':
        return (
          <Switch
            checked={currentValue || false}
            onCheckedChange={(checked) => handleParameterChange(paramKey, checked)}
          />
        );
      case 'select':
        return (
          <Select
            value={currentValue || ''}
            onValueChange={(value) => handleParameterChange(paramKey, value)}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Select ${paramKey}`} />
            </SelectTrigger>
            <SelectContent>
              {paramConfig.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case 'textarea':
        return (
          <Textarea
            value={currentValue || ''}
            onChange={(e) => handleParameterChange(paramKey, e.target.value)}
            placeholder={paramConfig.placeholder || `Enter ${paramKey}`}
            rows={3}
          />
        );
      default:
        return (
          <Input
            value={currentValue || ''}
            onChange={(e) => handleParameterChange(paramKey, e.target.value)}
            placeholder={paramConfig.placeholder || `Enter ${paramKey}`}
          />
        );
    }
  };

  return (
    <div className="bg-background min-h-screen p-8">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader className="flex flex-row items-center space-y-0 pb-4">
          <div className="flex items-center space-x-2">
            <Wrench className="h-5 w-5" />
            <CardTitle>Tool Node Configuration</CardTitle>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            {isValid ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Valid
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Invalid
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent>
          {validationErrors.length > 0 && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="parameters">Parameters</TabsTrigger>
              <TabsTrigger value="mapping">I/O Mapping</TabsTrigger>
              <TabsTrigger value="error">Error Handling</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="toolId">Tool Selection *</Label>
                  <Select
                    value={watch('toolId')}
                    onValueChange={(value) => setValue('toolId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a tool" />
                    </SelectTrigger>
                    <SelectContent>
                      {tools.map((tool) => (
                        <SelectItem key={tool.id} value={tool.id}>
                          <div className="flex items-center space-x-2">
                            {getToolIcon(tool.type)}
                            <span>{tool.name}</span>
                            <Badge variant="outline" className="ml-2">
                              {tool.type}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.toolId && (
                    <p className="text-sm text-red-600">{errors.toolId.message}</p>
                  )}
                </div>

                {selectedTool && (
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Tool Information</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      {selectedTool.description || 'No description available'}
                    </p>
                    <div className="flex items-center space-x-4 text-xs">
                      <span>Type: {selectedTool.type}</span>
                      <span>ID: {selectedTool.id}</span>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="timeout">Timeout (ms)</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[watch('timeout') || 30000]}
                      onValueChange={([value]) => setValue('timeout', value)}
                      min={1000}
                      max={300000}
                      step={1000}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>1s</span>
                      <span>{Math.round((watch('timeout') || 30000) / 1000)}s</span>
                      <span>5m</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="parameters" className="space-y-6">
              {selectedTool?.parameters ? (
                <div className="space-y-4">
                  <h4 className="font-medium">Tool Parameters</h4>
                  {Object.entries(selectedTool.parameters).map(([key, config]: [string, any]) => (
                    <div key={key} className="space-y-2">
                      <Label htmlFor={key}>
                        {config.label || key}
                        {config.required && <span className="text-red-500 ml-1">*</span>}
                      </Label>
                      {config.description && (
                        <p className="text-xs text-muted-foreground">{config.description}</p>
                      )}
                      {renderParameterInput(key, config)}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  <h4 className="font-medium">Custom Parameters</h4>
                  <div className="space-y-2">
                    <Label htmlFor="parameters">Parameters (JSON)</Label>
                    <Textarea
                      value={JSON.stringify(watch('parameters') || {}, null, 2)}
                      onChange={(e) => {
                        try {
                          const parsed = JSON.parse(e.target.value);
                          setValue('parameters', parsed);
                          trigger('parameters');
                        } catch (error) {
                          // Invalid JSON, don't update
                        }
                      }}
                      rows={8}
                      className="font-mono text-sm"
                      placeholder='{\n  "key": "value"\n}'
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="mapping" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Input Mapping</h4>
                  <p className="text-sm text-muted-foreground">
                    Map workflow variables to tool inputs
                  </p>

                  {selectedTool?.inputSchema ? (
                    <div className="space-y-3">
                      {Object.keys(selectedTool.inputSchema.properties || {}).map((inputKey) => (
                        <div key={inputKey} className="space-y-2">
                          <Label htmlFor={`input-${inputKey}`}>{inputKey}</Label>
                          <Input
                            id={`input-${inputKey}`}
                            value={watch('inputMapping')?.[inputKey] || ''}
                            onChange={(e) => handleInputMappingChange(inputKey, e.target.value)}
                            placeholder={`$\{variable_name\} or static value`}
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label htmlFor="inputMapping">Input Mapping (JSON)</Label>
                      <Textarea
                        value={JSON.stringify(watch('inputMapping') || {}, null, 2)}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value);
                            setValue('inputMapping', parsed);
                            trigger('inputMapping');
                          } catch (error) {
                            // Invalid JSON, don't update
                          }
                        }}
                        rows={4}
                        className="font-mono text-sm"
                        placeholder='{\n  "input_key": "${workflow_variable}"\n}'
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Output Mapping</h4>
                  <p className="text-sm text-muted-foreground">
                    Map tool outputs to workflow variables
                  </p>

                  {selectedTool?.outputSchema ? (
                    <div className="space-y-3">
                      {Object.keys(selectedTool.outputSchema.properties || {}).map((outputKey) => (
                        <div key={outputKey} className="space-y-2">
                          <Label htmlFor={`output-${outputKey}`}>{outputKey}</Label>
                          <Input
                            id={`output-${outputKey}`}
                            value={watch('outputMapping')?.[outputKey] || ''}
                            onChange={(e) => handleOutputMappingChange(outputKey, e.target.value)}
                            placeholder="variable_name"
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Label htmlFor="outputMapping">Output Mapping (JSON)</Label>
                      <Textarea
                        value={JSON.stringify(watch('outputMapping') || {}, null, 2)}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value);
                            setValue('outputMapping', parsed);
                            trigger('outputMapping');
                          } catch (error) {
                            // Invalid JSON, don't update
                          }
                        }}
                        rows={4}
                        className="font-mono text-sm"
                        placeholder='{\n  "result": "tool_result"\n}'
                      />
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="error" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Retry Policy</h4>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('retryPolicy.enabled')}
                      onCheckedChange={(checked) => setValue('retryPolicy.enabled', checked)}
                    />
                    <Label>Enable retry on failure</Label>
                  </div>

                  {watch('retryPolicy.enabled') && (
                    <div className="space-y-4 pl-6 border-l-2 border-muted">
                      <div className="space-y-2">
                        <Label>Max Retries</Label>
                        <Slider
                          value={[watch('retryPolicy.maxRetries') || 3]}
                          onValueChange={([value]) => setValue('retryPolicy.maxRetries', value)}
                          min={0}
                          max={5}
                          step={1}
                          className="w-full"
                        />
                        <div className="text-xs text-muted-foreground">
                          {watch('retryPolicy.maxRetries')} retries
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Backoff Strategy</Label>
                        <Select
                          value={watch('retryPolicy.backoffStrategy')}
                          onValueChange={(value: 'linear' | 'exponential') =>
                            setValue('retryPolicy.backoffStrategy', value)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="linear">Linear</SelectItem>
                            <SelectItem value="exponential">Exponential</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Retry Delay (ms)</Label>
                        <Slider
                          value={[watch('retryPolicy.retryDelay') || 1000]}
                          onValueChange={([value]) => setValue('retryPolicy.retryDelay', value)}
                          min={100}
                          max={10000}
                          step={100}
                          className="w-full"
                        />
                        <div className="text-xs text-muted-foreground">
                          {watch('retryPolicy.retryDelay')}ms initial delay
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Error Handling</h4>

                  <div className="space-y-2">
                    <Label>On Error</Label>
                    <Select
                      value={watch('errorHandling.onError')}
                      onValueChange={(value: 'stop' | 'continue' | 'retry') =>
                        setValue('errorHandling.onError', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="stop">Stop workflow</SelectItem>
                        <SelectItem value="continue">Continue with fallback</SelectItem>
                        <SelectItem value="retry">Retry operation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {watch('errorHandling.onError') === 'continue' && (
                    <div className="space-y-2">
                      <Label>Fallback Value (JSON)</Label>
                      <Textarea
                        value={JSON.stringify(watch('errorHandling.fallbackValue') || null, null, 2)}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value);
                            setValue('errorHandling.fallbackValue', parsed);
                          } catch (error) {
                            // Invalid JSON, don't update
                          }
                        }}
                        rows={3}
                        className="font-mono text-sm"
                        placeholder='null'
                      />
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Validation</h4>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={watch('validation.validateInput')}
                        onCheckedChange={(checked) => setValue('validation.validateInput', checked)}
                      />
                      <Label>Validate input before execution</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={watch('validation.validateOutput')}
                        onCheckedChange={(checked) => setValue('validation.validateOutput', checked)}
                      />
                      <Label>Validate output after execution</Label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Custom Validation Schema (JSON Schema)</Label>
                    <Textarea
                      value={watch('validation.schema') || ''}
                      onChange={(e) => setValue('validation.schema', e.target.value)}
                      rows={6}
                      className="font-mono text-sm"
                      placeholder='{\n  "type": "object",\n  "properties": {\n    "result": { "type": "string" }\n  }\n}'
                    />
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}