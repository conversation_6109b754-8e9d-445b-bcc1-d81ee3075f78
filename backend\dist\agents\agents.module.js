"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsModule = void 0;
const common_1 = require("@nestjs/common");
const cache_manager_1 = require("@nestjs/cache-manager");
const config_1 = require("@nestjs/config");
const agents_service_1 = require("./agents.service");
const agents_controller_1 = require("./agents.controller");
const prisma_module_1 = require("../prisma/prisma.module");
const auth_module_1 = require("../auth/auth.module");
const apix_module_1 = require("../apix/apix.module");
const agent_orchestrator_service_1 = require("./services/agent-orchestrator.service");
const session_memory_service_1 = require("./services/session-memory.service");
const skill_executor_service_1 = require("./services/skill-executor.service");
const provider_router_service_1 = require("./services/provider-router.service");
const task_tracker_service_1 = require("./services/task-tracker.service");
const event_emitter_mock_1 = require("./mocks/event-emitter.mock");
let AgentsModule = class AgentsModule {
};
exports.AgentsModule = AgentsModule;
exports.AgentsModule = AgentsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            prisma_module_1.PrismaModule,
            auth_module_1.AuthModule,
            apix_module_1.ApixModule,
            cache_manager_1.CacheModule.register(),
            config_1.ConfigModule,
        ],
        controllers: [agents_controller_1.AgentsController],
        providers: [
            agents_service_1.AgentsService,
            agent_orchestrator_service_1.AgentOrchestratorService,
            session_memory_service_1.SessionMemoryService,
            skill_executor_service_1.SkillExecutorService,
            provider_router_service_1.ProviderRouterService,
            task_tracker_service_1.TaskTrackerService,
            event_emitter_mock_1.MockEventEmitter,
        ],
        exports: [
            agents_service_1.AgentsService,
            agent_orchestrator_service_1.AgentOrchestratorService,
            session_memory_service_1.SessionMemoryService,
            skill_executor_service_1.SkillExecutorService,
            provider_router_service_1.ProviderRouterService,
            task_tracker_service_1.TaskTrackerService,
        ],
    })
], AgentsModule);
//# sourceMappingURL=agents.module.js.map