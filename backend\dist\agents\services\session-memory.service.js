"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SessionMemoryService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionMemoryService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_mock_1 = require("../mocks/event-emitter.mock");
let SessionMemoryService = SessionMemoryService_1 = class SessionMemoryService {
    constructor(prisma, cacheManager, configService, eventEmitter) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(SessionMemoryService_1.name);
        this.maxMemorySize = this.configService.get('AGENT_MAX_MEMORY_SIZE', 50000);
        this.defaultWindow = this.configService.get('AGENT_DEFAULT_WINDOW', 20);
        this.compressionThreshold = this.configService.get('AGENT_COMPRESSION_THRESHOLD', 10000);
    }
    async initializeAgentMemory(agentId, config) {
        const memoryKey = `agent_memory:${agentId}`;
        const initialMemory = {
            agentId,
            config,
            initialized: true,
            createdAt: new Date(),
            lastAccessed: new Date(),
            totalMessages: 0,
            totalTokens: 0,
            compressionHistory: [],
            metadata: {
                version: '1.0.0',
                type: config.type,
                capabilities: config.capabilities || {},
            },
        };
        await this.cacheManager.set(memoryKey, initialMemory, 0);
        this.logger.log(`Initialized memory for agent ${agentId}`);
    }
    async updateAgentMemory(agentId, updates) {
        const memoryKey = `agent_memory:${agentId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (memory) {
            memory.config = { ...memory.config, ...updates };
            memory.lastUpdated = new Date();
            await this.cacheManager.set(memoryKey, memory, 0);
        }
    }
    async clearAgentMemory(agentId) {
        const memoryKey = `agent_memory:${agentId}`;
        await this.cacheManager.del(memoryKey);
        const sessions = await this.prisma.agentSessionNew.findMany({
            where: { agentId },
            select: { id: true },
        });
        const deletePromises = sessions.map(session => this.cacheManager.del(`session_memory:${session.id}`));
        await Promise.all(deletePromises);
        this.logger.log(`Cleared all memory for agent ${agentId}`);
    }
    async getOrCreateSession(agentId, sessionId, userId) {
        let session = await this.prisma.agentSessionNew.findUnique({
            where: { id: sessionId },
            include: {
                agent: true,
                user: true,
            },
        });
        if (!session) {
            session = await this.prisma.agentSessionNew.create({
                data: {
                    id: sessionId,
                    agentId,
                    userId,
                    type: 'conversation',
                    status: 'active',
                    memory: {},
                    context: {},
                    metadata: {},
                    maxMessages: 100,
                    memoryStrategy: 'sliding_window',
                    messageCount: 0,
                    tokenUsage: {},
                    startedAt: new Date(),
                    lastActivityAt: new Date(),
                },
                include: {
                    agent: true,
                    user: true,
                },
            });
            await this.initializeSessionMemory(sessionId, {
                agentId,
                userId,
                strategy: session.memoryStrategy,
                maxMessages: session.maxMessages,
            });
            this.eventEmitter.emit('session.created', {
                sessionId,
                agentId,
                userId,
            });
        }
        return session;
    }
    async initializeSessionMemory(sessionId, config) {
        const memoryKey = `session_memory:${sessionId}`;
        const initialMemory = {
            sessionId,
            agentId: config.agentId,
            userId: config.userId,
            strategy: config.strategy,
            maxMessages: config.maxMessages,
            messages: [],
            summary: null,
            context: {},
            metadata: {},
            stats: {
                totalMessages: 0,
                totalTokens: 0,
                avgResponseTime: 0,
                lastCompression: null,
            },
            initialized: true,
            createdAt: new Date(),
            lastAccessed: new Date(),
        };
        await this.cacheManager.set(memoryKey, initialMemory, 0);
        this.logger.debug(`Initialized session memory for ${sessionId}`);
    }
    async updateSessionMemory(sessionId, updates) {
        const memoryKey = `session_memory:${sessionId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (memory) {
            Object.assign(memory, updates);
            memory.lastAccessed = new Date();
            await this.cacheManager.set(memoryKey, memory, 0);
            await this.prisma.agentSessionNew.update({
                where: { id: sessionId },
                data: {
                    lastActivityAt: new Date(),
                    messageCount: memory.stats?.totalMessages || 0,
                    tokenUsage: { total: memory.stats?.totalTokens || 0 },
                    memory: memory,
                },
            });
        }
    }
    async addMessage(sessionId, messageData) {
        const memoryKey = `session_memory:${sessionId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (!memory) {
            this.logger.warn(`Session memory not found for ${sessionId}`);
            return;
        }
        const message = {
            id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            ...messageData,
            timestamp: messageData.timestamp || new Date(),
            importance: messageData.importance || this.calculateMessageImportance(messageData),
        };
        memory.messages.push(message);
        memory.stats.totalMessages++;
        memory.stats.totalTokens += messageData.tokens || 0;
        memory.lastAccessed = new Date();
        await this.prisma.agentMessage.create({
            data: {
                sessionId,
                role: messageData.role,
                content: messageData.content,
                type: messageData.type || 'text',
                metadata: messageData.metadata || {},
                tokens: messageData.tokens,
                cost: messageData.cost,
                processed: true,
                embedding: messageData.embedding,
            },
        });
        const memorySize = this.calculateMemorySize(memory);
        if (memorySize > this.maxMemorySize || memory.messages.length > memory.maxMessages) {
            await this.pruneMemory(sessionId, memory);
        }
        await this.cacheManager.set(memoryKey, memory, 0);
        this.eventEmitter.emit('message.added', {
            sessionId,
            messageId: message.id,
            role: messageData.role,
            tokens: messageData.tokens,
        });
    }
    async getConversationHistory(sessionId, limit, includeSystem = false) {
        const memoryKey = `session_memory:${sessionId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (!memory) {
            const messages = await this.prisma.agentMessage.findMany({
                where: { sessionId },
                orderBy: { createdAt: 'asc' },
                take: limit || this.defaultWindow,
            });
            return messages.map(msg => ({
                role: msg.role,
                content: msg.content,
                timestamp: msg.createdAt,
                metadata: msg.metadata,
            }));
        }
        let messages = memory.messages;
        if (!includeSystem) {
            messages = messages.filter((msg) => msg.role !== 'system');
        }
        if (limit) {
            messages = messages.slice(-limit);
        }
        return messages.map((msg) => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp,
            metadata: msg.metadata,
        }));
    }
    async getMessagesByRole(sessionId, role) {
        const memoryKey = `session_memory:${sessionId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (!memory) {
            return [];
        }
        return memory.messages.filter((msg) => msg.role === role);
    }
    async pruneMemory(sessionId, memory) {
        const strategy = memory.strategy || 'sliding_window';
        switch (strategy) {
            case 'sliding_window':
                await this.applySlidingWindowPruning(sessionId, memory);
                break;
            case 'importance':
                await this.applyImportancePruning(sessionId, memory);
                break;
            case 'fifo':
                await this.applyFIFOPruning(sessionId, memory);
                break;
            case 'compression':
                await this.applyCompressionPruning(sessionId, memory);
                break;
            default:
                await this.applySlidingWindowPruning(sessionId, memory);
        }
        memory.stats.lastCompression = new Date();
        this.logger.debug(`Pruned memory for session ${sessionId} using ${strategy}`);
    }
    async applySlidingWindowPruning(sessionId, memory) {
        const maxMessages = memory.maxMessages || this.defaultWindow;
        if (memory.messages.length > maxMessages) {
            const excessMessages = memory.messages.length - maxMessages;
            const removedMessages = memory.messages.splice(0, excessMessages);
            if (removedMessages.length > 0) {
                const summary = await this.summarizeMessages(removedMessages);
                memory.summary = this.mergeSummaries(memory.summary, summary);
            }
        }
    }
    async applyImportancePruning(sessionId, memory) {
        const maxMessages = memory.maxMessages || this.defaultWindow;
        if (memory.messages.length > maxMessages) {
            memory.messages.sort((a, b) => (b.importance || 0) - (a.importance || 0));
            const removedMessages = memory.messages.splice(maxMessages);
            if (removedMessages.length > 0) {
                const summary = await this.summarizeMessages(removedMessages);
                memory.summary = this.mergeSummaries(memory.summary, summary);
            }
        }
    }
    async applyFIFOPruning(sessionId, memory) {
        const maxMessages = memory.maxMessages || this.defaultWindow;
        if (memory.messages.length > maxMessages) {
            const removedMessages = memory.messages.splice(0, memory.messages.length - maxMessages);
            if (removedMessages.length > 0) {
                const summary = await this.summarizeMessages(removedMessages);
                memory.summary = this.mergeSummaries(memory.summary, summary);
            }
        }
    }
    async applyCompressionPruning(sessionId, memory) {
        const recentThreshold = 10;
        const recentMessages = memory.messages.slice(-recentThreshold);
        const olderMessages = memory.messages.slice(0, -recentThreshold);
        if (olderMessages.length > 0) {
            const summary = await this.summarizeMessages(olderMessages);
            memory.summary = this.mergeSummaries(memory.summary, summary);
            memory.messages = recentMessages;
        }
    }
    async summarizeMessages(messages) {
        if (messages.length === 0) {
            return {
                summary: '',
                keyPoints: [],
                entities: [],
                messageCount: 0,
                tokenCount: 0,
                timespan: { start: new Date(), end: new Date() },
            };
        }
        const summary = this.generateBasicSummary(messages);
        const keyPoints = this.extractKeyPoints(messages);
        const entities = this.extractEntities(messages);
        const topics = this.extractTopics(messages);
        return {
            summary,
            keyPoints,
            entities,
            topics,
            messageCount: messages.length,
            tokenCount: messages.reduce((sum, msg) => sum + (msg.tokens || 0), 0),
            timespan: {
                start: new Date(Math.min(...messages.map(msg => new Date(msg.timestamp).getTime()))),
                end: new Date(Math.max(...messages.map(msg => new Date(msg.timestamp).getTime()))),
            },
        };
    }
    generateBasicSummary(messages) {
        const userMessages = messages.filter(msg => msg.role === 'user');
        const assistantMessages = messages.filter(msg => msg.role === 'assistant');
        const mainTopics = this.extractTopics(messages).slice(0, 3);
        const keyUserQuestions = userMessages
            .filter(msg => msg.content.includes('?'))
            .slice(0, 2)
            .map(msg => msg.content);
        return `Conversation covered ${mainTopics.join(', ')}. ` +
            `User asked about: ${keyUserQuestions.join('; ')}. ` +
            `${assistantMessages.length} responses provided.`;
    }
    extractKeyPoints(messages) {
        const keyPoints = [];
        messages.forEach(msg => {
            const sentences = msg.content.split(/[.!?]+/);
            sentences.forEach((sentence) => {
                const trimmed = sentence.trim();
                if (trimmed.length > 20 && (trimmed.includes('important') ||
                    trimmed.includes('key') ||
                    trimmed.includes('main') ||
                    trimmed.includes('note that') ||
                    trimmed.includes('remember'))) {
                    keyPoints.push(trimmed);
                }
            });
        });
        return keyPoints.slice(0, 5);
    }
    extractEntities(messages) {
        const entities = [];
        const entityPattern = /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g;
        messages.forEach(msg => {
            const matches = msg.content.match(entityPattern) || [];
            matches.forEach(match => {
                if (!entities.find(e => e.text === match)) {
                    entities.push({
                        text: match,
                        type: 'PERSON',
                        confidence: 0.8,
                    });
                }
            });
        });
        return entities.slice(0, 10);
    }
    extractTopics(messages) {
        const topicKeywords = new Map();
        const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
        messages.forEach(msg => {
            const words = msg.content.toLowerCase().match(/\b\w+\b/g) || [];
            words.forEach(word => {
                if (word.length > 3 && !commonWords.has(word)) {
                    topicKeywords.set(word, (topicKeywords.get(word) || 0) + 1);
                }
            });
        });
        return Array.from(topicKeywords.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([word]) => word);
    }
    mergeSummaries(existing, newSummary) {
        if (!existing) {
            return newSummary;
        }
        return {
            summary: `${existing.summary} ${newSummary.summary}`,
            keyPoints: [...existing.keyPoints, ...newSummary.keyPoints].slice(0, 10),
            entities: [...existing.entities, ...newSummary.entities].slice(0, 20),
            topics: [...(existing.topics || []), ...(newSummary.topics || [])].slice(0, 10),
            messageCount: existing.messageCount + newSummary.messageCount,
            tokenCount: existing.tokenCount + newSummary.tokenCount,
            timespan: {
                start: new Date(Math.min(existing.timespan.start.getTime(), newSummary.timespan.start.getTime())),
                end: new Date(Math.max(existing.timespan.end.getTime(), newSummary.timespan.end.getTime())),
            },
        };
    }
    calculateMessageImportance(messageData) {
        let importance = 1.0;
        if (messageData.content.includes('?')) {
            importance += 0.3;
        }
        if (messageData.content.length > 100) {
            importance += 0.2;
        }
        if (messageData.metadata && Object.keys(messageData.metadata).length > 0) {
            importance += 0.1;
        }
        if (messageData.role === 'tool' || messageData.type === 'tool_call') {
            importance += 0.4;
        }
        return Math.min(importance, 2.0);
    }
    calculateMemorySize(memory) {
        return JSON.stringify(memory).length;
    }
    async getMemoryMetrics(sessionId) {
        const memoryKey = `session_memory:${sessionId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (!memory) {
            return {
                totalMessages: 0,
                totalTokens: 0,
                memorySize: 0,
                compressionRatio: 0,
                lastActivity: new Date(),
                averageMessageLength: 0,
                topTopics: [],
                sentimentTrend: [],
            };
        }
        const memorySize = this.calculateMemorySize(memory);
        const avgMessageLength = memory.messages.length > 0
            ? memory.messages.reduce((sum, msg) => sum + msg.content.length, 0) / memory.messages.length
            : 0;
        return {
            totalMessages: memory.stats.totalMessages || 0,
            totalTokens: memory.stats.totalTokens || 0,
            memorySize,
            compressionRatio: memory.summary ? 0.7 : 1.0,
            lastActivity: memory.lastAccessed,
            averageMessageLength: avgMessageLength,
            topTopics: this.extractTopics(memory.messages),
            sentimentTrend: [],
        };
    }
    async searchMemory(sessionId, query, options) {
        const memoryKey = `session_memory:${sessionId}`;
        const memory = await this.cacheManager.get(memoryKey);
        if (!memory) {
            return [];
        }
        let messages = memory.messages;
        if (options?.role) {
            messages = messages.filter((msg) => msg.role === options.role);
        }
        if (options?.timeRange) {
            messages = messages.filter((msg) => {
                const msgTime = new Date(msg.timestamp);
                return msgTime >= options.timeRange.start && msgTime <= options.timeRange.end;
            });
        }
        const queryLower = query.toLowerCase();
        const matchingMessages = messages.filter((msg) => msg.content.toLowerCase().includes(queryLower));
        const limit = options?.limit || 10;
        return matchingMessages.slice(0, limit);
    }
};
exports.SessionMemoryService = SessionMemoryService;
exports.SessionMemoryService = SessionMemoryService = SessionMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, config_1.ConfigService,
        event_emitter_mock_1.MockEventEmitter])
], SessionMemoryService);
//# sourceMappingURL=session-memory.service.js.map