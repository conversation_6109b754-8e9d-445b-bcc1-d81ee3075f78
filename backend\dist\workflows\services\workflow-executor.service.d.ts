import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
export declare class WorkflowExecutorService {
    private prisma;
    private apixGateway;
    private sessionsService;
    private readonly logger;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, sessionsService: SessionsService);
    executeWorkflow(executionId: string, workflowId: string, sessionId: string, organizationId: string, input?: Record<string, any>): Promise<any>;
    private executeNode;
    private executeAgentNode;
    private executeToolNode;
    private executeConditionNode;
    private executeParallelNode;
    private executeHumanInputNode;
    private executeDelayNode;
    private executeHybridNode;
    private orchestrateMultipleTools;
    private determineNextTool;
    private extractToolParameters;
    private getNextNodes;
    private evaluateEdgeCondition;
    private evaluateCondition;
    private processVariables;
}
