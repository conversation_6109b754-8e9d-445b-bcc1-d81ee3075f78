import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import * as pako from 'pako';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
  user?: any;
}

interface APXEvent {
  type: string;
  channel?: string;
  data: any;
  timestamp: string;
  organizationId?: string;
  compressed?: boolean;
  retryCount?: number;
  priority?: 'low' | 'normal' | 'high';
  metadata?: Record<string, any>;
}

@Injectable()
@WebSocketGateway({
  namespace: '/apix',
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  transports: ['websocket', 'polling'],
})
export class ApixGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ApixGateway.name);
  private connectedClients = new Map<string, AuthenticatedSocket>();
  private userSockets = new Map<string, Set<string>>(); // userId -> socketIds
  private organizationSockets = new Map<string, Set<string>>(); // orgId -> socketIds
  private roomSubscriptions = new Map<string, Set<string>>(); // roomId -> socketIds
  private compressionThreshold = 1024; // 1KB
  private eventHistory = new Map<string, APXEvent[]>(); // organizationId -> events
  private maxHistorySize = 1000;

  constructor(
    private jwtService: JwtService,
    private prisma: PrismaService,
    private eventEmitter: EventEmitter2,
  ) {}

  afterInit(server: Server) {
    this.logger.log('APIX Gateway initialized with production features');
    
    // Set up compression middleware
    server.engine.generateId = () => {
      return `apix_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    };

    // Enable compression
    server.engine.compression = true;
    server.engine.perMessageDeflate = {
      threshold: this.compressionThreshold,
      concurrencyLimit: 10,
      memLevel: 7,
    };
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      this.logger.log(`Client attempting connection: ${client.id}`);

      // Authenticate client
      const token = client.handshake.auth?.token;
      const organizationId = client.handshake.auth?.organizationId;

      if (!token || !organizationId) {
        this.logger.warn(`Unauthenticated connection attempt: ${client.id}`);
        client.disconnect();
        return;
      }

      // Verify JWT token
      const payload = await this.jwtService.verifyAsync(token);
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        include: { organization: true },
      });

      if (!user || user.organizationId !== organizationId) {
        this.logger.warn(`Invalid user or organization: ${client.id}`);
        client.disconnect();
        return;
      }

      // Set client properties
      client.userId = user.id;
      client.organizationId = organizationId;
      client.user = user;

      // Store client connection
      this.connectedClients.set(client.id, client);

      // Track user sockets
      if (!this.userSockets.has(user.id)) {
        this.userSockets.set(user.id, new Set());
      }
      this.userSockets.get(user.id)!.add(client.id);

      // Track organization sockets
      if (!this.organizationSockets.has(organizationId)) {
        this.organizationSockets.set(organizationId, new Set());
      }
      this.organizationSockets.get(organizationId)!.add(client.id);

      // Join organization room
      client.join(`organization:${organizationId}`);

      // Send connection confirmation with server features
      client.emit('apix:connected', {
        clientId: client.id,
        userId: user.id,
        organizationId,
        features: {
          compression: true,
          latencyTracking: true,
          eventReplay: true,
          maxRetryAttempts: 5,
          heartbeatInterval: 30000,
        },
        serverTime: Date.now(),
      });

      // Send recent event history if available
      const history = this.eventHistory.get(organizationId);
      if (history && history.length > 0) {
        client.emit('apix:replay', {
          events: history.slice(-50), // Last 50 events
          totalCount: history.length,
          timestamp: Date.now(),
        });
      }

      this.logger.log(`Client connected successfully: ${client.id} (User: ${user.id}, Org: ${organizationId})`);

      // Emit connection event
      this.eventEmitter.emit('client.connected', {
        clientId: client.id,
        userId: user.id,
        organizationId,
        timestamp: Date.now(),
      });

    } catch (error) {
      this.logger.error(`Connection authentication failed: ${error.message}`, error.stack);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`Client disconnected: ${client.id}`);

    // Clean up client tracking
    this.connectedClients.delete(client.id);

    if (client.userId) {
      const userSockets = this.userSockets.get(client.userId);
      if (userSockets) {
        userSockets.delete(client.id);
        if (userSockets.size === 0) {
          this.userSockets.delete(client.userId);
        }
      }
    }

    if (client.organizationId) {
      const orgSockets = this.organizationSockets.get(client.organizationId);
      if (orgSockets) {
        orgSockets.delete(client.id);
        if (orgSockets.size === 0) {
          this.organizationSockets.delete(client.organizationId);
        }
      }
    }

    // Clean up room subscriptions
    for (const [roomId, socketIds] of this.roomSubscriptions.entries()) {
      socketIds.delete(client.id);
      if (socketIds.size === 0) {
        this.roomSubscriptions.delete(roomId);
      }
    }

    // Emit disconnection event
    this.eventEmitter.emit('client.disconnected', {
      clientId: client.id,
      userId: client.userId,
      organizationId: client.organizationId,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:event')
  async handleEvent(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() event: APXEvent,
  ) {
    try {
      // Add client context to event
      event.organizationId = client.organizationId;
      event.timestamp = new Date().toISOString();

      // Handle compression
      if (event.compressed && event.data) {
        try {
          const decompressed = pako.inflate(event.data, { to: 'string' });
          event.data = JSON.parse(decompressed);
          event.compressed = false;
        } catch (error) {
          this.logger.error('Event decompression failed', error);
          return;
        }
      }

      // Store in event history
      this.storeEventInHistory(client.organizationId!, event);

      // Emit to appropriate channels
      if (event.channel) {
        this.server.to(event.channel).emit(event.type, event.data);
      } else {
        // Broadcast to organization
        this.server.to(`organization:${client.organizationId}`).emit(event.type, event.data);
      }

      // Emit internal event for processing
      this.eventEmitter.emit(`apix.${event.type}`, {
        ...event,
        clientId: client.id,
        userId: client.userId,
      });

    } catch (error) {
      this.logger.error(`Event handling failed: ${error.message}`, error.stack);
      client.emit('apix:error', {
        message: 'Event processing failed',
        originalEvent: event.type,
      });
    }
  }

  @SubscribeMessage('join:room')
  handleJoinRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() roomId: string,
  ) {
    client.join(roomId);
    
    // Track room subscription
    if (!this.roomSubscriptions.has(roomId)) {
      this.roomSubscriptions.set(roomId, new Set());
    }
    this.roomSubscriptions.get(roomId)!.add(client.id);

    this.logger.log(`Client ${client.id} joined room: ${roomId}`);
    
    client.emit('room:joined', { roomId, timestamp: Date.now() });
  }

  @SubscribeMessage('leave:room')
  handleLeaveRoom(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() roomId: string,
  ) {
    client.leave(roomId);
    
    // Remove from room subscription tracking
    const roomSockets = this.roomSubscriptions.get(roomId);
    if (roomSockets) {
      roomSockets.delete(client.id);
      if (roomSockets.size === 0) {
        this.roomSubscriptions.delete(roomId);
      }
    }

    this.logger.log(`Client ${client.id} left room: ${roomId}`);
    
    client.emit('room:left', { roomId, timestamp: Date.now() });
  }

  @SubscribeMessage('apix:subscribe')
  handleSubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { channels: string[]; filters?: Record<string, any> },
  ) {
    for (const channel of data.channels) {
      client.join(channel);
      
      if (!this.roomSubscriptions.has(channel)) {
        this.roomSubscriptions.set(channel, new Set());
      }
      this.roomSubscriptions.get(channel)!.add(client.id);
    }

    client.emit('apix:subscribed', {
      channels: data.channels,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:unsubscribe')
  handleUnsubscribe(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { channels: string[] },
  ) {
    for (const channel of data.channels) {
      client.leave(channel);
      
      const channelSockets = this.roomSubscriptions.get(channel);
      if (channelSockets) {
        channelSockets.delete(client.id);
        if (channelSockets.size === 0) {
          this.roomSubscriptions.delete(channel);
        }
      }
    }

    client.emit('apix:unsubscribed', {
      channels: data.channels,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:request_replay')
  handleRequestReplay(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { since?: string; limit?: number },
  ) {
    const history = this.eventHistory.get(client.organizationId!);
    if (!history) {
      client.emit('apix:replay', { events: [], totalCount: 0, timestamp: Date.now() });
      return;
    }

    let events = history;
    
    if (data.since) {
      const sinceTime = new Date(data.since).getTime();
      events = history.filter(event => new Date(event.timestamp).getTime() > sinceTime);
    }

    if (data.limit) {
      events = events.slice(-data.limit);
    }

    client.emit('apix:replay', {
      events,
      totalCount: history.length,
      timestamp: Date.now(),
    });
  }

  @SubscribeMessage('apix:heartbeat')
  handleHeartbeat(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { timestamp: number },
  ) {
    client.emit('apix:heartbeat', {
      clientTimestamp: data.timestamp,
      serverTimestamp: Date.now(),
      latency: Date.now() - data.timestamp,
    });
  }

  // Production-grade event emission methods

  async emitToUser(userId: string, eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}) {
    const userSockets = this.userSockets.get(userId);
    if (!userSockets || userSockets.size === 0) {
      this.logger.warn(`No active sockets for user: ${userId}`);
      return;
    }

    const event = this.createEvent(eventType, data, options);
    
    for (const socketId of userSockets) {
      const socket = this.connectedClients.get(socketId);
      if (socket) {
        this.emitToSocket(socket, event);
      }
    }
  }

  async emitToOrganization(organizationId: string, eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}) {
    const event = this.createEvent(eventType, data, options);
    event.organizationId = organizationId;

    // Store in history
    this.storeEventInHistory(organizationId, event);

    // Emit to all organization sockets
    this.server.to(`organization:${organizationId}`).emit(eventType, data);
  }

  async emitToRoom(roomId: string, eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}) {
    const event = this.createEvent(eventType, data, options);
    this.server.to(roomId).emit(eventType, data);
  }

  async emitWorkflowEvent(
    organizationId: string,
    workflowId: string,
    eventType: string,
    data: any,
    options: {
      compress?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ) {
    const enrichedData = {
      ...data,
      workflowId,
      organizationId,
      timestamp: Date.now(),
    };

    // Emit to workflow-specific room
    await this.emitToRoom(`workflow:${workflowId}`, eventType, enrichedData, options);
    
    // Also emit to organization
    await this.emitToOrganization(organizationId, eventType, enrichedData, options);
  }

  async emitAgentEvent(
    organizationId: string,
    agentId: string,
    eventType: string,
    data: any,
    options: {
      compress?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ) {
    const enrichedData = {
      ...data,
      agentId,
      organizationId,
      timestamp: Date.now(),
    };

    // Emit to agent-specific room
    await this.emitToRoom(`agent:${agentId}`, eventType, enrichedData, options);
    
    // Also emit to organization
    await this.emitToOrganization(organizationId, eventType, enrichedData, options);
  }

  async emitToolEvent(
    organizationId: string,
    toolId: string,
    eventType: string,
    data: any,
    options: {
      compress?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ) {
    const enrichedData = {
      ...data,
      toolId,
      organizationId,
      timestamp: Date.now(),
    };

    // Emit to tool-specific room
    await this.emitToRoom(`tool:${toolId}`, eventType, enrichedData, options);
    
    // Also emit to organization
    await this.emitToOrganization(organizationId, eventType, enrichedData, options);
  }

  async emitSystemEvent(eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'high' | 'normal' | 'low';
  } = {}) {
    const event = this.createEvent(eventType, data, options);
    
    // Emit to all connected clients
    this.server.emit(eventType, data);
    
    // Store in all organization histories
    for (const organizationId of this.organizationSockets.keys()) {
      this.storeEventInHistory(organizationId, { ...event, organizationId });
    }
  }

  private createEvent(eventType: string, data: any, options: {
    compress?: boolean;
    priority?: 'low' | 'normal' | 'high';
  } = {}): APXEvent {
    let processedData = data;
    let compressed = false;

    // Auto-compress large payloads
    if (options.compress || this.shouldCompress(data)) {
      try {
        const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
        const compressedData = pako.deflate(jsonString);
        processedData = Array.from(compressedData);
        compressed = true;
      } catch (error) {
        this.logger.warn('Event compression failed, sending uncompressed', error);
      }
    }

    return {
      type: eventType,
      data: processedData,
      timestamp: new Date().toISOString(),
      compressed,
      priority: options.priority || 'normal',
      metadata: {
        serverTimestamp: Date.now(),
        originalSize: JSON.stringify(data).length,
        compressedSize: compressed ? processedData.length : undefined,
      },
    };
  }

  private shouldCompress(data: any): boolean {
    try {
      const size = JSON.stringify(data).length;
      return size > this.compressionThreshold;
    } catch {
      return false;
    }
  }

  private emitToSocket(socket: AuthenticatedSocket, event: APXEvent) {
    if (event.compressed) {
      socket.compress(true).emit(event.type, event.data);
    } else {
      socket.emit(event.type, event.data);
    }
  }

  private storeEventInHistory(organizationId: string, event: APXEvent) {
    if (!this.eventHistory.has(organizationId)) {
      this.eventHistory.set(organizationId, []);
    }

    const history = this.eventHistory.get(organizationId)!;
    history.push(event);

    // Limit history size
    if (history.length > this.maxHistorySize) {
      history.shift();
    }
  }

  // Event listeners for internal events

  @OnEvent('workflow.started')
  async handleWorkflowStarted(payload: any) {
    await this.emitWorkflowEvent(
      payload.organizationId,
      payload.workflowId,
      'workflow_started',
      payload,
      { priority: 'high' }
    );
  }

  @OnEvent('workflow.completed')
  async handleWorkflowCompleted(payload: any) {
    await this.emitWorkflowEvent(
      payload.organizationId,
      payload.workflowId,
      'workflow_completed',
      payload,
      { priority: 'high' }
    );
  }

  @OnEvent('workflow.failed')
  async handleWorkflowFailed(payload: any) {
    await this.emitWorkflowEvent(
      payload.organizationId,
      payload.workflowId,
      'workflow_failed',
      payload,
      { priority: 'high' }
    );
  }

  @OnEvent('agent.thinking')
  async handleAgentThinking(payload: any) {
    await this.emitAgentEvent(
      payload.organizationId,
      payload.agentId,
      'agent_thinking',
      payload
    );
  }

  @OnEvent('agent.response')
  async handleAgentResponse(payload: any) {
    await this.emitAgentEvent(
      payload.organizationId,
      payload.agentId,
      'agent_response',
      payload,
      { compress: true }
    );
  }

  @OnEvent('tool.started')
  async handleToolStarted(payload: any) {
    await this.emitToolEvent(
      payload.organizationId,
      payload.toolId,
      'tool_call_start',
      payload
    );
  }

  @OnEvent('tool.completed')
  async handleToolCompleted(payload: any) {
    await this.emitToolEvent(
      payload.organizationId,
      payload.toolId,
      'tool_call_result',
      payload,
      { compress: true }
    );
  }

  @OnEvent('tool.failed')
  async handleToolFailed(payload: any) {
    await this.emitToolEvent(
      payload.organizationId,
      payload.toolId,
      'tool_call_error',
      payload,
      { priority: 'high' }
    );
  }

  @OnEvent('system.alert')
  async handleSystemAlert(payload: any) {
    await this.emitSystemEvent('system_alert', payload, { priority: 'high' });
  }

  @OnEvent('system.maintenance')
  async handleSystemMaintenance(payload: any) {
    await this.emitSystemEvent('system_maintenance', payload, { priority: 'high' });
  }

  // Health and monitoring methods

  getConnectionStats() {
    return {
      totalConnections: this.connectedClients.size,
      userConnections: this.userSockets.size,
      organizationConnections: this.organizationSockets.size,
      activeRooms: this.roomSubscriptions.size,
      eventHistorySize: Array.from(this.eventHistory.values()).reduce((sum, history) => sum + history.length, 0),
    };
  }

  getOrganizationConnections(organizationId: string) {
    const sockets = this.organizationSockets.get(organizationId);
    return sockets ? Array.from(sockets).map(socketId => {
      const socket = this.connectedClients.get(socketId);
      return socket ? {
        socketId,
        userId: socket.userId,
        connectedAt: socket.handshake.time,
      } : null;
    }).filter(Boolean) : [];
  }

  async broadcastToOrganization(organizationId: string, message: string, data?: any) {
    await this.emitToOrganization(organizationId, 'broadcast', {
      message,
      data,
      timestamp: Date.now(),
    });
  }

  // Cleanup method for graceful shutdown
  async cleanup() {
    this.logger.log('Cleaning up APIX Gateway...');
    
    // Notify all clients of shutdown
    this.server.emit('system_maintenance', {
      type: 'shutdown',
      message: 'Server is shutting down',
      timestamp: Date.now(),
    });

    // Wait a bit for messages to be sent
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Disconnect all clients
    this.server.disconnectSockets(true);
    
    // Clear all maps
    this.connectedClients.clear();
    this.userSockets.clear();
    this.organizationSockets.clear();
    this.roomSubscriptions.clear();
    this.eventHistory.clear();

    this.logger.log('APIX Gateway cleanup completed');
  }
}