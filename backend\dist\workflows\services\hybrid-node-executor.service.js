"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var HybridNodeExecutorService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HybridNodeExecutorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const apix_gateway_1 = require("../../apix/apix.gateway");
const sessions_service_1 = require("../../sessions/sessions.service");
const agents_service_1 = require("../../agents/agents.service");
const tools_service_1 = require("../../tools/tools.service");
const event_emitter_1 = require("@nestjs/event-emitter");
let HybridNodeExecutorService = HybridNodeExecutorService_1 = class HybridNodeExecutorService {
    constructor(prisma, apixGateway, sessionsService, agentsService, toolsService, eventEmitter) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.sessionsService = sessionsService;
        this.agentsService = agentsService;
        this.toolsService = toolsService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(HybridNodeExecutorService_1.name);
        this.activeExecutions = new Map();
    }
    async executeHybridNode(config, variables, context) {
        const executionKey = `${context.executionId}_${context.nodeId}`;
        try {
            this.logger.log(`Starting hybrid node execution: ${executionKey}`);
            this.activeExecutions.set(executionKey, {
                config,
                variables,
                context,
                startTime: Date.now(),
                status: 'running',
            });
            await this.emitHybridEvent('hybrid_execution_started', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                agentId: config.agentId,
                toolIds: config.toolIds,
                executionPattern: config.executionPattern,
                timestamp: Date.now(),
            }, context);
            if (config.coordination.shareContext) {
                await this.initializeSharedContext(context, variables);
            }
            let result;
            switch (config.executionPattern) {
                case 'agent-first':
                    result = await this.executeAgentFirstPattern(config, variables, context);
                    break;
                case 'tool-first':
                    result = await this.executeToolFirstPattern(config, variables, context);
                    break;
                case 'parallel':
                    result = await this.executeParallelPattern(config, variables, context);
                    break;
                case 'multi-tool-orchestration':
                    result = await this.executeOrchestrationPattern(config, variables, context);
                    break;
                default:
                    throw new Error(`Unknown execution pattern: ${config.executionPattern}`);
            }
            const execution = this.activeExecutions.get(executionKey);
            if (execution) {
                execution.status = 'completed';
                execution.result = result;
                execution.endTime = Date.now();
                execution.duration = execution.endTime - execution.startTime;
            }
            await this.emitHybridEvent('hybrid_execution_completed', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                result,
                duration: execution?.duration,
                pattern: config.executionPattern,
                timestamp: Date.now(),
            }, context);
            await this.storeExecutionAnalytics(context, config, result, execution?.duration || 0);
            return result;
        }
        catch (error) {
            this.logger.error(`Hybrid node execution failed: ${error.message}`, error.stack);
            const execution = this.activeExecutions.get(executionKey);
            if (execution) {
                execution.status = 'failed';
                execution.error = error.message;
                execution.endTime = Date.now();
                execution.duration = execution.endTime - execution.startTime;
            }
            await this.emitHybridEvent('hybrid_execution_failed', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                error: error.message,
                duration: execution?.duration,
                timestamp: Date.now(),
            }, context);
            if (config.fallback.enabled && this.shouldTriggerFallback(error, config.fallback.conditions)) {
                this.logger.log(`Triggering fallback for execution: ${executionKey}`);
                return await this.executeFallback(config, variables, context, error);
            }
            throw error;
        }
        finally {
            setTimeout(() => {
                this.activeExecutions.delete(executionKey);
            }, 300000);
        }
    }
    async executeAgentFirstPattern(config, variables, context) {
        this.logger.log(`Executing agent-first pattern for node: ${context.nodeId}`);
        await this.emitHybridEvent('agent_first_started', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            agentId: config.agentId,
            toolCount: config.toolIds.length,
        }, context);
        const agentResult = await this.executeAgent(config, variables, context);
        const toolParameters = this.extractToolParameters(agentResult, config.toolIds);
        const toolResults = [];
        for (const toolId of config.toolIds) {
            try {
                const toolConfig = config.toolConfigs[toolId] || {};
                const parameters = { ...toolConfig.parameters, ...toolParameters[toolId] };
                const toolResult = await this.executeTool(toolId, parameters, toolConfig, context);
                toolResults.push({ toolId, result: toolResult, success: true });
                if (config.coordination.shareContext) {
                    await this.updateSharedContext(context, { [`tool_${toolId}_result`]: toolResult });
                }
            }
            catch (error) {
                this.logger.error(`Tool execution failed: ${toolId}`, error);
                toolResults.push({ toolId, error: error.message, success: false });
                if (config.coordination.errorPropagation) {
                    throw error;
                }
            }
        }
        const result = {
            pattern: 'agent-first',
            agentResult,
            toolResults,
            executionOrder: ['agent', ...config.toolIds],
            timestamp: Date.now(),
        };
        await this.emitHybridEvent('agent_first_completed', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            result,
        }, context);
        return result;
    }
    async executeToolFirstPattern(config, variables, context) {
        this.logger.log(`Executing tool-first pattern for node: ${context.nodeId}`);
        await this.emitHybridEvent('tool_first_started', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            toolCount: config.toolIds.length,
            agentId: config.agentId,
        }, context);
        const toolResults = [];
        const aggregatedData = {};
        for (const toolId of config.toolIds) {
            try {
                const toolConfig = config.toolConfigs[toolId] || {};
                const parameters = { ...toolConfig.parameters, ...variables };
                const toolResult = await this.executeTool(toolId, parameters, toolConfig, context);
                toolResults.push({ toolId, result: toolResult, success: true });
                Object.assign(aggregatedData, toolResult);
                if (config.coordination.shareContext) {
                    await this.updateSharedContext(context, { [`tool_${toolId}_result`]: toolResult });
                }
            }
            catch (error) {
                this.logger.error(`Tool execution failed: ${toolId}`, error);
                toolResults.push({ toolId, error: error.message, success: false });
                if (config.coordination.errorPropagation) {
                    throw error;
                }
            }
        }
        const enrichedVariables = {
            ...variables,
            toolResults: aggregatedData,
            toolExecutionSummary: toolResults,
        };
        const agentResult = await this.executeAgent(config, enrichedVariables, context);
        const result = {
            pattern: 'tool-first',
            toolResults,
            agentResult,
            aggregatedData,
            executionOrder: [...config.toolIds, 'agent'],
            timestamp: Date.now(),
        };
        await this.emitHybridEvent('tool_first_completed', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            result,
        }, context);
        return result;
    }
    async executeParallelPattern(config, variables, context) {
        this.logger.log(`Executing parallel pattern for node: ${context.nodeId}`);
        await this.emitHybridEvent('parallel_execution_started', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            agentId: config.agentId,
            toolCount: config.toolIds.length,
            parallelLimit: config.performance.parallelLimit,
        }, context);
        const agentPromise = this.executeAgent(config, variables, context);
        const toolPromises = config.toolIds.map(async (toolId) => {
            try {
                const toolConfig = config.toolConfigs[toolId] || {};
                const parameters = { ...toolConfig.parameters, ...variables };
                const result = await this.executeTool(toolId, parameters, toolConfig, context);
                return { toolId, result, success: true };
            }
            catch (error) {
                return { toolId, error: error.message, success: false };
            }
        });
        const allPromises = [agentPromise, ...toolPromises];
        const results = await this.executeWithParallelLimit(allPromises, config.performance.parallelLimit);
        const agentResult = results[0];
        const toolResults = results.slice(1);
        if (config.coordination.syncPoints.length > 0) {
            await this.processSyncPoints(config.coordination.syncPoints, context, {
                agentResult,
                toolResults,
            });
        }
        const result = {
            pattern: 'parallel',
            agentResult,
            toolResults,
            executionOrder: ['parallel'],
            synchronizationPoints: config.coordination.syncPoints,
            timestamp: Date.now(),
        };
        await this.emitHybridEvent('parallel_execution_completed', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            result,
        }, context);
        return result;
    }
    async executeOrchestrationPattern(config, variables, context) {
        this.logger.log(`Executing orchestration pattern for node: ${context.nodeId}`);
        await this.emitHybridEvent('orchestration_started', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            agentId: config.agentId,
            toolIds: config.toolIds,
            maxIterations: config.maxIterations,
        }, context);
        let currentVariables = { ...variables };
        const executionHistory = [];
        let iteration = 0;
        while (iteration < config.maxIterations) {
            this.logger.log(`Orchestration iteration ${iteration + 1}/${config.maxIterations}`);
            const orchestrationPrompt = this.buildOrchestrationPrompt(config.toolIds, currentVariables, executionHistory, iteration);
            const agentConfig = {
                ...config.agentConfig,
                systemPrompt: orchestrationPrompt,
            };
            const agentResult = await this.executeAgent({ ...config, agentConfig }, currentVariables, context);
            executionHistory.push({
                iteration: iteration + 1,
                type: 'agent_decision',
                result: agentResult,
                timestamp: Date.now(),
            });
            const decision = this.parseOrchestrationDecision(agentResult.response, config.toolIds);
            await this.emitHybridEvent('orchestration_decision', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                iteration: iteration + 1,
                decision,
                agentResult,
            }, context);
            if (decision.complete) {
                this.logger.log(`Orchestration completed at iteration ${iteration + 1}`);
                break;
            }
            if (decision.toolsToExecute.length > 0) {
                for (const toolExecution of decision.toolsToExecute) {
                    try {
                        const toolConfig = config.toolConfigs[toolExecution.toolId] || {};
                        const parameters = {
                            ...toolConfig.parameters,
                            ...toolExecution.parameters,
                            ...currentVariables,
                        };
                        const toolResult = await this.executeTool(toolExecution.toolId, parameters, toolConfig, context);
                        executionHistory.push({
                            iteration: iteration + 1,
                            type: 'tool_execution',
                            toolId: toolExecution.toolId,
                            result: toolResult,
                            timestamp: Date.now(),
                        });
                        currentVariables = { ...currentVariables, ...toolResult };
                        if (config.coordination.shareContext) {
                            await this.updateSharedContext(context, {
                                [`iteration_${iteration + 1}_tool_${toolExecution.toolId}`]: toolResult,
                            });
                        }
                    }
                    catch (error) {
                        this.logger.error(`Tool execution failed in orchestration: ${toolExecution.toolId}`, error);
                        executionHistory.push({
                            iteration: iteration + 1,
                            type: 'tool_error',
                            toolId: toolExecution.toolId,
                            error: error.message,
                            timestamp: Date.now(),
                        });
                        if (config.coordination.errorPropagation) {
                            throw error;
                        }
                    }
                }
            }
            iteration++;
        }
        const summaryPrompt = this.buildSummaryPrompt(executionHistory, currentVariables);
        const finalAgentResult = await this.executeAgent({ ...config, agentConfig: { ...config.agentConfig, systemPrompt: summaryPrompt } }, currentVariables, context);
        const result = {
            pattern: 'multi-tool-orchestration',
            executionHistory,
            finalResult: currentVariables,
            finalSummary: finalAgentResult,
            totalIterations: iteration,
            maxIterations: config.maxIterations,
            timestamp: Date.now(),
        };
        await this.emitHybridEvent('orchestration_completed', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            result,
        }, context);
        return result;
    }
    async executeAgent(config, variables, context) {
        await this.emitHybridEvent('agent_execution_started', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            agentId: config.agentId,
        }, context);
        try {
            let contextData = {};
            if (config.coordination.shareContext) {
                contextData = await this.getSharedContext(context);
            }
            const input = {
                ...variables,
                ...contextData,
                hybridContext: {
                    executionPattern: config.executionPattern,
                    availableTools: config.toolIds,
                    nodeId: context.nodeId,
                },
            };
            const result = await this.agentsService.executeAgent(config.agentId, {
                input: input.message || input.query || JSON.stringify(input),
                systemPrompt: config.agentConfig.systemPrompt,
                maxTokens: config.agentConfig.maxTokens,
                temperature: config.agentConfig.temperature,
                variables: input,
            }, context.organizationId);
            await this.emitHybridEvent('agent_execution_completed', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                agentId: config.agentId,
                result,
            }, context);
            return result;
        }
        catch (error) {
            await this.emitHybridEvent('agent_execution_failed', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                agentId: config.agentId,
                error: error.message,
            }, context);
            throw error;
        }
    }
    async executeTool(toolId, parameters, toolConfig, context) {
        await this.emitHybridEvent('tool_execution_started', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            toolId,
            parameters,
        }, context);
        try {
            const timeoutMs = toolConfig.timeout || 30000;
            let result;
            if (toolConfig.retryPolicy?.enabled) {
                result = await this.executeWithRetry(() => this.toolsService.executeTool(toolId, parameters, {
                    organizationId: context.organizationId,
                    executorType: 'hybrid_workflow',
                    executorId: context.executionId,
                    sessionId: context.sessionId,
                }), toolConfig.retryPolicy.maxRetries || 2, timeoutMs);
            }
            else {
                result = await Promise.race([
                    this.toolsService.executeTool(toolId, parameters, {
                        organizationId: context.organizationId,
                        executorType: 'hybrid_workflow',
                        executorId: context.executionId,
                        sessionId: context.sessionId,
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Tool execution timeout')), timeoutMs)),
                ]);
            }
            await this.emitHybridEvent('tool_execution_completed', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                toolId,
                result,
            }, context);
            return result;
        }
        catch (error) {
            await this.emitHybridEvent('tool_execution_failed', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                toolId,
                error: error.message,
            }, context);
            throw error;
        }
    }
    async executeWithRetry(operation, maxRetries, timeoutMs) {
        let lastError;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await Promise.race([
                    operation(),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Operation timeout')), timeoutMs)),
                ]);
            }
            catch (error) {
                lastError = error;
                if (attempt < maxRetries) {
                    const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        throw lastError;
    }
    async executeWithParallelLimit(promises, limit) {
        const results = [];
        const executing = [];
        for (let i = 0; i < promises.length; i++) {
            const promise = promises[i].then(result => {
                executing.splice(executing.indexOf(promise), 1);
                return result;
            });
            results.push(promise);
            executing.push(promise);
            if (executing.length >= limit) {
                await Promise.race(executing);
            }
        }
        return Promise.all(results);
    }
    extractToolParameters(agentResult, toolIds) {
        const parameters = {};
        try {
            const response = agentResult.response || '';
            const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[1]);
                if (parsed.toolParameters) {
                    return parsed.toolParameters;
                }
            }
            for (const toolId of toolIds) {
                const toolMatch = response.match(new RegExp(`${toolId}[:\\s]*({[^}]+})`, 'i'));
                if (toolMatch) {
                    try {
                        parameters[toolId] = JSON.parse(toolMatch[1]);
                    }
                    catch {
                        parameters[toolId] = { query: toolMatch[1] };
                    }
                }
                else {
                    parameters[toolId] = {
                        query: response.substring(0, 200),
                        context: agentResult,
                    };
                }
            }
        }
        catch (error) {
            this.logger.warn('Failed to extract tool parameters from agent result', error);
            for (const toolId of toolIds) {
                parameters[toolId] = {
                    query: agentResult.response?.substring(0, 200) || '',
                    context: agentResult,
                };
            }
        }
        return parameters;
    }
    buildOrchestrationPrompt(toolIds, variables, history, iteration) {
        return `You are orchestrating a multi-tool workflow. 

Available tools: ${toolIds.join(', ')}
Current iteration: ${iteration + 1}
Current data: ${JSON.stringify(variables, null, 2)}

Execution history:
${history.map(h => `- ${h.type}: ${JSON.stringify(h.result || h.error)}`).join('\n')}

Decide what to do next. Respond with JSON:
{
  "complete": boolean,
  "reasoning": "explanation of decision",
  "toolsToExecute": [
    {
      "toolId": "tool_name",
      "parameters": { "key": "value" }
    }
  ]
}

If the task is complete, set "complete": true and provide a summary.`;
    }
    buildSummaryPrompt(history, finalVariables) {
        return `Summarize the multi-tool orchestration execution:

Execution History:
${history.map(h => `- ${h.type}: ${JSON.stringify(h.result || h.error)}`).join('\n')}

Final Data:
${JSON.stringify(finalVariables, null, 2)}

Provide a comprehensive summary of what was accomplished.`;
    }
    parseOrchestrationDecision(response, availableTools) {
        try {
            const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/({[\s\S]*})/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[1]);
                return {
                    complete: parsed.complete || false,
                    reasoning: parsed.reasoning,
                    toolsToExecute: parsed.toolsToExecute || [],
                };
            }
        }
        catch (error) {
            this.logger.warn('Failed to parse orchestration decision JSON', error);
        }
        const lowerResponse = response.toLowerCase();
        if (lowerResponse.includes('complete') || lowerResponse.includes('done') || lowerResponse.includes('finished')) {
            return { complete: true, toolsToExecute: [] };
        }
        const toolsToExecute = [];
        for (const toolId of availableTools) {
            if (lowerResponse.includes(toolId.toLowerCase())) {
                toolsToExecute.push({
                    toolId,
                    parameters: { query: response.substring(0, 100) },
                });
            }
        }
        return {
            complete: toolsToExecute.length === 0,
            toolsToExecute,
        };
    }
    async initializeSharedContext(context, variables) {
        const contextKey = `hybrid_context:${context.executionId}:${context.nodeId}`;
        await this.sessionsService.updateSessionMemory(context.sessionId, {
            temporaryData: {
                [contextKey]: {
                    initialized: true,
                    variables,
                    timestamp: Date.now(),
                },
            },
        });
    }
    async updateSharedContext(context, updates) {
        const contextKey = `hybrid_context:${context.executionId}:${context.nodeId}`;
        const session = await this.sessionsService.getSession(context.sessionId);
        const currentContext = session?.memory?.temporaryData?.[contextKey] || {};
        await this.sessionsService.updateSessionMemory(context.sessionId, {
            temporaryData: {
                [contextKey]: {
                    ...currentContext,
                    ...updates,
                    lastUpdated: Date.now(),
                },
            },
        });
    }
    async getSharedContext(context) {
        const contextKey = `hybrid_context:${context.executionId}:${context.nodeId}`;
        const session = await this.sessionsService.getSession(context.sessionId);
        return session?.memory?.temporaryData?.[contextKey] || {};
    }
    async processSyncPoints(syncPoints, context, data) {
        for (const syncPoint of syncPoints) {
            await this.emitHybridEvent('sync_point_reached', {
                executionId: context.executionId,
                nodeId: context.nodeId,
                syncPoint,
                data,
            }, context);
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    shouldTriggerFallback(error, conditions) {
        const errorMessage = error.message.toLowerCase();
        return conditions.some(condition => {
            switch (condition) {
                case 'timeout':
                    return errorMessage.includes('timeout');
                case 'error':
                    return true;
                case 'low_confidence':
                    return errorMessage.includes('confidence') || errorMessage.includes('uncertain');
                case 'resource_limit':
                    return errorMessage.includes('limit') || errorMessage.includes('quota');
                default:
                    return false;
            }
        });
    }
    async executeFallback(config, variables, context, originalError) {
        await this.emitHybridEvent('fallback_triggered', {
            executionId: context.executionId,
            nodeId: context.nodeId,
            originalError: originalError.message,
            fallbackAgent: config.fallback.fallbackAgent,
            fallbackTools: config.fallback.fallbackTools,
        }, context);
        try {
            if (config.fallback.fallbackAgent) {
                const fallbackConfig = {
                    ...config,
                    agentId: config.fallback.fallbackAgent,
                    toolIds: config.fallback.fallbackTools,
                };
                return await this.executeAgentFirstPattern(fallbackConfig, variables, context);
            }
            if (config.fallback.fallbackTools.length > 0) {
                const toolResults = [];
                for (const toolId of config.fallback.fallbackTools) {
                    try {
                        const result = await this.executeTool(toolId, variables, {}, context);
                        toolResults.push({ toolId, result, success: true });
                    }
                    catch (error) {
                        toolResults.push({ toolId, error: error.message, success: false });
                    }
                }
                return {
                    pattern: 'fallback',
                    toolResults,
                    originalError: originalError.message,
                    timestamp: Date.now(),
                };
            }
            throw originalError;
        }
        catch (fallbackError) {
            this.logger.error('Fallback execution also failed', fallbackError);
            throw new Error(`Original error: ${originalError.message}. Fallback error: ${fallbackError.message}`);
        }
    }
    async emitHybridEvent(eventType, data, context) {
        await this.apixGateway.emitToRoom(`workflow:${context.workflowId}`, eventType, {
            ...data,
            organizationId: context.organizationId,
            timestamp: Date.now(),
        });
        await this.apixGateway.emitToOrganization(context.organizationId, eventType, {
            ...data,
            workflowId: context.workflowId,
            timestamp: Date.now(),
        });
        this.eventEmitter.emit(`hybrid.${eventType}`, {
            ...data,
            context,
        });
    }
    async storeExecutionAnalytics(context, config, result, duration) {
        try {
            await this.prisma.hybridExecutionAnalytics.create({
                data: {
                    executionId: context.executionId,
                    nodeId: context.nodeId,
                    workflowId: context.workflowId,
                    organizationId: context.organizationId,
                    agentId: config.agentId,
                    toolIds: config.toolIds,
                    executionPattern: config.executionPattern,
                    duration,
                    success: !!result,
                    errorMessage: result?.error || null,
                    resultSize: JSON.stringify(result).length,
                    metadata: {
                        maxIterations: config.maxIterations,
                        toolCount: config.toolIds.length,
                        contextSharing: config.coordination.shareContext,
                        fallbackEnabled: config.fallback.enabled,
                    },
                },
            });
        }
        catch (error) {
            this.logger.error('Failed to store execution analytics', error);
        }
    }
    getActiveExecutions() {
        return Array.from(this.activeExecutions.entries()).map(([key, execution]) => ({
            executionKey: key,
            status: execution.status,
            startTime: execution.startTime,
            duration: execution.endTime ? execution.endTime - execution.startTime : Date.now() - execution.startTime,
        }));
    }
    async cancelExecution(executionKey) {
        const execution = this.activeExecutions.get(executionKey);
        if (execution && execution.status === 'running') {
            execution.status = 'cancelled';
            execution.endTime = Date.now();
            await this.emitHybridEvent('hybrid_execution_cancelled', {
                executionKey,
                reason: 'manual_cancellation',
            }, execution.context);
            return true;
        }
        return false;
    }
    async getExecutionAnalytics(organizationId, timeRange) {
        const where = { organizationId };
        if (timeRange) {
            where.createdAt = {
                gte: timeRange.start,
                lte: timeRange.end,
            };
        }
        const analytics = await this.prisma.hybridExecutionAnalytics.findMany({
            where,
            orderBy: { createdAt: 'desc' },
            take: 1000,
        });
        return {
            totalExecutions: analytics.length,
            successRate: analytics.filter(a => a.success).length / analytics.length,
            averageDuration: analytics.reduce((sum, a) => sum + a.duration, 0) / analytics.length,
            patternDistribution: this.calculatePatternDistribution(analytics),
            errorAnalysis: this.analyzeErrors(analytics),
            performanceMetrics: this.calculatePerformanceMetrics(analytics),
        };
    }
    calculatePatternDistribution(analytics) {
        const distribution = {};
        analytics.forEach(a => {
            distribution[a.executionPattern] = (distribution[a.executionPattern] || 0) + 1;
        });
        return distribution;
    }
    analyzeErrors(analytics) {
        const errors = analytics.filter(a => !a.success);
        const errorTypes = {};
        errors.forEach(error => {
            const errorType = this.categorizeError(error.errorMessage);
            errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        });
        return {
            totalErrors: errors.length,
            errorTypes,
            errorRate: errors.length / analytics.length,
        };
    }
    categorizeError(errorMessage) {
        if (!errorMessage)
            return 'unknown';
        const message = errorMessage.toLowerCase();
        if (message.includes('timeout'))
            return 'timeout';
        if (message.includes('not found'))
            return 'not_found';
        if (message.includes('permission'))
            return 'permission';
        if (message.includes('rate limit'))
            return 'rate_limit';
        if (message.includes('network'))
            return 'network';
        return 'other';
    }
    calculatePerformanceMetrics(analytics) {
        const durations = analytics.map(a => a.duration).sort((a, b) => a - b);
        return {
            min: durations[0] || 0,
            max: durations[durations.length - 1] || 0,
            median: durations[Math.floor(durations.length / 2)] || 0,
            p95: durations[Math.floor(durations.length * 0.95)] || 0,
            p99: durations[Math.floor(durations.length * 0.99)] || 0,
        };
    }
};
exports.HybridNodeExecutorService = HybridNodeExecutorService;
exports.HybridNodeExecutorService = HybridNodeExecutorService = HybridNodeExecutorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        sessions_service_1.SessionsService,
        agents_service_1.AgentsService,
        tools_service_1.ToolsService, typeof (_a = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _a : Object])
], HybridNodeExecutorService);
//# sourceMappingURL=hybrid-node-executor.service.js.map