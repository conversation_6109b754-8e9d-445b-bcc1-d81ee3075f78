import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { AgentType, AgentStatus, Prisma } from '@prisma/client';
import { SessionMemoryService } from './session-memory.service';
import { SkillExecutorService } from './skill-executor.service';
import { ProviderRouterService } from './provider-router.service';
import { TaskTrackerService } from './task-tracker.service';
import { ApixGateway } from '../../apix/apix.gateway';


export interface CreateAgentInstanceDto {
    name: string;
    description?: string;
    templateId?: string;
    type: AgentType;

    // Configuration
    config?: any;
    systemPrompt?: string;
    instructions?: string;

    // AI Model settings
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;

    // Skills and capabilities
    skills?: string[];
    tools?: string[];
    capabilities?: any;

    // Collaboration settings
    canCollaborate?: boolean;
    shareContext?: boolean;
    priority?: number;
}

export interface UpdateAgentInstanceDto {
    name?: string;
    description?: string;
    status?: AgentStatus;
    config?: any;
    systemPrompt?: string;
    instructions?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: any;
    canCollaborate?: boolean;
    shareContext?: boolean;
    priority?: number;
}

export interface AgentExecutionContext {
    agentId: string;
    sessionId: string;
    userId?: string;
    input: any;
    context?: any;
    metadata?: any;
}

export interface AgentExecutionResult {
    success: boolean;
    output?: any;
    error?: string;
    metadata?: any;
    tokens?: number;
    cost?: number;
    duration?: number;
}

@Injectable()
export class AgentOrchestratorService {
    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private sessionMemory: SessionMemoryService,
        private skillExecutor: SkillExecutorService,
        private providerRouter: ProviderRouterService,
        private taskTracker: TaskTrackerService,
        private apixGateway: ApixGateway,
    ) { }

    // ============================================================================
    // AGENT LIFECYCLE MANAGEMENT
    // ============================================================================

    async createAgentInstance(
        createDto: CreateAgentInstanceDto,
        creatorId: string,
        organizationId: string,
    ) {
        // Validate template if provided
        if (createDto.templateId) {
            const template = await this.prisma.agentTemplate.findFirst({
                where: {
                    id: createDto.templateId,
                    OR: [
                        { isPublic: true },
                        { organizationId },
                        { createdBy: creatorId },
                    ],
                },
            });

            if (!template) {
                throw new NotFoundException('Agent template not found or not accessible');
            }

            // Merge template configuration with provided overrides
            createDto.config = {
                ...(template.config as object || {}),
                ...(createDto.config as object || {}),
            };

            if (!createDto.systemPrompt && template.systemPrompt) {
                createDto.systemPrompt = template.systemPrompt;
            }

            if (!createDto.instructions && template.instructions) {
                createDto.instructions = template.instructions;
            }

            if (!createDto.skills?.length && template.skills?.length) {
                createDto.skills = template.skills;
            }
        }

        // Create agent instance
        const agentInstance = await this.prisma.agentInstance.create({
            data: {
                name: createDto.name,
                description: createDto.description,
                templateId: createDto.templateId,
                type: createDto.type,
                status: AgentStatus.ACTIVE,

                config: createDto.config || {},
                systemPrompt: createDto.systemPrompt,
                instructions: createDto.instructions,

                provider: createDto.provider || 'openai',
                model: createDto.model || 'gpt-4',
                temperature: createDto.temperature ?? 0.7,
                maxTokens: createDto.maxTokens || 2000,

                skills: createDto.skills || [],
                tools: createDto.tools || [],
                capabilities: createDto.capabilities || {},

                canCollaborate: createDto.canCollaborate ?? false,
                shareContext: createDto.shareContext ?? false,
                priority: createDto.priority ?? 1,

                creatorId,
                organizationId,
            },
            include: {
                template: true,
                creator: true,
                organization: true,
            },
        });

        // Initialize agent memory and context
        await this.sessionMemory.initializeAgentMemory(agentInstance.id, {
            type: agentInstance.type,
            systemPrompt: agentInstance.systemPrompt,
            instructions: agentInstance.instructions,
            capabilities: agentInstance.capabilities,
        });

        // Cache agent configuration for quick access
        await this.cacheManager.set(
            `agent:${agentInstance.id}`,
            agentInstance,
            300000, // 5 minutes
        );

        // Emit real-time agent created event
        await this.apixGateway.emitAgentEvent(
            organizationId,
            agentInstance.id,
            'agent.created',
            {
                agentId: agentInstance.id,
                organizationId,
                creatorId,
                type: agentInstance.type,
                timestamp: Date.now(),
            },
            { priority: 'normal' }
        );

        return agentInstance;
    }

    async updateAgentInstance(
        agentId: string,
        updateDto: UpdateAgentInstanceDto,
        userId: string,
        organizationId: string,
    ) {
        // Check permissions
        const agent = await this.prisma.agentInstance.findFirst({
            where: {
                id: agentId,
                organizationId,
            },
        });

        if (!agent) {
            throw new NotFoundException('Agent instance not found');
        }

        // Update agent instance
        const updatedAgent = await this.prisma.agentInstance.update({
            where: { id: agentId },
            data: {
                ...updateDto,
                updatedAt: new Date(),
            },
            include: {
                template: true,
                creator: true,
                organization: true,
            },
        });

        // Update cache
        await this.cacheManager.set(
            `agent:${agentId}`,
            updatedAgent,
            300000,
        );

        // Update agent memory if configuration changed
        if (updateDto.systemPrompt || updateDto.instructions || updateDto.capabilities) {
            await this.sessionMemory.updateAgentMemory(agentId, {
                systemPrompt: updatedAgent.systemPrompt,
                instructions: updatedAgent.instructions,
                capabilities: updatedAgent.capabilities,
            });
        }

        // Emit real-time agent updated event
        await this.apixGateway.emitAgentEvent(
            organizationId,
            agentId,
            'agent.updated',
            {
                agentId,
                organizationId,
                userId,
                changes: Object.keys(updateDto),
                timestamp: Date.now(),
            },
            { priority: 'normal' }
        );

        return updatedAgent;
    }

    async deleteAgentInstance(agentId: string, userId: string, organizationId: string) {
        // Check permissions and get agent
        const agent = await this.prisma.agentInstance.findFirst({
            where: {
                id: agentId,
                organizationId,
            },
        });

        if (!agent) {
            throw new NotFoundException('Agent instance not found');
        }

        // Check if agent has active sessions or tasks
        const activeSessions = await this.prisma.agentSessionNew.count({
            where: {
                agentId,
                status: 'active',
            },
        });

        const activeTasks = await this.prisma.agentTask.count({
            where: {
                agentId,
                status: {
                    in: ['PENDING', 'RUNNING'],
                },
            },
        });

        if (activeSessions > 0 || activeTasks > 0) {
            throw new BadRequestException('Cannot delete agent with active sessions or tasks');
        }

        // Archive instead of hard delete for audit trail
        await this.prisma.agentInstance.update({
            where: { id: agentId },
            data: {
                status: AgentStatus.ARCHIVED,
                isActive: false,
            },
        });

        // Clean up cache and memory
        await this.cacheManager.del(`agent:${agentId}`);
        await this.sessionMemory.clearAgentMemory(agentId);

        // Emit agent deleted event
        await this.apixGateway.emitAgentEvent(
            organizationId,
            agentId,
            'agent.deleted',
            {
                agentId,
                organizationId,
                userId,
                timestamp: Date.now(),
            },
            { priority: 'high' }
        );

        return { success: true, message: 'Agent instance archived successfully' };
    }

    // ============================================================================
    // AGENT EXECUTION
    // ============================================================================

    async executeAgent(context: AgentExecutionContext): Promise<AgentExecutionResult> {
        const startTime = Date.now();

        try {
            // Get agent configuration
            const agent = await this.getAgentById(context.agentId);

            if (!agent || agent.status !== AgentStatus.ACTIVE) {
                throw new BadRequestException('Agent is not active or not found');
            }

            // Create or get session
            const session = await this.sessionMemory.getOrCreateSession(
                context.agentId,
                context.sessionId,
                context.userId,
            );

            // Prepare execution context
            const executionContext = {
                agent,
                session,
                input: context.input,
                context: context.context || {},
                metadata: context.metadata || {},
            };

            // Route to appropriate provider
            const provider = await this.providerRouter.getProvider(
                agent.provider,
                agent.organizationId,
            );

            // Execute based on agent type
            let result: AgentExecutionResult;

            switch (agent.type) {
                case AgentType.STANDALONE:
                    result = await this.executeStandaloneAgent(executionContext, provider);
                    break;
                case AgentType.TOOL_DRIVEN:
                    result = await this.executeToolDrivenAgent(executionContext, provider);
                    break;
                case AgentType.HYBRID:
                    result = await this.executeHybridAgent(executionContext, provider);
                    break;
                case AgentType.MULTI_TASKING:
                    result = await this.executeMultiTaskingAgent(executionContext, provider);
                    break;
                case AgentType.MULTI_PROVIDER:
                    result = await this.executeMultiProviderAgent(executionContext);
                    break;
                default:
                    throw new BadRequestException(`Unsupported agent type: ${agent.type}`);
            }

            // Calculate duration
            result.duration = Date.now() - startTime;

            // Update session memory
            await this.sessionMemory.updateSessionMemory(context.sessionId, {
                lastInput: context.input,
                lastOutput: result.output,
                lastActivity: new Date(),
                messageCount: session.messageCount + 1,
                tokenUsage: {
                    ...session.tokenUsage,
                    total: (session.tokenUsage?.total || 0) + (result.tokens || 0),
                },
            });

            // Track metrics
            await this.trackAgentMetrics(context.agentId, result);

            // Emit real-time execution event
            await this.apixGateway.emitAgentEvent(
                context.organizationId || 'system',
                context.agentId,
                'agent.executed',
                {
                    agentId: context.agentId,
                    sessionId: context.sessionId,
                    userId: context.userId,
                    success: result.success,
                    duration: result.duration,
                    tokens: result.tokens,
                    timestamp: Date.now(),
                },
                { priority: 'high' }
            );

            return result;

        } catch (error) {
            const duration = Date.now() - startTime;

            const result: AgentExecutionResult = {
                success: false,
                error: error.message,
                duration,
            };

            // Track error metrics
            await this.trackAgentMetrics(context.agentId, result);

            // Emit real-time error event
            await this.apixGateway.emitAgentEvent(
                context.organizationId || 'system',
                context.agentId,
                'agent.error',
                {
                    agentId: context.agentId,
                    sessionId: context.sessionId,
                    error: error.message,
                    duration,
                    timestamp: Date.now(),
                },
                { priority: 'high' }
            );

            return result;
        }
    }

    // ============================================================================
    // AGENT TYPE EXECUTION METHODS
    // ============================================================================

    private async executeStandaloneAgent(
        context: any,
        provider: any,
    ): Promise<AgentExecutionResult> {
        const { agent, session, input } = context;

        // Build conversation history
        const messages = await this.sessionMemory.getConversationHistory(
            session.id,
            agent.memoryWindow,
        );

        // Add system prompt if provided
        const conversationMessages = [];
        if (agent.systemPrompt) {
            conversationMessages.push({
                role: 'system',
                content: agent.systemPrompt,
            });
        }

        // Add conversation history
        conversationMessages.push(...messages);

        // Add current input
        conversationMessages.push({
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
        });

        // Call AI provider
        const response = await provider.chat({
            messages: conversationMessages,
            model: agent.model,
            temperature: agent.temperature,
            max_tokens: agent.maxTokens,
            top_p: agent.topP,
        });

        // Store conversation in session memory
        await this.sessionMemory.addMessage(session.id, {
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
            type: 'text',
            metadata: context.metadata,
        });

        await this.sessionMemory.addMessage(session.id, {
            role: 'assistant',
            content: response.content,
            type: 'text',
            tokens: response.tokens,
            cost: response.cost,
        });

        return {
            success: true,
            output: response.content,
            tokens: response.tokens,
            cost: response.cost,
            metadata: {
                model: agent.model,
                provider: agent.provider,
                messageCount: conversationMessages.length,
            },
        };
    }

    private async executeToolDrivenAgent(
        context: any,
        provider: any,
    ): Promise<AgentExecutionResult> {
        const { agent, session, input } = context;

        // Get available tools for this agent
        const tools = await this.skillExecutor.getAgentTools(agent.id);

        // Build conversation with tool definitions
        const messages = await this.sessionMemory.getConversationHistory(
            session.id,
            agent.memoryWindow,
        );

        const conversationMessages = [];
        if (agent.systemPrompt) {
            conversationMessages.push({
                role: 'system',
                content: agent.systemPrompt,
            });
        }

        conversationMessages.push(...messages);
        conversationMessages.push({
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
        });

        // Call provider with tool definitions
        const response = await provider.chatWithTools({
            messages: conversationMessages,
            tools: tools.map(tool => tool.definition),
            model: agent.model,
            temperature: agent.temperature,
            max_tokens: agent.maxTokens,
        });

        let finalOutput = response.content;
        let totalTokens = response.tokens || 0;
        let totalCost = response.cost || 0;

        // Execute any tool calls
        if (response.tool_calls?.length > 0) {
            for (const toolCall of response.tool_calls) {
                const toolResult = await this.skillExecutor.executeSkill(
                    agent.id,
                    toolCall.function.name,
                    JSON.parse(toolCall.function.arguments),
                );

                // Add tool result to conversation
                conversationMessages.push({
                    role: 'tool',
                    content: JSON.stringify(toolResult),
                    tool_call_id: toolCall.id,
                });

                // Get final response with tool results
                const finalResponse = await provider.chat({
                    messages: conversationMessages,
                    model: agent.model,
                    temperature: agent.temperature,
                    max_tokens: agent.maxTokens,
                });

                finalOutput = finalResponse.content;
                totalTokens += finalResponse.tokens || 0;
                totalCost += finalResponse.cost || 0;
            }
        }

        // Store conversation
        await this.sessionMemory.addMessage(session.id, {
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
            type: 'text',
            metadata: context.metadata,
        });

        await this.sessionMemory.addMessage(session.id, {
            role: 'assistant',
            content: finalOutput,
            type: 'text',
            tokens: totalTokens,
            cost: totalCost,
            metadata: {
                tool_calls: response.tool_calls?.length || 0,
            },
        });

        return {
            success: true,
            output: finalOutput,
            tokens: totalTokens,
            cost: totalCost,
            metadata: {
                model: agent.model,
                provider: agent.provider,
                tool_calls: response.tool_calls?.length || 0,
                tools_used: response.tool_calls?.map(tc => tc.function.name) || [],
            },
        };
    }

    private async executeHybridAgent(
        context: any,
        provider: any,
    ): Promise<AgentExecutionResult> {
        // Hybrid agents combine chat + tools + API capabilities
        // This is a more complex orchestration that can switch between modes

        const { agent, session, input } = context;

        // Analyze input to determine execution strategy
        const strategy = await this.determineExecutionStrategy(agent, input);

        switch (strategy) {
            case 'chat':
                return this.executeStandaloneAgent(context, provider);
            case 'tool':
                return this.executeToolDrivenAgent(context, provider);
            case 'hybrid':
                // Execute both and combine results
                const chatResult = await this.executeStandaloneAgent(context, provider);
                const toolResult = await this.executeToolDrivenAgent(context, provider);

                return {
                    success: true,
                    output: {
                        chat: chatResult.output,
                        tool: toolResult.output,
                        combined: `${chatResult.output}\n\nTool Result: ${toolResult.output}`,
                    },
                    tokens: (chatResult.tokens || 0) + (toolResult.tokens || 0),
                    cost: (chatResult.cost || 0) + (toolResult.cost || 0),
                    metadata: {
                        strategy: 'hybrid',
                        chat_metadata: chatResult.metadata,
                        tool_metadata: toolResult.metadata,
                    },
                };
            default:
                return this.executeStandaloneAgent(context, provider);
        }
    }

    private async executeMultiTaskingAgent(
        context: any,
        provider: any,
    ): Promise<AgentExecutionResult> {
        const { agent, session, input } = context;

        // Break down input into multiple tasks
        const tasks = await this.taskTracker.analyzeAndCreateTasks(
            agent.id,
            session.id,
            input,
        );

        const results = [];
        let totalTokens = 0;
        let totalCost = 0;

        // Execute tasks based on agent configuration
        const maxConcurrent = agent.capabilities?.maxConcurrentTasks || 3;

        for (let i = 0; i < tasks.length; i += maxConcurrent) {
            const batch = tasks.slice(i, i + maxConcurrent);

            const batchPromises = batch.map(async (task) => {
                const taskContext = {
                    ...context,
                    input: task.input,
                    metadata: { ...context.metadata, taskId: task.id },
                };

                return this.executeStandaloneAgent(taskContext, provider);
            });

            const batchResults = await Promise.allSettled(batchPromises);

            batchResults.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    results.push({
                        taskId: batch[index].id,
                        success: true,
                        output: result.value.output,
                        tokens: result.value.tokens,
                        cost: result.value.cost,
                    });
                    totalTokens += result.value.tokens || 0;
                    totalCost += result.value.cost || 0;
                } else {
                    results.push({
                        taskId: batch[index].id,
                        success: false,
                        error: result.reason?.message || 'Unknown error',
                    });
                }
            });
        }

        // Combine results
        const combinedOutput = results
            .filter(r => r.success)
            .map(r => r.output)
            .join('\n\n');

        return {
            success: results.some(r => r.success),
            output: combinedOutput,
            tokens: totalTokens,
            cost: totalCost,
            metadata: {
                tasks: results,
                total_tasks: tasks.length,
                successful_tasks: results.filter(r => r.success).length,
                failed_tasks: results.filter(r => !r.success).length,
            },
        };
    }

    private async executeMultiProviderAgent(
        context: any,
    ): Promise<AgentExecutionResult> {
        const { agent } = context;

        // Get available providers for the organization
        const providers = await this.providerRouter.getAvailableProviders(
            agent.organizationId,
        );

        // Determine best provider based on routing rules
        const selectedProvider = await this.providerRouter.selectBestProvider(
            providers,
            {
                task: context.input,
                requirements: agent.capabilities?.providerRequirements || {},
            },
        );

        // Execute with selected provider
        return this.executeStandaloneAgent(context, selectedProvider);
    }

    // ============================================================================
    // HELPER METHODS
    // ============================================================================

    private async getAgentById(agentId: string): Promise<any> {
        // Try cache first
        let agent = await this.cacheManager.get(`agent:${agentId}`) as any;

        if (!agent) {
            agent = await this.prisma.agentInstance.findUnique({
                where: { id: agentId },
                include: {
                    template: true,
                    organization: true,
                    creator: true,
                },
            });

            if (agent) {
                await this.cacheManager.set(`agent:${agentId}`, agent, 300000);
            }
        }

        return agent;
    }

    private async determineExecutionStrategy(agent: any, input: any): Promise<string> {
        // Simple strategy determination - can be enhanced with ML
        const inputText = typeof input === 'string' ? input : JSON.stringify(input);

        // Check for tool keywords
        const toolKeywords = ['calculate', 'search', 'fetch', 'analyze', 'process'];
        const hasToolKeywords = toolKeywords.some(keyword =>
            inputText.toLowerCase().includes(keyword)
        );

        // Check for conversational patterns
        const chatKeywords = ['what', 'how', 'why', 'explain', 'tell me'];
        const hasChatKeywords = chatKeywords.some(keyword =>
            inputText.toLowerCase().includes(keyword)
        );

        if (hasToolKeywords && hasChatKeywords) {
            return 'hybrid';
        } else if (hasToolKeywords) {
            return 'tool';
        } else {
            return 'chat';
        }
    }

    private async trackAgentMetrics(agentId: string, result: AgentExecutionResult) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        await this.prisma.agentMetrics.upsert({
            where: {
                agentId_date_hour: {
                    agentId,
                    date: today,
                    hour: null,
                },
            },
            update: {
                conversations: { increment: 1 },
                messages: { increment: 1 },
                totalTokens: { increment: result.tokens || 0 },
                totalCost: { increment: result.cost || 0 },
                successRate: result.success ? 1 : 0,
                errorRate: result.success ? 0 : 1,
            },
            create: {
                agentId,
                date: today,
                conversations: 1,
                messages: 1,
                totalTokens: result.tokens || 0,
                totalCost: result.cost || 0,
                successRate: result.success ? 1 : 0,
                errorRate: result.success ? 0 : 1,
            },
        });
    }

    // ============================================================================
    // PUBLIC QUERY METHODS
    // ============================================================================

    async getAgentInstances(
        organizationId: string,
        filters?: {
            type?: AgentType;
            status?: AgentStatus;
            search?: string;
            creatorId?: string;
            limit?: number;
            offset?: number;
        },
    ) {
        const where: Prisma.AgentInstanceWhereInput = {
            organizationId,
        };

        if (filters?.type) {
            where.type = filters.type;
        }

        if (filters?.status) {
            where.status = filters.status;
        }

        if (filters?.creatorId) {
            where.creatorId = filters.creatorId;
        }

        if (filters?.search) {
            where.OR = [
                { name: { contains: filters.search, mode: 'insensitive' } },
                { description: { contains: filters.search, mode: 'insensitive' } },
            ];
        }

        const [agents, total] = await Promise.all([
            this.prisma.agentInstance.findMany({
                where,
                include: {
                    template: true,
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true },
                    },
                    _count: {
                        select: {
                            sessions: true,
                            tasks: true,
                            conversations: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                take: filters?.limit || 20,
                skip: filters?.offset || 0,
            }),
            this.prisma.agentInstance.count({ where }),
        ]);

        return {
            agents,
            total,
            hasMore: (filters?.offset || 0) + agents.length < total,
        };
    }

    async getAgentInstance(agentId: string, organizationId: string) {
        const agent = await this.prisma.agentInstance.findFirst({
            where: { id: agentId, organizationId },
            include: {
                template: true,
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
                sessions: {
                    take: 5,
                    orderBy: { createdAt: 'desc' },
                },
                tasks: {
                    take: 10,
                    orderBy: { createdAt: 'desc' },
                },
                metrics: {
                    take: 30,
                    orderBy: { date: 'desc' },
                },
                _count: {
                    select: {
                        sessions: true,
                        tasks: true,
                        conversations: true,
                        collaborations: true,
                    },
                },
            },
        });

        if (!agent) {
            throw new NotFoundException('Agent instance not found');
        }

        return agent;
    }

    async getAgentMetrics(
        agentId: string,
        organizationId: string,
        dateRange?: { from: Date; to: Date },
    ) {
        // Verify agent belongs to organization
        const agent = await this.prisma.agentInstance.findFirst({
            where: { id: agentId, organizationId },
        });

        if (!agent) {
            throw new NotFoundException('Agent instance not found');
        }

        const where: Prisma.AgentMetricsWhereInput = { agentId };

        if (dateRange) {
            where.date = {
                gte: dateRange.from,
                lte: dateRange.to,
            };
        }

        return this.prisma.agentMetrics.findMany({
            where,
            orderBy: { date: 'desc' },
        });
    }
}