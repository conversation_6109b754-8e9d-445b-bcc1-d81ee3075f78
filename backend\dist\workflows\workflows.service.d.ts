import { PrismaService } from '../prisma/prisma.service';
import { ApixGateway } from '../apix/apix.gateway';
import { SessionsService } from '../sessions/sessions.service';
import { CreateWorkflowDto, UpdateWorkflowDto, ExecuteWorkflowDto } from './dto/workflow.dto';
import { FlowControllerService } from './services/flow-controller.service';
import { NodeRegistryService } from './services/node-registry.service';
import { VariableResolverService } from './services/variable-resolver.service';
export interface WorkflowNode {
    id: string;
    type: 'agent' | 'tool' | 'condition' | 'parallel' | 'human_input' | 'delay' | 'hybrid';
    name: string;
    config: any;
    position: {
        x: number;
        y: number;
    };
    inputs: string[];
    outputs: string[];
}
export interface WorkflowDefinition {
    nodes: WorkflowNode[];
    edges: Array<{
        id: string;
        source: string;
        target: string;
        condition?: string;
    }>;
    triggers: Array<{
        type: 'manual' | 'scheduled' | 'webhook' | 'event';
        config: any;
    }>;
    settings: {
        timeout?: number;
        retryPolicy?: {
            maxRetries: number;
            backoffStrategy: 'linear' | 'exponential';
            retryDelay: number;
        };
        errorHandling?: {
            onError: 'stop' | 'continue' | 'retry';
            fallbackNode?: string;
        };
    };
    variables?: Record<string, any>;
}
export declare class WorkflowsService {
    private prisma;
    private apixGateway;
    private sessionsService;
    private flowController;
    private nodeRegistry;
    private variableResolver;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, sessionsService: SessionsService, flowController: FlowControllerService, nodeRegistry: NodeRegistryService, variableResolver: VariableResolverService);
    create(userId: string, organizationId: string, createWorkflowDto: CreateWorkflowDto): Promise<{
        creator: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        version: number;
        description: string | null;
        tags: string[];
        definition: import("@prisma/client/runtime/library").JsonValue;
        creatorId: string;
    }>;
    findAll(organizationId: string, filters?: {
        isActive?: boolean;
        tags?: string[];
        creatorId?: string;
        search?: string;
    }): Promise<({
        _count: {
            executions: number;
        };
        creator: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        version: number;
        description: string | null;
        tags: string[];
        definition: import("@prisma/client/runtime/library").JsonValue;
        creatorId: string;
    })[]>;
    findOne(id: string, organizationId: string): Promise<{
        creator: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
        executions: {
            error: string;
            id: string;
            duration: number;
            status: import(".prisma/client").$Enums.ExecutionStatus;
            startedAt: Date;
            completedAt: Date;
        }[];
    } & {
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        version: number;
        description: string | null;
        tags: string[];
        definition: import("@prisma/client/runtime/library").JsonValue;
        creatorId: string;
    }>;
    update(id: string, organizationId: string, userId: string, updateWorkflowDto: UpdateWorkflowDto): Promise<{
        creator: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        version: number;
        description: string | null;
        tags: string[];
        definition: import("@prisma/client/runtime/library").JsonValue;
        creatorId: string;
    }>;
    remove(id: string, organizationId: string): Promise<void>;
    execute(id: string, organizationId: string, userId: string, executeWorkflowDto: ExecuteWorkflowDto): Promise<{
        executionId: string;
        status: import(".prisma/client").$Enums.ExecutionStatus;
        startedAt: Date;
    }>;
    getExecutions(workflowId: string, organizationId: string, limit?: number): Promise<({
        workflow: {
            id: string;
            name: string;
        };
    } & {
        error: string | null;
        id: string;
        output: import("@prisma/client/runtime/library").JsonValue | null;
        input: import("@prisma/client/runtime/library").JsonValue;
        sessionId: string;
        duration: number | null;
        status: import(".prisma/client").$Enums.ExecutionStatus;
        startedAt: Date;
        completedAt: Date | null;
        workflowId: string;
    })[]>;
    getExecution(executionId: string, organizationId: string): Promise<{
        workflow: {
            id: string;
            name: string;
        };
        steps: {
            error: string | null;
            id: string;
            name: string;
            type: string;
            output: import("@prisma/client/runtime/library").JsonValue | null;
            input: import("@prisma/client/runtime/library").JsonValue;
            duration: number | null;
            status: import(".prisma/client").$Enums.ExecutionStatus;
            startedAt: Date;
            completedAt: Date | null;
            stepId: string;
            executionId: string;
        }[];
    } & {
        error: string | null;
        id: string;
        output: import("@prisma/client/runtime/library").JsonValue | null;
        input: import("@prisma/client/runtime/library").JsonValue;
        sessionId: string;
        duration: number | null;
        status: import(".prisma/client").$Enums.ExecutionStatus;
        startedAt: Date;
        completedAt: Date | null;
        workflowId: string;
    }>;
    cancelExecution(executionId: string, organizationId: string): Promise<void>;
    pauseExecution(executionId: string, organizationId: string): Promise<void>;
    resumeExecution(executionId: string, organizationId: string): Promise<void>;
    getExecutionStatus(executionId: string, organizationId: string): Promise<{
        isActive: boolean;
        currentNode: string;
        completedNodes: string[];
        failedNodes: string[];
        variables: any;
        steps: {
            error: string | null;
            id: string;
            name: string;
            type: string;
            output: import("@prisma/client/runtime/library").JsonValue | null;
            input: import("@prisma/client/runtime/library").JsonValue;
            duration: number | null;
            status: import(".prisma/client").$Enums.ExecutionStatus;
            startedAt: Date;
            completedAt: Date | null;
            stepId: string;
            executionId: string;
        }[];
        error: string | null;
        id: string;
        output: import("@prisma/client/runtime/library").JsonValue | null;
        input: import("@prisma/client/runtime/library").JsonValue;
        sessionId: string;
        duration: number | null;
        status: import(".prisma/client").$Enums.ExecutionStatus;
        startedAt: Date;
        completedAt: Date | null;
        workflowId: string;
    }>;
    getAvailableNodeTypes(organizationId: string): Promise<{
        nodeTypes: import("./services/node-registry.service").NodeDefinition[];
        agents: {
            id: string;
            name: string;
            type: import(".prisma/client").$Enums.AgentType;
        }[];
        tools: {
            id: string;
            name: string;
            type: import(".prisma/client").$Enums.ToolType;
        }[];
    }>;
    private validateWorkflowDefinition;
}
