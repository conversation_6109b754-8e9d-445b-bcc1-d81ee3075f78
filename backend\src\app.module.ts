import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';
import { CacheModule } from '@nestjs/cache-manager';
import { EventEmitterModule } from '@nestjs/event-emitter';
import * as redisStore from 'cache-manager-redis-store';

// Core Modules
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { OrganizationsModule } from './organizations/organizations.module';

// Feature Modules
import { WorkflowsModule } from './workflows/workflows.module';
import { AgentsModule } from './agents/agents.module';
import { ToolsModule } from './tools/tools.module';
import { SessionsModule } from './sessions/sessions.module';
import { ProvidersModule } from './providers/providers.module';
import { AnalyticsModule } from './analytics/analytics.module';

// Real-time & Communication
import { ApixModule } from './apix/apix.module';
import { NotificationsModule } from './notifications/notifications.module';

// Infrastructure
import { HealthModule } from './health/health.module';
import { LoggingModule } from './logging/logging.module';
import { SecurityModule } from './security/security.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Rate Limiting
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 100,
    }),

    // Task Scheduling
    ScheduleModule.forRoot(),

    // Queue Management
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
      },
    }),

    // Caching
    CacheModule.register({
      isGlobal: true,
      store: redisStore,
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
      ttl: 300, // 5 minutes default
    }),

    // Event System
    EventEmitterModule.forRoot(),

    // Core Infrastructure
    PrismaModule,
    LoggingModule,
    SecurityModule,
    HealthModule,

    // Authentication & Authorization
    AuthModule,
    UsersModule,
    OrganizationsModule,

    // Core Features
    WorkflowsModule,
    AgentsModule,
    ToolsModule,
    SessionsModule,
    ProvidersModule,
    AnalyticsModule,

    // Real-time Communication
    ApixModule,
    NotificationsModule,
  ],
})
export class AppModule {}