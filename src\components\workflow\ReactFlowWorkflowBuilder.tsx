"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Connection,
  EdgeChange,
  NodeChange,
  ReactFlowProvider,
  Panel,
  useReactFlow,
  MarkerType,
  ConnectionMode,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Save,
  Play,
  Settings,
  Workflow,
  Bot,
  Wrench,
  GitBranch,
  Split,
  User,
  Clock,
  Layers,
  Activity,
  CheckCircle,
  XCircle,
  MessageSquare,
  Search,
  Plus,
  Trash2,
  Copy,
  Download,
  Upload,
  Zap,
  Database,
  Code,
  ArrowRight,
  ArrowDown,
} from 'lucide-react';

import apiClient from '@/lib/api-client';
import apixClient from '@/lib/apix-client';

// Custom Node Components
import AgentNode from './nodes/AgentNode';
import ToolNode from './nodes/ToolNode';
import ConditionNode from './nodes/ConditionNode';
import ParallelNode from './nodes/ParallelNode';
import HumanInputNode from './nodes/HumanInputNode';
import DelayNode from './nodes/DelayNode';
import HybridNode from './nodes/HybridNode';
import StartNode from './nodes/StartNode';
import EndNode from './nodes/EndNode';

// Node Configuration Panels
import ToolNodeConfigPanel from './node-config/ToolNodeConfigPanel';
import ConditionNodeConfigPanel from './node-config/ConditionNodeConfigPanel';
import ParallelNodeConfigPanel from './node-config/ParallelNodeConfigPanel';
import HumanInputNodeConfigPanel from './node-config/HumanInputNodeConfigPanel';
import DelayNodeConfigPanel from './node-config/DelayNodeConfigPanel';
import HybridNodeConfigPanel from './node-config/HybridNodeConfigPanel';

// Define node types
const nodeTypes = {
  agent: AgentNode,
  tool: ToolNode,
  condition: ConditionNode,
  parallel: ParallelNode,
  humanInput: HumanInputNode,
  delay: DelayNode,
  hybrid: HybridNode,
  start: StartNode,
  end: EndNode,
};

// Define edge types with custom styling
const edgeOptions = {
  animated: true,
  style: {
    stroke: '#b1b1b7',
    strokeWidth: 2,
  },
  markerEnd: {
    type: MarkerType.ArrowClosed,
    color: '#b1b1b7',
  },
};

interface WorkflowBuilderProps {
  workflowId?: string;
  initialNodes?: Node[];
  initialEdges?: Edge[];
  readOnly?: boolean;
}

const ReactFlowWorkflowBuilder: React.FC<WorkflowBuilderProps> = ({
  workflowId = '',
  initialNodes = [],
  initialEdges = [],
  readOnly = false,
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes.length > 0 ? initialNodes : [
    {
      id: 'start-1',
      type: 'start',
      position: { x: 250, y: 50 },
      data: { 
        label: 'Start',
        description: 'Workflow entry point',
        config: {},
        status: 'idle',
        isConfigValid: true,
      },
    },
  ]);
  
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [workflowName, setWorkflowName] = useState(workflowId ? `Workflow ${workflowId}` : 'New Workflow');
  const [activeTab, setActiveTab] = useState('canvas');
  const [searchTerm, setSearchTerm] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [nodeStatuses, setNodeStatuses] = useState<Record<string, any>>({});
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  
  const { toast } = useToast();
  const reactFlowInstance = useReactFlow();

  // Node palette configuration
  const nodeCategories = [
    {
      title: 'Agents',
      nodes: [
        { type: 'agent', label: 'Agent', icon: Bot, description: 'AI agent for processing' },
      ],
    },
    {
      title: 'Tools',
      nodes: [
        { type: 'tool', label: 'Generic Tool', icon: Wrench, description: 'Generic tool execution' },
        { type: 'tool', label: 'Search Tool', icon: Search, description: 'Search and retrieval' },
        { type: 'tool', label: 'Database Tool', icon: Database, description: 'Database operations' },
        { type: 'tool', label: 'Code Tool', icon: Code, description: 'Code execution' },
      ],
    },
    {
      title: 'Flow Control',
      nodes: [
        { type: 'condition', label: 'Condition', icon: GitBranch, description: 'Conditional branching' },
        { type: 'parallel', label: 'Parallel', icon: Split, description: 'Parallel execution' },
        { type: 'delay', label: 'Delay', icon: Clock, description: 'Time delay' },
        { type: 'humanInput', label: 'Human Input', icon: User, description: 'Human intervention' },
      ],
    },
    {
      title: 'Advanced',
      nodes: [
        { type: 'hybrid', label: 'Hybrid Agent-Tool', icon: Layers, description: 'Hybrid execution' },
      ],
    },
    {
      title: 'Input/Output',
      nodes: [
        { type: 'start', label: 'Start', icon: ArrowDown, description: 'Workflow start' },
        { type: 'end', label: 'End', icon: ArrowRight, description: 'Workflow end' },
      ],
    },
  ];

  // Handle node connection
  const onConnect = useCallback(
    (params: Connection) => {
      const edge = {
        ...params,
        ...edgeOptions,
        id: `edge-${params.source}-${params.target}`,
      };
      setEdges((eds) => addEdge(edge, eds));
    },
    [setEdges]
  );

  // Handle node selection
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setSelectedEdge(null);
    setActiveTab('properties');
  }, []);

  // Handle edge selection
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    setSelectedEdge(edge);
    setSelectedNode(null);
    setActiveTab('properties');
  }, []);

  // Add new node
  const addNode = useCallback((type: string, label: string) => {
    const id = `${type}-${Date.now()}`;
    const position = reactFlowInstance.project({
      x: Math.random() * 400 + 100,
      y: Math.random() * 400 + 100,
    });

    const newNode: Node = {
      id,
      type,
      position,
      data: {
        label,
        description: `New ${label.toLowerCase()}`,
        config: getDefaultConfigForType(type),
        status: 'idle',
        isConfigValid: type === 'start' || type === 'end',
      },
    };

    setNodes((nds) => [...nds, newNode]);
    setSelectedNode(newNode);
    setActiveTab('properties');
  }, [reactFlowInstance, setNodes]);

  // Get default configuration for node type
  const getDefaultConfigForType = (type: string) => {
    switch (type) {
      case 'agent':
        return { agentId: '', systemPrompt: '', maxTokens: 2000, temperature: 0.7 };
      case 'tool':
        return { toolId: '', parameters: {}, timeout: 30000 };
      case 'condition':
        return { condition: '', conditionType: 'simple', truePath: '', falsePath: '' };
      case 'parallel':
        return { nodes: [], executionMode: 'all', aggregateResults: true };
      case 'humanInput':
        return { prompt: 'Please provide input', inputType: 'text', timeout: 300000 };
      case 'delay':
        return { delay: 1000, unit: 'milliseconds' };
      case 'hybrid':
        return { 
          agentId: '', 
          toolIds: [], 
          executionPattern: 'agent-first',
          maxIterations: 5 
        };
      default:
        return {};
    }
  };

  // Update node data
  const updateNodeData = useCallback((nodeId: string, updates: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                ...updates,
              },
            }
          : node
      )
    );
  }, [setNodes]);

  // Update node configuration
  const updateNodeConfig = useCallback((nodeId: string, config: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                config: {
                  ...node.data.config,
                  ...config,
                },
              },
            }
          : node
      )
    );
  }, [setNodes]);

  // Delete selected node
  const deleteSelectedNode = useCallback(() => {
    if (!selectedNode) return;

    setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id));
    setEdges((eds) => eds.filter((edge) => 
      edge.source !== selectedNode.id && edge.target !== selectedNode.id
    ));
    setSelectedNode(null);
  }, [selectedNode, setNodes, setEdges]);

  // Delete selected edge
  const deleteSelectedEdge = useCallback(() => {
    if (!selectedEdge) return;

    setEdges((eds) => eds.filter((edge) => edge.id !== selectedEdge.id));
    setSelectedEdge(null);
  }, [selectedEdge, setEdges]);

  // Duplicate selected node
  const duplicateSelectedNode = useCallback(() => {
    if (!selectedNode) return;

    const newNode: Node = {
      ...selectedNode,
      id: `${selectedNode.type}-${Date.now()}`,
      position: {
        x: selectedNode.position.x + 50,
        y: selectedNode.position.y + 50,
      },
    };

    setNodes((nds) => [...nds, newNode]);
    setSelectedNode(newNode);
  }, [selectedNode, setNodes]);

  // Workflow validation
  const validateWorkflow = useCallback(() => {
    const errors: string[] = [];

    if (nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }

    // Check for start node
    const startNodes = nodes.filter(node => node.type === 'start');
    if (startNodes.length === 0) {
      errors.push('Workflow must have a start node');
    }

    // Check for unconnected nodes (except start and end)
    const connectedNodes = new Set<string>();
    edges.forEach(edge => {
      connectedNodes.add(edge.source);
      connectedNodes.add(edge.target);
    });

    const unconnectedNodes = nodes.filter(node => 
      node.type !== 'start' && node.type !== 'end' && !connectedNodes.has(node.id)
    );

    if (unconnectedNodes.length > 0) {
      errors.push(`Unconnected nodes: ${unconnectedNodes.map(n => n.data.label).join(', ')}`);
    }

    // Check for invalid configurations
    const invalidNodes = nodes.filter(node => !node.data.isConfigValid);
    if (invalidNodes.length > 0) {
      errors.push(`Invalid configuration: ${invalidNodes.map(n => n.data.label).join(', ')}`);
    }

    return errors;
  }, [nodes, edges]);

  // Save workflow
  const handleSaveWorkflow = useCallback(async () => {
    if (isSaving) return;

    try {
      setIsSaving(true);

      const validationErrors = validateWorkflow();
      if (validationErrors.length > 0) {
        toast({
          title: 'Validation Error',
          description: validationErrors.join(', '),
          variant: 'destructive',
        });
        return;
      }

      const workflowData = {
        name: workflowName,
        description: `Workflow with ${nodes.length} nodes`,
        definition: {
          nodes: nodes.map(node => ({
            id: node.id,
            type: node.type,
            name: node.data.label,
            config: node.data.config || {},
            position: node.position,
            data: node.data,
          })),
          edges: edges.map(edge => ({
            id: edge.id,
            source: edge.source,
            target: edge.target,
            sourceHandle: edge.sourceHandle,
            targetHandle: edge.targetHandle,
            data: edge.data,
          })),
          viewport: reactFlowInstance.getViewport(),
        },
        tags: ['react-flow-builder'],
      };

      let savedWorkflow;
      if (workflowId) {
        savedWorkflow = await apiClient.updateWorkflow(workflowId, workflowData);
        toast({
          title: 'Success',
          description: 'Workflow updated successfully',
        });
      } else {
        savedWorkflow = await apiClient.createWorkflow(workflowData);
        toast({
          title: 'Success',
          description: 'Workflow created successfully',
        });
      }

      console.log('Workflow saved:', savedWorkflow);
    } catch (error) {
      console.error('Failed to save workflow:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save workflow',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, [workflowName, nodes, edges, workflowId, isSaving, toast, reactFlowInstance, validateWorkflow]);

  // Execute workflow
  const handleRunWorkflow = useCallback(async () => {
    if (isExecuting) return;

    try {
      setIsExecuting(true);
      setNodeStatuses({});

      const validationErrors = validateWorkflow();
      if (validationErrors.length > 0) {
        toast({
          title: 'Validation Error',
          description: validationErrors.join(', '),
          variant: 'destructive',
        });
        return;
      }

      // Save workflow first if needed
      if (!workflowId) {
        await handleSaveWorkflow();
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const currentWorkflowId = workflowId || 'temp-workflow-id';

      // Subscribe to workflow events
      apixClient.subscribeToWorkflow(currentWorkflowId);

      // Set up event listeners
      const unsubscribeEvents = apixClient.on('*', (event) => {
        if (event.data?.workflowId === currentWorkflowId || event.data?.executionId === executionId) {
          handleWorkflowEvent(event);
        }
      });

      // Start execution
      const executionResponse = await apiClient.executeWorkflow(currentWorkflowId, {
        input: {},
        options: { async: true, priority: 'normal' },
      });

      setExecutionId(executionResponse.executionId);

      toast({
        title: 'Execution Started',
        description: `Workflow execution started with ID: ${executionResponse.executionId}`,
      });

      // Cleanup after timeout
      setTimeout(() => {
        unsubscribeEvents();
        apixClient.unsubscribeFromWorkflow(currentWorkflowId);
      }, 300000);

    } catch (error) {
      console.error('Failed to execute workflow:', error);
      toast({
        title: 'Execution Error',
        description: error instanceof Error ? error.message : 'Failed to execute workflow',
        variant: 'destructive',
      });
      setIsExecuting(false);
    }
  }, [workflowId, nodes, edges, isExecuting, executionId, handleSaveWorkflow, toast, validateWorkflow]);

  // Handle workflow events
  const handleWorkflowEvent = useCallback((event: any) => {
    const { type, data } = event;

    switch (type) {
      case 'workflow_started':
        setNodeStatuses(prev => ({
          ...prev,
          [data.startNodeId]: { status: 'running', startTime: Date.now() },
        }));
        break;

      case 'workflow_completed':
        setIsExecuting(false);
        toast({
          title: 'Workflow Completed',
          description: 'Workflow execution completed successfully',
        });
        break;

      case 'workflow_failed':
        setIsExecuting(false);
        toast({
          title: 'Workflow Failed',
          description: data.error || 'Workflow execution failed',
          variant: 'destructive',
        });
        break;

      case 'tool_call_start':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: { 
              status: 'running', 
              startTime: Date.now(),
              toolId: data.toolId,
            },
          }));
          updateNodeData(data.nodeId, { status: 'running' });
        }
        break;

      case 'tool_call_result':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: { 
              ...prev[data.nodeId],
              status: 'success', 
              result: data.result,
              endTime: Date.now(),
            },
          }));
          updateNodeData(data.nodeId, { status: 'success', lastOutput: data.result });
        }
        break;

      case 'tool_call_error':
        if (data.nodeId) {
          setNodeStatuses(prev => ({
            ...prev,
            [data.nodeId]: { 
              ...prev[data.nodeId],
              status: 'error', 
              error: data.error,
            },
          }));
          updateNodeData(data.nodeId, { status: 'error', errorMessage: data.error });
        }
        break;

      case 'agent_thinking':
        if (data.nodeId) {
          updateNodeData(data.nodeId, { 
            status: 'thinking', 
            progress: data.progress,
            lastOutput: data.context 
          });
        }
        break;

      case 'agent_response':
        if (data.nodeId) {
          updateNodeData(data.nodeId, { 
            status: 'success', 
            lastOutput: data.response 
          });
        }
        break;

      default:
        console.log('Unhandled workflow event:', type, data);
    }
  }, [toast, updateNodeData]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            handleSaveWorkflow();
            break;
          case 'r':
            event.preventDefault();
            handleRunWorkflow();
            break;
          case 'd':
            if (selectedNode) {
              event.preventDefault();
              duplicateSelectedNode();
            }
            break;
        }
      }
      
      if (event.key === 'Delete' || event.key === 'Backspace') {
        if (selectedNode) {
          deleteSelectedNode();
        } else if (selectedEdge) {
          deleteSelectedEdge();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleSaveWorkflow, handleRunWorkflow, selectedNode, selectedEdge, duplicateSelectedNode, deleteSelectedNode, deleteSelectedEdge]);

  // Configuration panel component
  const renderConfigPanel = () => {
    if (!selectedNode) return null;

    const commonProps = {
      nodeId: selectedNode.id,
      initialConfig: selectedNode.data.config,
      onConfigChange: (config: any) => {
        updateNodeConfig(selectedNode.id, config);
      },
      onValidationChange: (isValid: boolean) => {
        updateNodeData(selectedNode.id, { isConfigValid: isValid });
      },
    };

    switch (selectedNode.type) {
      case 'tool':
        return <ToolNodeConfigPanel {...commonProps} />;
      case 'condition':
        return <ConditionNodeConfigPanel {...commonProps} />;
      case 'parallel':
        return <ParallelNodeConfigPanel {...commonProps} />;
      case 'humanInput':
        return <HumanInputNodeConfigPanel {...commonProps} />;
      case 'delay':
        return <DelayNodeConfigPanel {...commonProps} />;
      case 'hybrid':
        return <HybridNodeConfigPanel {...commonProps} />;
      default:
        return (
          <Card>
            <CardHeader>
              <CardTitle>Node Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <p>No specific configuration available for this node type.</p>
            </CardContent>
          </Card>
        );
    }
  };

  return (
    <div className="flex flex-col h-full w-full bg-background">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center space-x-4">
          <Workflow className="h-6 w-6" />
          <Input
            value={workflowName}
            onChange={(e) => setWorkflowName(e.target.value)}
            className="w-64 h-9 text-lg font-medium"
            disabled={readOnly}
          />
          <Badge variant="outline">
            {workflowId ? "Saved" : "Draft"}
          </Badge>
          {isExecuting && (
            <Badge variant="default" className="bg-blue-100 text-blue-800">
              <Activity className="h-3 w-3 mr-1 animate-pulse" />
              Executing
            </Badge>
          )}
          <Badge variant="secondary">
            {nodes.length} nodes, {edges.length} connections
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          {!readOnly && (
            <>
              <Button 
                variant="outline" 
                onClick={handleSaveWorkflow}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
              <Button 
                onClick={handleRunWorkflow}
                disabled={isExecuting || nodes.length === 0}
              >
                {isExecuting ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Run
                  </>
                )}
              </Button>
            </>
          )}
          <Button 
            variant="outline"
            onClick={() => setShowConfigPanel(!showConfigPanel)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar - Node Palette */}
        <div className="w-64 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="p-4">
            <div className="relative mb-4">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search nodes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4">
                {nodeCategories.map((category) => (
                  <div key={category.title}>
                    <h3 className="text-sm font-medium mb-2 text-muted-foreground">
                      {category.title}
                    </h3>
                    <div className="grid grid-cols-1 gap-2">
                      {category.nodes
                        .filter(node => 
                          !searchTerm || 
                          node.label.toLowerCase().includes(searchTerm.toLowerCase())
                        )
                        .map((node, index) => (
                          <Button
                            key={`${node.type}-${index}`}
                            variant="outline"
                            className="justify-start h-auto p-3"
                            onClick={() => addNode(node.type, node.label)}
                            disabled={readOnly}
                          >
                            <div className="flex items-center space-x-2 w-full">
                              <node.icon className="h-4 w-4 flex-shrink-0" />
                              <div className="flex-1 text-left">
                                <div className="font-medium text-sm">{node.label}</div>
                                <div className="text-xs text-muted-foreground">
                                  {node.description}
                                </div>
                              </div>
                            </div>
                          </Button>
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Main Canvas */}
        <div className="flex-1 relative">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onEdgeClick={onEdgeClick}
            nodeTypes={nodeTypes}
            connectionMode={ConnectionMode.Loose}
            defaultEdgeOptions={edgeOptions}
            fitView
            attributionPosition="bottom-left"
            className="bg-background"
          >
            <Background 
              variant={BackgroundVariant.Dots} 
              gap={20} 
              size={1}
              color="#e2e8f0"
            />
            <Controls 
              position="bottom-right"
              className="bg-background border border-border rounded-lg shadow-lg"
            />
            <MiniMap 
              position="bottom-left"
              className="bg-background border border-border rounded-lg shadow-lg"
              nodeColor={(node) => {
                switch (node.data?.status) {
                  case 'running': return '#3b82f6';
                  case 'success': return '#10b981';
                  case 'error': return '#ef4444';
                  case 'thinking': return '#8b5cf6';
                  default: return '#6b7280';
                }
              }}
            />
            
            {/* Top Panel with workflow stats */}
            <Panel position="top-center" className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border border-border rounded-lg shadow-lg p-2">
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Ready</span>
                </div>
                <Separator orientation="vertical" className="h-4" />
                <span>{nodes.length} nodes</span>
                <Separator orientation="vertical" className="h-4" />
                <span>{edges.length} connections</span>
                {isExecuting && (
                  <>
                    <Separator orientation="vertical" className="h-4" />
                    <div className="flex items-center space-x-1">
                      <Activity className="h-3 w-3 animate-pulse" />
                      <span>Executing...</span>
                    </div>
                  </>
                )}
              </div>
            </Panel>
          </ReactFlow>
        </div>

        {/* Right Sidebar - Configuration Panel */}
        {showConfigPanel && (
          <div className="w-96 border-l bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 overflow-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Configuration</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowConfigPanel(false)}
                >
                  ×
                </Button>
              </div>
              
              {selectedNode ? (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{selectedNode.type}</Badge>
                    <span className="font-medium">{selectedNode.data.label}</span>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Label</label>
                    <Input
                      value={selectedNode.data.label}
                      onChange={(e) => updateNodeData(selectedNode.id, { label: e.target.value })}
                      disabled={readOnly}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Description</label>
                    <Input
                      value={selectedNode.data.description || ''}
                      onChange={(e) => updateNodeData(selectedNode.id, { description: e.target.value })}
                      disabled={readOnly}
                    />
                  </div>
                  
                  <Separator />
                  
                  {renderConfigPanel()}
                  
                  <Separator />
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={duplicateSelectedNode}
                      disabled={readOnly}
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Duplicate
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={deleteSelectedNode}
                      disabled={readOnly}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              ) : selectedEdge ? (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">Edge</Badge>
                    <span className="font-medium">Connection</span>
                  </div>
                  
                  <Separator />
                  
                  <div className="text-sm">
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">From:</span>
                      <span>{selectedEdge.source}</span>
                    </div>
                    <div className="flex justify-between py-1">
                      <span className="text-muted-foreground">To:</span>
                      <span>{selectedEdge.target}</span>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={deleteSelectedEdge}
                    disabled={readOnly}
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete Connection
                  </Button>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a node or connection to configure</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Wrapper component with ReactFlowProvider
const ReactFlowWorkflowBuilderWrapper: React.FC<WorkflowBuilderProps> = (props) => {
  return (
    <ReactFlowProvider>
      <ReactFlowWorkflowBuilder {...props} />
    </ReactFlowProvider>
  );
};

export default ReactFlowWorkflowBuilderWrapper;