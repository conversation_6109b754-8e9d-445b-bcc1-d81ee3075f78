import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { CheckCircle, XCircle, AlertTriangle, Layers, Bot, Wrench, Plus, Trash2 } from 'lucide-react';
import apiClient from '@/lib/api-client';
import { useToast } from '@/components/ui/use-toast';

const hybridConfigSchema = z.object({
  agentId: z.string().min(1, 'Agent selection is required'),
  toolIds: z.array(z.string()).min(1, 'At least one tool is required'),
  executionPattern: z.enum(['agent-first', 'tool-first', 'parallel', 'multi-tool-orchestration']),
  maxIterations: z.number().min(1).max(20),
  agentConfig: z.object({
    systemPrompt: z.string().optional(),
    maxTokens: z.number().min(1).max(8000),
    temperature: z.number().min(0).max(2),
    contextWindow: z.number().min(1000).max(32000),
  }),
  toolConfigs: z.record(z.string(), z.object({
    timeout: z.number().min(1000).max(300000),
    retryPolicy: z.object({
      enabled: z.boolean(),
      maxRetries: z.number().min(0).max(5),
    }),
    parameters: z.record(z.string(), z.any()),
  })),
  coordination: z.object({
    shareContext: z.boolean(),
    contextStrategy: z.enum(['full', 'summary', 'selective']),
    syncPoints: z.array(z.string()),
    errorPropagation: z.boolean(),
  }),
  performance: z.object({
    parallelLimit: z.number().min(1).max(10),
    timeout: z.number().min(10000).max(600000),
    memoryLimit: z.number().min(1).max(100),
  }),
  fallback: z.object({
    enabled: z.boolean(),
    fallbackAgent: z.string().optional(),
    fallbackTools: z.array(z.string()),
    conditions: z.array(z.string()),
  }),
});

type HybridConfigFormData = z.infer<typeof hybridConfigSchema>;

interface HybridNodeConfigPanelProps {
  nodeId: string;
  initialConfig?: Partial<HybridConfigFormData>;
  onConfigChange: (config: HybridConfigFormData) => void;
  onValidationChange: (isValid: boolean) => void;
}

interface Agent {
  id: string;
  name: string;
  type: string;
  description?: string;
}

interface Tool {
  id: string;
  name: string;
  type: string;
  description?: string;
}

export default function HybridNodeConfigPanel({
  nodeId,
  initialConfig = {},
  onConfigChange,
  onValidationChange,
}: HybridNodeConfigPanelProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [tools, setTools] = useState<Tool[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [selectedTools, setSelectedTools] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
    trigger,
  } = useForm<HybridConfigFormData>({
    resolver: zodResolver(hybridConfigSchema),
    defaultValues: {
      agentId: '',
      toolIds: [],
      executionPattern: 'agent-first',
      maxIterations: 5,
      agentConfig: {
        systemPrompt: '',
        maxTokens: 2000,
        temperature: 0.7,
        contextWindow: 8000,
      },
      toolConfigs: {},
      coordination: {
        shareContext: true,
        contextStrategy: 'full',
        syncPoints: [],
        errorPropagation: true,
      },
      performance: {
        parallelLimit: 3,
        timeout: 120000,
        memoryLimit: 50,
      },
      fallback: {
        enabled: true,
        fallbackAgent: '',
        fallbackTools: [],
        conditions: ['timeout', 'error', 'low_confidence'],
      },
      ...initialConfig,
    },
    mode: 'onChange',
  });

  const watchedValues = watch();
  const selectedAgentId = watch('agentId');
  const selectedToolIds = watch('toolIds');
  const executionPattern = watch('executionPattern');

  // Load available agents and tools
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [agentsResponse, toolsResponse] = await Promise.all([
          apiClient.getAgents(),
          apiClient.getTools(),
        ]);
        setAgents(agentsResponse.agents || []);
        setTools(toolsResponse.tools || []);
      } catch (error) {
        console.error('Failed to load agents/tools:', error);
        toast({
          title: 'Error',
          description: 'Failed to load available agents and tools',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [toast]);

  // Update selected agent when agentId changes
  useEffect(() => {
    if (selectedAgentId) {
      const agent = agents.find(a => a.id === selectedAgentId);
      setSelectedAgent(agent || null);
    }
  }, [selectedAgentId, agents]);

  // Update selected tools when toolIds change
  useEffect(() => {
    const selected = tools.filter(t => selectedToolIds.includes(t.id));
    setSelectedTools(selected);
  }, [selectedToolIds, tools]);

  // Debounced config change handler
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (isValid) {
        onConfigChange(watchedValues);
        setValidationErrors([]);
      } else {
        const errorMessages = Object.values(errors).map(error => error.message).filter(Boolean) as string[];
        setValidationErrors(errorMessages);
      }
      onValidationChange(isValid);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, isValid, errors, onConfigChange, onValidationChange]);

  const handleToolSelection = (toolId: string, checked: boolean) => {
    const currentTools = watch('toolIds') || [];
    if (checked) {
      setValue('toolIds', [...currentTools, toolId]);
      // Initialize tool config
      const currentConfigs = watch('toolConfigs') || {};
      setValue('toolConfigs', {
        ...currentConfigs,
        [toolId]: {
          timeout: 30000,
          retryPolicy: { enabled: true, maxRetries: 2 },
          parameters: {},
        },
      });
    } else {
      setValue('toolIds', currentTools.filter(id => id !== toolId));
      // Remove tool config
      const currentConfigs = watch('toolConfigs') || {};
      const { [toolId]: removed, ...remaining } = currentConfigs;
      setValue('toolConfigs', remaining);
    }
  };

  const updateToolConfig = (toolId: string, field: string, value: any) => {
    const currentConfigs = watch('toolConfigs') || {};
    const toolConfig = currentConfigs[toolId] || {};
    setValue('toolConfigs', {
      ...currentConfigs,
      [toolId]: { ...toolConfig, [field]: value },
    });
  };

  const getExecutionPatternDescription = (pattern: string) => {
    switch (pattern) {
      case 'agent-first':
        return 'Agent processes input first, then uses tools based on its analysis';
      case 'tool-first':
        return 'Tools gather data first, then agent processes the results';
      case 'parallel':
        return 'Agent and tools execute simultaneously with coordination';
      case 'multi-tool-orchestration':
        return 'Agent coordinates multiple tools in sequence or parallel';
      default:
        return '';
    }
  };

  const addSyncPoint = () => {
    const currentSyncPoints = watch('coordination.syncPoints') || [];
    const newSyncPoint = `sync_${currentSyncPoints.length + 1}`;
    setValue('coordination.syncPoints', [...currentSyncPoints, newSyncPoint]);
  };

  const removeSyncPoint = (index: number) => {
    const currentSyncPoints = watch('coordination.syncPoints') || [];
    setValue('coordination.syncPoints', currentSyncPoints.filter((_, i) => i !== index));
  };

  return (
    <div className="bg-background min-h-screen p-8">
      <Card className="w-full max-w-6xl mx-auto">
        <CardHeader className="flex flex-row items-center space-y-0 pb-4">
          <div className="flex items-center space-x-2">
            <Layers className="h-5 w-5" />
            <CardTitle>Hybrid Agent-Tool Configuration</CardTitle>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            {isValid ? (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Valid
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Invalid
              </Badge>
            )}
            <Badge variant="outline">
              {selectedTools.length} Tools Selected
            </Badge>
          </div>
        </CardHeader>

        <CardContent>
          {validationErrors.length > 0 && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="basic">Basic</TabsTrigger>
              <TabsTrigger value="agent">Agent Config</TabsTrigger>
              <TabsTrigger value="tools">Tool Config</TabsTrigger>
              <TabsTrigger value="coordination">Coordination</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="fallback">Fallback</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="agentId">Primary Agent *</Label>
                  <Select
                    value={watch('agentId')}
                    onValueChange={(value) => setValue('agentId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select an agent" />
                    </SelectTrigger>
                    <SelectContent>
                      {agents.map((agent) => (
                        <SelectItem key={agent.id} value={agent.id}>
                          <div className="flex items-center space-x-2">
                            <Bot className="h-4 w-4" />
                            <span>{agent.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {agent.type}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.agentId && (
                    <p className="text-sm text-red-600">{errors.agentId.message}</p>
                  )}
                </div>

                {selectedAgent && (
                  <div className="p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Selected Agent</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      {selectedAgent.description || 'No description available'}
                    </p>
                    <div className="flex items-center space-x-4 text-xs">
                      <span>Type: {selectedAgent.type}</span>
                      <span>ID: {selectedAgent.id}</span>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Execution Pattern</Label>
                  <Select
                    value={watch('executionPattern')}
                    onValueChange={(value: any) => setValue('executionPattern', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="agent-first">Agent First</SelectItem>
                      <SelectItem value="tool-first">Tool First</SelectItem>
                      <SelectItem value="parallel">Parallel</SelectItem>
                      <SelectItem value="multi-tool-orchestration">Multi-Tool Orchestration</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {getExecutionPatternDescription(executionPattern)}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Max Iterations</Label>
                  <div className="space-y-2">
                    <Slider
                      value={[watch('maxIterations') || 5]}
                      onValueChange={([value]) => setValue('maxIterations', value)}
                      min={1}
                      max={20}
                      step={1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      {watch('maxIterations')} iterations maximum
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <Label>Available Tools</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {tools.map((tool) => (
                      <div key={tool.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                        <Checkbox
                          checked={selectedToolIds.includes(tool.id)}
                          onCheckedChange={(checked) => handleToolSelection(tool.id, checked as boolean)}
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <Wrench className="h-4 w-4" />
                            <span className="font-medium">{tool.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {tool.type}
                            </Badge>
                          </div>
                          {tool.description && (
                            <p className="text-xs text-muted-foreground mt-1">
                              {tool.description}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  {errors.toolIds && (
                    <p className="text-sm text-red-600">{errors.toolIds.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="agent" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Agent Configuration</h4>

                  <div className="space-y-2">
                    <Label>System Prompt</Label>
                    <Textarea
                      value={watch('agentConfig.systemPrompt') || ''}
                      onChange={(e) => setValue('agentConfig.systemPrompt', e.target.value)}
                      rows={4}
                      placeholder="System prompt for the agent..."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Max Tokens</Label>
                      <Slider
                        value={[watch('agentConfig.maxTokens') || 2000]}
                        onValueChange={([value]) => setValue('agentConfig.maxTokens', value)}
                        min={1}
                        max={8000}
                        step={100}
                        className="w-full"
                      />
                      <div className="text-xs text-muted-foreground">
                        {watch('agentConfig.maxTokens')} tokens
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Temperature</Label>
                      <Slider
                        value={[watch('agentConfig.temperature') || 0.7]}
                        onValueChange={([value]) => setValue('agentConfig.temperature', value)}
                        min={0}
                        max={2}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="text-xs text-muted-foreground">
                        {watch('agentConfig.temperature')} creativity
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Context Window</Label>
                    <Slider
                      value={[watch('agentConfig.contextWindow') || 8000]}
                      onValueChange={([value]) => setValue('agentConfig.contextWindow', value)}
                      min={1000}
                      max={32000}
                      step={1000}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      {watch('agentConfig.contextWindow')} tokens context window
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="tools" className="space-y-6">
              <div className="space-y-6">
                <h4 className="font-medium">Tool Configurations</h4>

                {selectedTools.length > 0 ? (
                  <div className="space-y-4">
                    {selectedTools.map((tool) => (
                      <Card key={tool.id} className="p-4">
                        <div className="flex items-center space-x-2 mb-4">
                          <Wrench className="h-4 w-4" />
                          <h5 className="font-medium">{tool.name}</h5>
                          <Badge variant="outline">{tool.type}</Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Timeout (ms)</Label>
                            <Slider
                              value={[watch(`toolConfigs.${tool.id}.timeout`) || 30000]}
                              onValueChange={([value]) => updateToolConfig(tool.id, 'timeout', value)}
                              min={1000}
                              max={300000}
                              step={1000}
                              className="w-full"
                            />
                            <div className="text-xs text-muted-foreground">
                              {Math.round((watch(`toolConfigs.${tool.id}.timeout`) || 30000) / 1000)}s timeout
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>Max Retries</Label>
                            <Slider
                              value={[watch(`toolConfigs.${tool.id}.retryPolicy.maxRetries`) || 2]}
                              onValueChange={([value]) => updateToolConfig(tool.id, 'retryPolicy', {
                                ...watch(`toolConfigs.${tool.id}.retryPolicy`),
                                maxRetries: value,
                              })}
                              min={0}
                              max={5}
                              step={1}
                              className="w-full"
                            />
                            <div className="text-xs text-muted-foreground">
                              {watch(`toolConfigs.${tool.id}.retryPolicy.maxRetries`) || 2} retries
                            </div>
                          </div>
                        </div>

                        <div className="mt-4 space-y-2">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={watch(`toolConfigs.${tool.id}.retryPolicy.enabled`) !== false}
                              onCheckedChange={(checked) => updateToolConfig(tool.id, 'retryPolicy', {
                                ...watch(`toolConfigs.${tool.id}.retryPolicy`),
                                enabled: checked,
                              })}
                            />
                            <Label>Enable retry on failure</Label>
                          </div>
                        </div>

                        <div className="mt-4 space-y-2">
                          <Label>Parameters (JSON)</Label>
                          <Textarea
                            value={JSON.stringify(watch(`toolConfigs.${tool.id}.parameters`) || {}, null, 2)}
                            onChange={(e) => {
                              try {
                                const parsed = JSON.parse(e.target.value);
                                updateToolConfig(tool.id, 'parameters', parsed);
                              } catch (error) {
                                // Invalid JSON, don't update
                              }
                            }}
                            rows={3}
                            className="font-mono text-sm"
                            placeholder='{\n  "key": "value"\n}'
                          />
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Wrench className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No tools selected</p>
                    <p className="text-sm">Select tools in the Basic tab to configure them</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="coordination" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Context Sharing</h4>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('coordination.shareContext')}
                      onCheckedChange={(checked) => setValue('coordination.shareContext', checked)}
                    />
                    <Label>Share context between agent and tools</Label>
                  </div>

                  {watch('coordination.shareContext') && (
                    <div className="space-y-2 pl-6 border-l-2 border-muted">
                      <Label>Context Strategy</Label>
                      <Select
                        value={watch('coordination.contextStrategy')}
                        onValueChange={(value: 'full' | 'summary' | 'selective') =>
                          setValue('coordination.contextStrategy', value)
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="full">Full Context</SelectItem>
                          <SelectItem value="summary">Summary Only</SelectItem>
                          <SelectItem value="selective">Selective Sharing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Synchronization Points</h4>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addSyncPoint}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Sync Point
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {(watch('coordination.syncPoints') || []).map((syncPoint, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input
                          value={syncPoint}
                          onChange={(e) => {
                            const updated = [...(watch('coordination.syncPoints') || [])];
                            updated[index] = e.target.value;
                            setValue('coordination.syncPoints', updated);
                          }}
                          placeholder="Sync point name"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSyncPoint(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}

                    {(watch('coordination.syncPoints') || []).length === 0 && (
                      <p className="text-sm text-muted-foreground text-center py-4">
                        No synchronization points defined
                      </p>
                    )}
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('coordination.errorPropagation')}
                      onCheckedChange={(checked) => setValue('coordination.errorPropagation', checked)}
                    />
                    <Label>Propagate errors between components</Label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Execution Limits</h4>

                  <div className="space-y-2">
                    <Label>Parallel Execution Limit</Label>
                    <Slider
                      value={[watch('performance.parallelLimit') || 3]}
                      onValueChange={([value]) => setValue('performance.parallelLimit', value)}
                      min={1}
                      max={10}
                      step={1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      Maximum {watch('performance.parallelLimit')} parallel executions
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Overall Timeout</Label>
                    <Slider
                      value={[watch('performance.timeout') || 120000]}
                      onValueChange={([value]) => setValue('performance.timeout', value)}
                      min={10000}
                      max={600000}
                      step={10000}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      {Math.round((watch('performance.timeout') || 120000) / 1000)}s total timeout
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Memory Limit (MB)</Label>
                    <Slider
                      value={[watch('performance.memoryLimit') || 50]}
                      onValueChange={([value]) => setValue('performance.memoryLimit', value)}
                      min={1}
                      max={100}
                      step={1}
                      className="w-full"
                    />
                    <div className="text-xs text-muted-foreground">
                      {watch('performance.memoryLimit')}MB memory limit
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="fallback" className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={watch('fallback.enabled')}
                      onCheckedChange={(checked) => setValue('fallback.enabled', checked)}
                    />
                    <Label>Enable fallback mechanisms</Label>
                  </div>

                  {watch('fallback.enabled') && (
                    <div className="space-y-4 pl-6 border-l-2 border-muted">
                      <div className="space-y-2">
                        <Label>Fallback Agent</Label>
                        <Select
                          value={watch('fallback.fallbackAgent') || ''}
                          onValueChange={(value) => setValue('fallback.fallbackAgent', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select fallback agent" />
                          </SelectTrigger>
                          <SelectContent>
                            {agents.filter(a => a.id !== selectedAgentId).map((agent) => (
                              <SelectItem key={agent.id} value={agent.id}>
                                {agent.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label>Fallback Tools</Label>
                        <div className="space-y-2">
                          {tools.filter(t => !selectedToolIds.includes(t.id)).map((tool) => (
                            <div key={tool.id} className="flex items-center space-x-2">
                              <Checkbox
                                checked={(watch('fallback.fallbackTools') || []).includes(tool.id)}
                                onCheckedChange={(checked) => {
                                  const current = watch('fallback.fallbackTools') || [];
                                  if (checked) {
                                    setValue('fallback.fallbackTools', [...current, tool.id]);
                                  } else {
                                    setValue('fallback.fallbackTools', current.filter(id => id !== tool.id));
                                  }
                                }}
                              />
                              <Label>{tool.name}</Label>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Fallback Conditions</Label>
                        <div className="space-y-2">
                          {['timeout', 'error', 'low_confidence', 'resource_limit'].map((condition) => (
                            <div key={condition} className="flex items-center space-x-2">
                              <Checkbox
                                checked={(watch('fallback.conditions') || []).includes(condition)}
                                onCheckedChange={(checked) => {
                                  const current = watch('fallback.conditions') || [];
                                  if (checked) {
                                    setValue('fallback.conditions', [...current, condition]);
                                  } else {
                                    setValue('fallback.conditions', current.filter(c => c !== condition));
                                  }
                                }}
                              />
                              <Label>{condition.replace('_', ' ')}</Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}