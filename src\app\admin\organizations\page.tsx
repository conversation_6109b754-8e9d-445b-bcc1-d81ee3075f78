"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Building2,
  Users,
  MoreHorizontal,
  Search,
  Plus,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Shield,
  ShieldAlert,
  CheckCircle,
  XCircle,
  Activity,
  BarChart3,
  Settings,
  CreditCard,
  Calendar,
  Globe,
  Zap,
} from "lucide-react";
import Link from "next/link";
import apiClient from "@/lib/api-client";
import apixClient from "@/lib/apix-client";

interface Organization {
  id: string;
  name: string;
  slug: string;
  description: string;
  status: "active" | "suspended" | "inactive";
  plan: "starter" | "professional" | "enterprise";
  createdAt: string;
  lastActiveAt: string;
  owner: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  metrics: {
    totalUsers: number;
    activeUsers: number;
    workflows: number;
    agents: number;
    tools: number;
    apiCalls: number;
    storageUsed: number;
    storageLimit: number;
  };
  billing: {
    currentPeriodStart: string;
    currentPeriodEnd: string;
    amountDue: number;
    paymentStatus: "paid" | "pending" | "failed";
  };
}

interface OrganizationFormData {
  name: string;
  slug: string;
  description: string;
  plan: string;
  ownerEmail: string;
  ownerFirstName: string;
  ownerLastName: string;
}

export default function OrganizationsPage() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [planFilter, setPlanFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [formData, setFormData] = useState<OrganizationFormData>({
    name: "",
    slug: "",
    description: "",
    plan: "starter",
    ownerEmail: "",
    ownerFirstName: "",
    ownerLastName: "",
  });

  // Load organizations data
  useEffect(() => {
    const loadOrganizations = async () => {
      setLoading(true);
      try {
        const response = await apiClient.get('/admin/organizations');
        setOrganizations(response.data.organizations);
      } catch (error) {
        console.error("Failed to load organizations:", error);
        // Set fallback data
        setOrganizations([
          {
            id: "org-1",
            name: "TechCorp Inc.",
            slug: "techcorp",
            description: "Leading technology solutions provider",
            status: "active",
            plan: "enterprise",
            createdAt: "2024-01-15T00:00:00Z",
            lastActiveAt: "2024-01-20T14:30:00Z",
            owner: {
              id: "user-1",
              email: "<EMAIL>",
              firstName: "John",
              lastName: "Smith",
            },
            metrics: {
              totalUsers: 25,
              activeUsers: 18,
              workflows: 12,
              agents: 8,
              tools: 15,
              apiCalls: 12500,
              storageUsed: 2.5,
              storageLimit: 10,
            },
            billing: {
              currentPeriodStart: "2024-01-01T00:00:00Z",
              currentPeriodEnd: "2024-02-01T00:00:00Z",
              amountDue: 299,
              paymentStatus: "paid",
            },
          },
          {
            id: "org-2",
            name: "DataFlow Systems",
            slug: "dataflow",
            description: "Data analytics and processing solutions",
            status: "active",
            plan: "professional",
            createdAt: "2024-01-10T00:00:00Z",
            lastActiveAt: "2024-01-20T12:15:00Z",
            owner: {
              id: "user-2",
              email: "<EMAIL>",
              firstName: "Sarah",
              lastName: "Johnson",
            },
            metrics: {
              totalUsers: 18,
              activeUsers: 14,
              workflows: 8,
              agents: 6,
              tools: 12,
              apiCalls: 8750,
              storageUsed: 1.8,
              storageLimit: 5,
            },
            billing: {
              currentPeriodStart: "2024-01-01T00:00:00Z",
              currentPeriodEnd: "2024-02-01T00:00:00Z",
              amountDue: 99,
              paymentStatus: "paid",
            },
          },
          {
            id: "org-3",
            name: "InnovateLab",
            slug: "innovatelab",
            description: "Innovation and research laboratory",
            status: "suspended",
            plan: "starter",
            createdAt: "2024-01-05T00:00:00Z",
            lastActiveAt: "2024-01-18T09:45:00Z",
            owner: {
              id: "user-3",
              email: "<EMAIL>",
              firstName: "Michael",
              lastName: "Chen",
            },
            metrics: {
              totalUsers: 8,
              activeUsers: 3,
              workflows: 4,
              agents: 2,
              tools: 6,
              apiCalls: 2100,
              storageUsed: 0.5,
              storageLimit: 1,
            },
            billing: {
              currentPeriodStart: "2024-01-01T00:00:00Z",
              currentPeriodEnd: "2024-02-01T00:00:00Z",
              amountDue: 29,
              paymentStatus: "failed",
            },
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadOrganizations();
  }, []);

  // Set up real-time updates
  useEffect(() => {
    const unsubscribeOrgUpdate = apixClient.on('admin_organization_updated', (event) => {
      setOrganizations(prev => 
        prev.map(org => 
          org.id === event.data.organizationId 
            ? { ...org, ...event.data.updates }
            : org
        )
      );
    });

    const unsubscribeOrgCreated = apixClient.on('admin_organization_created', (event) => {
      setOrganizations(prev => [event.data.organization, ...prev]);
    });

    return () => {
      unsubscribeOrgUpdate();
      unsubscribeOrgCreated();
    };
  }, []);

  // Filter organizations
  const filteredOrganizations = organizations.filter(org => {
    const matchesSearch = org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         org.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         org.owner.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || org.status === statusFilter;
    const matchesPlan = planFilter === "all" || org.plan === planFilter;
    
    return matchesSearch && matchesStatus && matchesPlan;
  });

  const handleCreateOrganization = async () => {
    try {
      const response = await apiClient.post('/admin/organizations', formData);
      setOrganizations(prev => [response.data.organization, ...prev]);
      setIsCreateDialogOpen(false);
      setFormData({
        name: "",
        slug: "",
        description: "",
        plan: "starter",
        ownerEmail: "",
        ownerFirstName: "",
        ownerLastName: "",
      });
    } catch (error) {
      console.error("Failed to create organization:", error);
    }
  };

  const handleUpdateOrganization = async () => {
    if (!selectedOrganization) return;
    
    try {
      const response = await apiClient.put(`/admin/organizations/${selectedOrganization.id}`, formData);
      setOrganizations(prev => 
        prev.map(org => 
          org.id === selectedOrganization.id 
            ? { ...org, ...response.data.organization }
            : org
        )
      );
      setIsEditDialogOpen(false);
      setSelectedOrganization(null);
    } catch (error) {
      console.error("Failed to update organization:", error);
    }
  };

  const handleDeleteOrganization = async () => {
    if (!selectedOrganization) return;
    
    try {
      await apiClient.delete(`/admin/organizations/${selectedOrganization.id}`);
      setOrganizations(prev => prev.filter(org => org.id !== selectedOrganization.id));
      setIsDeleteDialogOpen(false);
      setSelectedOrganization(null);
    } catch (error) {
      console.error("Failed to delete organization:", error);
    }
  };

  const handleSuspendOrganization = async (organizationId: string) => {
    try {
      await apiClient.put(`/admin/organizations/${organizationId}/suspend`);
      setOrganizations(prev => 
        prev.map(org => 
          org.id === organizationId 
            ? { ...org, status: "suspended" as const }
            : org
        )
      );
    } catch (error) {
      console.error("Failed to suspend organization:", error);
    }
  };

  const handleActivateOrganization = async (organizationId: string) => {
    try {
      await apiClient.put(`/admin/organizations/${organizationId}/activate`);
      setOrganizations(prev => 
        prev.map(org => 
          org.id === organizationId 
            ? { ...org, status: "active" as const }
            : org
        )
      );
    } catch (error) {
      console.error("Failed to activate organization:", error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "suspended":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "inactive":
        return <Activity className="h-4 w-4 text-gray-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getPlanBadgeVariant = (plan: string) => {
    switch (plan) {
      case "enterprise":
        return "default";
      case "professional":
        return "secondary";
      case "starter":
        return "outline";
      default:
        return "outline";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
          <span>Loading organizations...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold">Organizations</h1>
          <p className="text-muted-foreground">
            Manage all organizations and their configurations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Organization
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Create New Organization</DialogTitle>
                <DialogDescription>
                  Set up a new organization with initial configuration and owner details.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Organization Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="TechCorp Inc."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="slug">Slug</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                      placeholder="techcorp"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of the organization..."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="plan">Plan</Label>
                  <Select value={formData.plan} onValueChange={(value) => setFormData(prev => ({ ...prev, plan: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="starter">Starter - $29/month</SelectItem>
                      <SelectItem value="professional">Professional - $99/month</SelectItem>
                      <SelectItem value="enterprise">Enterprise - $299/month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="ownerFirstName">Owner First Name</Label>
                    <Input
                      id="ownerFirstName"
                      value={formData.ownerFirstName}
                      onChange={(e) => setFormData(prev => ({ ...prev, ownerFirstName: e.target.value }))}
                      placeholder="John"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ownerLastName">Owner Last Name</Label>
                    <Input
                      id="ownerLastName"
                      value={formData.ownerLastName}
                      onChange={(e) => setFormData(prev => ({ ...prev, ownerLastName: e.target.value }))}
                      placeholder="Smith"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ownerEmail">Owner Email</Label>
                  <Input
                    id="ownerEmail"
                    type="email"
                    value={formData.ownerEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, ownerEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateOrganization}>
                  Create Organization
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters and Search */}
      <Card className="bg-card/80 backdrop-blur-sm">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search organizations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <Select value={planFilter} onValueChange={setPlanFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Plans</SelectItem>
                  <SelectItem value="starter">Starter</SelectItem>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Organizations Table */}
      <Card className="bg-card/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Organizations ({filteredOrganizations.length})</span>
            <Badge variant="outline">
              {organizations.filter(org => org.status === 'active').length} Active
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Organization</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Billing</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrganizations.map((org) => (
                <TableRow key={org.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${org.name}`} />
                        <AvatarFallback>{org.name.slice(0, 2).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{org.name}</p>
                        <p className="text-sm text-muted-foreground">/{org.slug}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{org.owner.firstName} {org.owner.lastName}</p>
                      <p className="text-sm text-muted-foreground">{org.owner.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getPlanBadgeVariant(org.plan)} className="capitalize">
                      {org.plan}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(org.status)}
                      <span className="capitalize">{org.status}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{org.metrics.totalUsers} total</p>
                      <p className="text-muted-foreground">{org.metrics.activeUsers} active</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{org.metrics.workflows} workflows</p>
                      <p className="text-muted-foreground">{org.metrics.agents} agents</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p className="font-medium">{formatCurrency(org.billing.amountDue)}</p>
                      <Badge variant={
                        org.billing.paymentStatus === 'paid' ? 'secondary' :
                        org.billing.paymentStatus === 'pending' ? 'outline' : 'destructive'
                      } className="text-xs">
                        {org.billing.paymentStatus}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <p>{formatDate(org.createdAt)}</p>
                      <p className="text-muted-foreground">Last: {formatDate(org.lastActiveAt)}</p>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => {
                          setSelectedOrganization(org);
                          setFormData({
                            name: org.name,
                            slug: org.slug,
                            description: org.description,
                            plan: org.plan,
                            ownerEmail: org.owner.email,
                            ownerFirstName: org.owner.firstName,
                            ownerLastName: org.owner.lastName,
                          });
                          setIsEditDialogOpen(true);
                        }}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Organization
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Analytics
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <CreditCard className="mr-2 h-4 w-4" />
                          Billing
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {org.status === 'active' ? (
                          <DropdownMenuItem 
                            onClick={() => handleSuspendOrganization(org.id)}
                            className="text-yellow-600"
                          >
                            <ShieldAlert className="mr-2 h-4 w-4" />
                            Suspend
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem 
                            onClick={() => handleActivateOrganization(org.id)}
                            className="text-green-600"
                          >
                            <Shield className="mr-2 h-4 w-4" />
                            Activate
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => {
                            setSelectedOrganization(org);
                            setIsDeleteDialogOpen(true);
                          }}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
            <DialogDescription>
              Update organization details and configuration.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Organization Name</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-slug">Slug</Label>
                <Input
                  id="edit-slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-plan">Plan</Label>
              <Select value={formData.plan} onValueChange={(value) => setFormData(prev => ({ ...prev, plan: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="starter">Starter - $29/month</SelectItem>
                  <SelectItem value="professional">Professional - $99/month</SelectItem>
                  <SelectItem value="enterprise">Enterprise - $299/month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateOrganization}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Organization</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedOrganization?.name}"? This action cannot be undone and will permanently remove all associated data.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteOrganization}>
              Delete Organization
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}