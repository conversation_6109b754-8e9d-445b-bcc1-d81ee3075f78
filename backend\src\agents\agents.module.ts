import { Modu<PERSON> } from '@nestjs/common';
import { AgentsController } from './agents.controller';
import { AgentsService } from './agents.service';
import { PrismaModule } from '../prisma/prisma.module';
import { ApixModule } from '../apix/apix.module';
import { SessionsModule } from '../sessions/sessions.module';
import { AgentOrchestratorService } from './services/agent-orchestrator.service';
import { SessionMemoryService } from './services/session-memory.service';
import { SkillExecutorService } from './services/skill-executor.service';
import { TaskTrackerService } from './services/task-tracker.service';
import { AgentCommunicationService } from './services/agent-communication.service';
import { AgentTemplateService } from './services/agent-template.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CacheModule } from '@nestjs/cache-manager';

@Module({
  imports: [
    PrismaModule,
    ApixModule,
    SessionsModule,
    EventEmitterModule,
    CacheModule.register({
      ttl: 300, // 5 minutes
      max: 1000, // maximum number of items in cache
    }),
  ],
  controllers: [AgentsController],
  providers: [
    AgentsService,
    AgentOrchestratorService,
    SessionMemoryService,
    SkillExecutorService,
    TaskTrackerService,
    AgentCommunicationService,
    AgentTemplateService,
  ],
  exports: [
    AgentsService,
    AgentOrchestratorService,
    SessionMemoryService,
    SkillExecutorService,
    TaskTrackerService,
    AgentCommunicationService,
    AgentTemplateService,
  ],
})
export class AgentsModule {}