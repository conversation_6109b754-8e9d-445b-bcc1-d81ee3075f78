import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ToolType } from '@prisma/client';
import { MockEventEmitter } from '../../agents/mocks/event-emitter.mock';
import { ToolCacheService } from './tool-cache.service';
export interface ToolExecutionContext {
    toolId: string;
    input: any;
    executorType: 'user' | 'agent' | 'system' | 'workflow';
    executorId?: string;
    sessionId?: string;
    organizationId: string;
    metadata?: any;
    timeout?: number;
    retryPolicy?: {
        maxRetries: number;
        backoffStrategy: 'linear' | 'exponential';
        initialDelay: number;
    };
}
export interface ToolExecutionResult {
    success: boolean;
    output?: any;
    error?: string;
    duration: number;
    cached: boolean;
    metadata?: any;
    usage?: {
        tokensUsed?: number;
        apiCalls?: number;
        cost?: number;
        memoryUsed?: number;
        cpuTime?: number;
    };
}
export interface ToolExecutor {
    type: ToolType;
    execute(tool: any, input: any, context: ToolExecutionContext): Promise<any>;
    validate?(tool: any, input: any): Promise<void>;
    healthCheck?(tool: any): Promise<boolean>;
}
export declare class ToolExecutionService {
    private prisma;
    private cacheManager;
    private configService;
    private eventEmitter;
    private cacheService;
    private readonly logger;
    private readonly executors;
    private readonly activeExecutions;
    constructor(prisma: PrismaService, cacheManager: Cache, configService: ConfigService, eventEmitter: MockEventEmitter, cacheService: ToolCacheService);
    executeTool(context: ToolExecutionContext): Promise<ToolExecutionResult>;
    private executeWithRetryAndTimeout;
    private executeToolInternal;
    private initializeExecutors;
    private executeAPIFetch;
    private validateAPIFetchInput;
    private healthCheckAPIFetch;
    private executeFunctionCall;
    private executeJavaScript;
    private executePython;
    private executeShell;
    private validateFunctionCallInput;
    private executeRAG;
    private validateRAGInput;
    private getToolDefinition;
    private validateInput;
    private checkCache;
    private cacheResult;
    private createExecutionRecord;
    private updateExecutionRecord;
    private updateToolStats;
    private generateExecutionId;
    private isRetryableError;
    private calculateRetryDelay;
    private applyAuthentication;
    private getToolAPIKey;
    private mapInputToRequest;
    private mapResponseToOutput;
    private getNestedValue;
    getActiveExecutions(organizationId: string): Promise<any[]>;
    getExecutionHistory(toolId?: string, organizationId?: string, limit?: number): Promise<any[]>;
    cancelExecution(executionId: string, organizationId: string): Promise<void>;
}
