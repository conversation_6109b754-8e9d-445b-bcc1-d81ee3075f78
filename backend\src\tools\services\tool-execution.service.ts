import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ToolType, ToolExecutionStatus, ToolCacheStrategy } from '@prisma/client';
import { MockEventEmitter } from '../../agents/mocks/event-emitter.mock';
import { ToolCacheService } from './tool-cache.service';
import * as z from 'zod';
import * as crypto from 'crypto';

export interface ToolExecutionContext {
    toolId: string;
    input: any;
    executorType: 'user' | 'agent' | 'system' | 'workflow';
    executorId?: string;
    sessionId?: string;
    organizationId: string;
    metadata?: any;
    timeout?: number;
    retryPolicy?: {
        maxRetries: number;
        backoffStrategy: 'linear' | 'exponential';
        initialDelay: number;
    };
}

export interface ToolExecutionResult {
    success: boolean;
    output?: any;
    error?: string;
    duration: number;
    cached: boolean;
    metadata?: any;
    usage?: {
        tokensUsed?: number;
        apiCalls?: number;
        cost?: number;
        memoryUsed?: number;
        cpuTime?: number;
    };
}

export interface ToolExecutor {
    type: ToolType;
    execute(tool: any, input: any, context: ToolExecutionContext): Promise<any>;
    validate?(tool: any, input: any): Promise<void>;
    healthCheck?(tool: any): Promise<boolean>;
}

@Injectable()
export class ToolExecutionService {
    private readonly logger = new Logger(ToolExecutionService.name);
    private readonly executors = new Map<ToolType, ToolExecutor>();
    private readonly activeExecutions = new Map<string, any>();

    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configService: ConfigService,
        private eventEmitter: MockEventEmitter,
        private cacheService: ToolCacheService,
    ) {
        this.initializeExecutors();
    }

    // ============================================================================
    // MAIN EXECUTION METHODS
    // ============================================================================

    async executeTool(context: ToolExecutionContext): Promise<ToolExecutionResult> {
        const executionId = this.generateExecutionId();
        const startTime = Date.now();

        try {
            // Get tool definition
            const tool = await this.getToolDefinition(context.toolId, context.organizationId);

            if (!tool.isActive) {
                throw new BadRequestException('Tool is not active');
            }

            // Validate input
            await this.validateInput(tool, context.input);

            // Check cache first
            const cachedResult = await this.checkCache(tool, context.input);
            if (cachedResult) {
                return {
                    success: true,
                    output: cachedResult.output,
                    duration: Date.now() - startTime,
                    cached: true,
                    metadata: {
                        executionId,
                        cacheHit: true,
                    },
                };
            }

            // Create execution record
            const execution = await this.createExecutionRecord(executionId, tool, context);

            // Track active execution
            this.activeExecutions.set(executionId, {
                startTime,
                tool,
                context,
                status: 'running',
            });

            // Emit start event
            this.eventEmitter.emit('tool.execution.start', {
                executionId,
                toolId: context.toolId,
                inputs: context.input,
                sessionId: context.sessionId,
            });

            // Execute with timeout and retries
            const result = await this.executeWithRetryAndTimeout(executionId, tool, context);

            // Update execution record
            await this.updateExecutionRecord(executionId, {
                status: ToolExecutionStatus.COMPLETED,
                output: result.output,
                duration: result.duration,
                tokensUsed: result.usage?.tokensUsed || 0,
                cost: result.usage?.cost || 0,
                memoryUsed: result.usage?.memoryUsed,
                cpuTime: result.usage?.cpuTime,
                completedAt: new Date(),
            });

            // Cache result if applicable
            await this.cacheResult(tool, context.input, result.output);

            // Update tool usage statistics
            await this.updateToolStats(context.toolId, true, result.duration);

            // Emit completion event
            this.eventEmitter.emit('tool.execution.complete', {
                executionId,
                outputs: result.output,
                duration: result.duration,
                cached: false,
            });

            // Cleanup
            this.activeExecutions.delete(executionId);

            return {
                success: true,
                output: result.output,
                duration: result.duration,
                cached: false,
                metadata: {
                    executionId,
                    toolVersion: tool.version,
                },
                usage: result.usage,
            };

        } catch (error) {
            const duration = Date.now() - startTime;

            // Update execution record with error
            try {
                await this.updateExecutionRecord(executionId, {
                    status: ToolExecutionStatus.FAILED,
                    error: error.message,
                    duration,
                    completedAt: new Date(),
                });
            } catch (dbError) {
                this.logger.error('Failed to update execution record', dbError);
            }

            // Update tool error statistics
            await this.updateToolStats(context.toolId, false, duration);

            // Emit error event
            this.eventEmitter.emit('tool.execution.error', {
                executionId,
                error: error.message,
                retryable: this.isRetryableError(error),
            });

            // Cleanup
            this.activeExecutions.delete(executionId);

            return {
                success: false,
                error: error.message,
                duration,
                cached: false,
                metadata: {
                    executionId,
                    errorType: error.constructor.name,
                },
            };
        }
    }

    private async executeWithRetryAndTimeout(
        executionId: string,
        tool: any,
        context: ToolExecutionContext,
    ): Promise<any> {
        const timeout = context.timeout || tool.timeout || 30000;
        const retryPolicy = context.retryPolicy || tool.retryPolicy || {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            initialDelay: 1000,
        };

        let lastError: Error;
        let attempt = 0;

        while (attempt <= retryPolicy.maxRetries) {
            try {
                // Execute with timeout
                const result = await Promise.race([
                    this.executeToolInternal(tool, context.input, context),
                    new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('Tool execution timeout')), timeout)
                    ),
                ]);

                return result;

            } catch (error) {
                lastError = error;
                attempt++;

                if (attempt <= retryPolicy.maxRetries && this.isRetryableError(error)) {
                    const delay = this.calculateRetryDelay(attempt, retryPolicy);

                    this.logger.warn(
                        `Tool execution attempt ${attempt} failed, retrying in ${delay}ms: ${tool.id}`,
                        error.message,
                    );

                    // Emit progress event
                    this.eventEmitter.emit('tool.execution.progress', {
                        executionId,
                        progress: (attempt / (retryPolicy.maxRetries + 1)) * 100,
                        message: `Retry attempt ${attempt}/${retryPolicy.maxRetries}`,
                    });

                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    break;
                }
            }
        }

        throw lastError;
    }

    private async executeToolInternal(tool: any, input: any, context: ToolExecutionContext): Promise<any> {
        const executor = this.executors.get(tool.type);

        if (!executor) {
            throw new BadRequestException(`No executor found for tool type: ${tool.type}`);
        }

        // Validate input with executor if available
        if (executor.validate) {
            await executor.validate(tool, input);
        }

        // Execute the tool
        const startTime = Date.now();
        const output = await executor.execute(tool, input, context);
        const duration = Date.now() - startTime;

        return {
            output,
            duration,
            usage: {
                // Usage tracking would be implemented based on tool type
            },
        };
    }

    // ============================================================================
    // EXECUTOR IMPLEMENTATIONS
    // ============================================================================

    private initializeExecutors(): void {
        // API Fetch Executor
        this.executors.set(ToolType.API_FETCH, {
            type: ToolType.API_FETCH,
            execute: this.executeAPIFetch.bind(this),
            validate: this.validateAPIFetchInput.bind(this),
            healthCheck: this.healthCheckAPIFetch.bind(this),
        });

        // Function Call Executor
        this.executors.set(ToolType.FUNCTION_CALL, {
            type: ToolType.FUNCTION_CALL,
            execute: this.executeFunctionCall.bind(this),
            validate: this.validateFunctionCallInput.bind(this),
        });

        // RAG Executor
        this.executors.set(ToolType.RAG, {
            type: ToolType.RAG,
            execute: this.executeRAG.bind(this),
            validate: this.validateRAGInput.bind(this),
        });

        this.logger.log('Initialized tool executors');
    }

    private async executeAPIFetch(tool: any, input: any, context: ToolExecutionContext): Promise<any> {
        const config = tool.config;
        const { endpoint, method, headers = {}, authentication } = config;

        // Apply authentication
        const authHeaders = await this.applyAuthentication(authentication, tool.id);
        const finalHeaders = { ...headers, ...authHeaders };

        // Map input to request
        const requestData = this.mapInputToRequest(input, config.requestMapping);

        // Make the request
        const requestOptions: any = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...finalHeaders,
            },
        };

        if (['POST', 'PUT', 'PATCH'].includes(method) && requestData) {
            requestOptions.body = JSON.stringify(requestData);
        }

        const url = method === 'GET' && requestData
            ? `${endpoint}?${new URLSearchParams(requestData)}`
            : endpoint;

        const response = await fetch(url, requestOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json();

        // Map response to output
        return this.mapResponseToOutput(responseData, config.responseMapping);
    }

    private async validateAPIFetchInput(tool: any, input: any): Promise<void> {
        if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
            // Validate against input schema
            // This would use a JSON schema validator
        }
    }

    private async healthCheckAPIFetch(tool: any): Promise<boolean> {
        try {
            const config = tool.config;
            const response = await fetch(config.endpoint, {
                method: 'HEAD',
                headers: await this.applyAuthentication(config.authentication, tool.id),
            });
            return response.ok;
        } catch {
            return false;
        }
    }

    private async executeFunctionCall(tool: any, input: any, context: ToolExecutionContext): Promise<any> {
        const config = tool.config;
        const { code, runtime, environment = {} } = config;

        switch (runtime) {
            case 'javascript':
                return this.executeJavaScript(code, input, environment);
            case 'python':
                return this.executePython(code, input, environment);
            case 'shell':
                return this.executeShell(code, input, environment);
            default:
                throw new BadRequestException(`Unsupported runtime: ${runtime}`);
        }
    }

    private async executeJavaScript(code: string, input: any, environment: any): Promise<any> {
        // Create a sandboxed execution environment
        const sandbox = {
            input,
            environment,
            console: {
                log: (...args: any[]) => this.logger.debug('Tool JS Log:', ...args),
            },
            fetch, // Allow HTTP requests
            Buffer,
            setTimeout,
            clearTimeout,
        };

        // Execute code in sandbox
        const func = new Function(...Object.keys(sandbox), `
      "use strict";
      ${code}
      
      // If no return statement, return the result variable if it exists
      if (typeof result !== 'undefined') {
        return result;
      }
    `);

        return func(...Object.values(sandbox));
    }

    private async executePython(code: string, input: any, environment: any): Promise<any> {
        // This would require a Python execution environment
        // For now, throw an error indicating it's not implemented
        throw new BadRequestException('Python execution not implemented in this environment');
    }

    private async executeShell(code: string, input: any, environment: any): Promise<any> {
        // This would require shell command execution
        // For security reasons, this should be carefully sandboxed
        throw new BadRequestException('Shell execution not implemented for security reasons');
    }

    private async validateFunctionCallInput(tool: any, input: any): Promise<void> {
        // Validate input for function call
        if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
            // Validate against input schema
        }
    }

    private async executeRAG(tool: any, input: any, context: ToolExecutionContext): Promise<any> {
        const config = tool.config;
        const { vectorStore, embedding, retrieval } = config;

        // This would integrate with various vector databases
        // For now, return a mock response
        return {
            query: input.query,
            results: [
                {
                    content: 'Mock RAG result',
                    score: 0.95,
                    metadata: {},
                },
            ],
            totalResults: 1,
        };
    }

    private async validateRAGInput(tool: any, input: any): Promise<void> {
        if (!input.query || typeof input.query !== 'string') {
            throw new BadRequestException('RAG tools require a query string');
        }
    }

    // ============================================================================
    // HELPER METHODS
    // ============================================================================

    private async getToolDefinition(toolId: string, organizationId: string): Promise<any> {
        const tool = await this.prisma.toolDefinition.findFirst({
            where: {
                id: toolId,
                isActive: true,
                OR: [
                    { isPublic: true },
                    { organizationId },
                ],
            },
        });

        if (!tool) {
            throw new NotFoundException('Tool not found or not accessible');
        }

        return tool;
    }

    private async validateInput(tool: any, input: any): Promise<void> {
        if (tool.inputSchema && Object.keys(tool.inputSchema).length > 0) {
            try {
                // Basic validation - in production, use a proper JSON schema validator
                if (tool.inputSchema.required) {
                    for (const field of tool.inputSchema.required) {
                        if (!(field in input)) {
                            throw new Error(`Required field '${field}' is missing`);
                        }
                    }
                }
            } catch (error) {
                throw new BadRequestException(`Input validation failed: ${error.message}`);
            }
        }
    }

    private async checkCache(tool: any, input: any): Promise<any> {
        if (tool.cacheStrategy === ToolCacheStrategy.NONE) {
            return null;
        }

        return this.cacheService.get(tool.id, input);
    }

    private async cacheResult(tool: any, input: any, output: any): Promise<void> {
        if (tool.cacheStrategy !== ToolCacheStrategy.NONE) {
            await this.cacheService.set(tool.id, input, output, tool.cacheTTL);
        }
    }

    private async createExecutionRecord(executionId: string, tool: any, context: ToolExecutionContext): Promise<any> {
        return this.prisma.toolExecution.create({
            data: {
                id: executionId,
                toolId: tool.id,
                organizationId: context.organizationId,
                executorType: context.executorType,
                executorId: context.executorId,
                sessionId: context.sessionId,
                status: ToolExecutionStatus.RUNNING,
                input: context.input,
                metadata: context.metadata || {},
                startedAt: new Date(),
            },
        });
    }

    private async updateExecutionRecord(executionId: string, updates: any): Promise<void> {
        await this.prisma.toolExecution.update({
            where: { id: executionId },
            data: updates,
        });
    }

    private async updateToolStats(toolId: string, success: boolean, duration: number): Promise<void> {
        // Update tool usage statistics
        const updates: any = {
            usageCount: { increment: 1 },
            avgLatency: duration, // This should be calculated properly
        };

        if (success) {
            updates.successRate = { increment: 0.01 }; // Simplified calculation
        }

        await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: updates,
        });
    }

    private generateExecutionId(): string {
        return `exec_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
    }

    private isRetryableError(error: any): boolean {
        // Determine if error is retryable
        const retryablePatterns = [
            /timeout/i,
            /network/i,
            /connection/i,
            /rate limit/i,
            /temporary/i,
        ];

        return retryablePatterns.some(pattern => pattern.test(error.message));
    }

    private calculateRetryDelay(attempt: number, retryPolicy: any): number {
        const { backoffStrategy, initialDelay } = retryPolicy;

        if (backoffStrategy === 'exponential') {
            return initialDelay * Math.pow(2, attempt - 1);
        } else {
            return initialDelay * attempt;
        }
    }

    private async applyAuthentication(authConfig: any, toolId: string): Promise<Record<string, string>> {
        if (!authConfig || authConfig.type === 'none') {
            return {};
        }

        switch (authConfig.type) {
            case 'bearer':
                // Get API key from tool's API keys
                const apiKey = await this.getToolAPIKey(toolId, 'bearer');
                return {
                    'Authorization': `Bearer ${apiKey}`,
                };
            case 'api_key':
                const key = await this.getToolAPIKey(toolId, 'api_key');
                const headerName = authConfig.config?.headerName || 'X-API-Key';
                return {
                    [headerName]: key,
                };
            case 'oauth':
                // OAuth token management would be implemented here
                return {};
            default:
                return {};
        }
    }

    private async getToolAPIKey(toolId: string, keyType: string): Promise<string> {
        // This would decrypt and return the API key
        // For now, return a placeholder
        return 'mock_api_key';
    }

    private mapInputToRequest(input: any, mapping?: any): any {
        if (!mapping) {
            return input;
        }

        // Apply input mapping transformations
        const result: any = {};

        for (const [targetKey, sourceKey] of Object.entries(mapping)) {
            if (typeof sourceKey === 'string' && sourceKey in input) {
                result[targetKey] = input[sourceKey];
            }
        }

        return result;
    }

    private mapResponseToOutput(response: any, mapping?: any): any {
        if (!mapping) {
            return response;
        }

        // Apply output mapping transformations
        const result: any = {};

        for (const [targetKey, sourceKey] of Object.entries(mapping)) {
            if (typeof sourceKey === 'string' && this.getNestedValue(response, sourceKey) !== undefined) {
                result[targetKey] = this.getNestedValue(response, sourceKey);
            }
        }

        return result;
    }

    private getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    // ============================================================================
    // PUBLIC QUERY METHODS
    // ============================================================================

    async getActiveExecutions(organizationId: string): Promise<any[]> {
        return this.prisma.toolExecution.findMany({
            where: {
                organizationId,
                status: {
                    in: [ToolExecutionStatus.PENDING, ToolExecutionStatus.RUNNING],
                },
            },
            include: {
                tool: {
                    select: { id: true, name: true, type: true },
                },
            },
            orderBy: { startedAt: 'desc' },
        });
    }

    async getExecutionHistory(
        toolId?: string,
        organizationId?: string,
        limit = 50,
    ): Promise<any[]> {
        const where: any = {};

        if (toolId) {
            where.toolId = toolId;
        }

        if (organizationId) {
            where.organizationId = organizationId;
        }

        return this.prisma.toolExecution.findMany({
            where,
            include: {
                tool: {
                    select: { id: true, name: true, type: true },
                },
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
        });
    }

    async cancelExecution(executionId: string, organizationId: string): Promise<void> {
        const execution = await this.prisma.toolExecution.findFirst({
            where: {
                id: executionId,
                organizationId,
                status: {
                    in: [ToolExecutionStatus.PENDING, ToolExecutionStatus.RUNNING],
                },
            },
        });

        if (!execution) {
            throw new NotFoundException('Execution not found or cannot be cancelled');
        }

        await this.prisma.toolExecution.update({
            where: { id: executionId },
            data: {
                status: ToolExecutionStatus.CANCELLED,
                completedAt: new Date(),
            },
        });

        // Remove from active executions
        this.activeExecutions.delete(executionId);

        this.logger.log(`Cancelled execution: ${executionId}`);
    }
}