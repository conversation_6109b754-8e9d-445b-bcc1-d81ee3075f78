"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiXErrorDto = exports.ApiXHeartbeatDto = exports.ApiXEventReplayDto = exports.ApiXLatencyMetricDto = exports.ApiXChannelDto = exports.ApiXSubscriptionDto = exports.ApiXEventDto = exports.ApiXConnectionDto = exports.EventPriority = exports.ChannelType = exports.ConnectionStatus = exports.ClientType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var ClientType;
(function (ClientType) {
    ClientType["WEB_APP"] = "WEB_APP";
    ClientType["MOBILE_APP"] = "MOBILE_APP";
    ClientType["SDK_WIDGET"] = "SDK_WIDGET";
    ClientType["API_CLIENT"] = "API_CLIENT";
    ClientType["INTERNAL_SERVICE"] = "INTERNAL_SERVICE";
})(ClientType || (exports.ClientType = ClientType = {}));
var ConnectionStatus;
(function (ConnectionStatus) {
    ConnectionStatus["CONNECTED"] = "CONNECTED";
    ConnectionStatus["DISCONNECTED"] = "DISCONNECTED";
    ConnectionStatus["RECONNECTING"] = "RECONNECTING";
    ConnectionStatus["SUSPENDED"] = "SUSPENDED";
})(ConnectionStatus || (exports.ConnectionStatus = ConnectionStatus = {}));
var ChannelType;
(function (ChannelType) {
    ChannelType["AGENT_EVENTS"] = "AGENT_EVENTS";
    ChannelType["TOOL_EVENTS"] = "TOOL_EVENTS";
    ChannelType["WORKFLOW_EVENTS"] = "WORKFLOW_EVENTS";
    ChannelType["PROVIDER_EVENTS"] = "PROVIDER_EVENTS";
    ChannelType["SYSTEM_EVENTS"] = "SYSTEM_EVENTS";
    ChannelType["SESSION_EVENTS"] = "SESSION_EVENTS";
    ChannelType["PRIVATE_USER"] = "PRIVATE_USER";
    ChannelType["ORGANIZATION"] = "ORGANIZATION";
    ChannelType["PUBLIC"] = "PUBLIC";
})(ChannelType || (exports.ChannelType = ChannelType = {}));
var EventPriority;
(function (EventPriority) {
    EventPriority["LOW"] = "LOW";
    EventPriority["NORMAL"] = "NORMAL";
    EventPriority["HIGH"] = "HIGH";
    EventPriority["CRITICAL"] = "CRITICAL";
})(EventPriority || (exports.EventPriority = EventPriority = {}));
class ApiXConnectionDto {
}
exports.ApiXConnectionDto = ApiXConnectionDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ClientType }),
    (0, class_validator_1.IsEnum)(ClientType),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "clientType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXConnectionDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ApiXConnectionDto.prototype, "subscriptions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXConnectionDto.prototype, "metadata", void 0);
class ApiXEventDto {
}
exports.ApiXEventDto = ApiXEventDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Object)
], ApiXEventDto.prototype, "payload", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "sessionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "correlationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: EventPriority, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(EventPriority),
    __metadata("design:type", String)
], ApiXEventDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ApiXEventDto.prototype, "compressed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXEventDto.prototype, "metadata", void 0);
class ApiXSubscriptionDto {
}
exports.ApiXSubscriptionDto = ApiXSubscriptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ApiXSubscriptionDto.prototype, "channels", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXSubscriptionDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ApiXSubscriptionDto.prototype, "acknowledgment", void 0);
class ApiXChannelDto {
}
exports.ApiXChannelDto = ApiXChannelDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXChannelDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: ChannelType }),
    (0, class_validator_1.IsEnum)(ChannelType),
    __metadata("design:type", String)
], ApiXChannelDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXChannelDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXChannelDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXChannelDto.prototype, "metadata", void 0);
class ApiXLatencyMetricDto {
}
exports.ApiXLatencyMetricDto = ApiXLatencyMetricDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXLatencyMetricDto.prototype, "connectionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXLatencyMetricDto.prototype, "eventType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXLatencyMetricDto.prototype, "latencyMs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXLatencyMetricDto.prototype, "organizationId", void 0);
class ApiXEventReplayDto {
}
exports.ApiXEventReplayDto = ApiXEventReplayDto;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXEventReplayDto.prototype, "since", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [String], required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ApiXEventReplayDto.prototype, "eventTypes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXEventReplayDto.prototype, "limit", void 0);
class ApiXHeartbeatDto {
}
exports.ApiXHeartbeatDto = ApiXHeartbeatDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXHeartbeatDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ApiXHeartbeatDto.prototype, "latency", void 0);
class ApiXErrorDto {
}
exports.ApiXErrorDto = ApiXErrorDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXErrorDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXErrorDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApiXErrorDto.prototype, "stack", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ApiXErrorDto.prototype, "context", void 0);
//# sourceMappingURL=apix.dto.js.map