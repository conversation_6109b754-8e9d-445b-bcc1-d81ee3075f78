import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ToolType, SkillCategory, ToolCacheStrategy } from '@prisma/client';
import { MockEventEmitter } from '../../agents/mocks/event-emitter.mock';
export interface CreateToolDto {
    name: string;
    description?: string;
    category?: SkillCategory;
    type: ToolType;
    config: any;
    inputSchema?: any;
    outputSchema?: any;
    timeout?: number;
    retryPolicy?: any;
    cacheStrategy?: ToolCacheStrategy;
    cacheTTL?: number;
    tags?: string[];
    documentation?: string;
    examples?: any[];
    isPublic?: boolean;
    dependencies?: string[];
    requirements?: any;
}
export interface UpdateToolDto {
    name?: string;
    description?: string;
    category?: SkillCategory;
    config?: any;
    inputSchema?: any;
    outputSchema?: any;
    timeout?: number;
    retryPolicy?: any;
    cacheStrategy?: ToolCacheStrategy;
    cacheTTL?: number;
    tags?: string[];
    documentation?: string;
    examples?: any[];
    isPublic?: boolean;
    isActive?: boolean;
    dependencies?: string[];
    requirements?: any;
}
export interface ToolVersionDto {
    version: string;
    description?: string;
    changes: string[];
    config: any;
    inputSchema: any;
    outputSchema: any;
    requirements?: any;
    isStable?: boolean;
    releaseNotes?: string;
    breakingChanges?: string[];
}
export interface ToolSearchOptions {
    query?: string;
    type?: ToolType;
    category?: SkillCategory;
    tags?: string[];
    isPublic?: boolean;
    isActive?: boolean;
    createdBy?: string;
    organizationId?: string;
    limit?: number;
    offset?: number;
    sortBy?: 'name' | 'createdAt' | 'usageCount' | 'successRate';
    sortOrder?: 'asc' | 'desc';
}
export interface ToolImportOptions {
    format: 'openapi' | 'postman' | 'curl' | 'json';
    data: string;
    overrides?: Partial<CreateToolDto>;
}
export declare class ToolManagerService {
    private prisma;
    private cacheManager;
    private configService;
    private eventEmitter;
    private readonly logger;
    constructor(prisma: PrismaService, cacheManager: Cache, configService: ConfigService, eventEmitter: MockEventEmitter);
    createTool(createDto: CreateToolDto, creatorId: string, organizationId?: string): Promise<any>;
    updateTool(toolId: string, updateDto: UpdateToolDto, userId: string, organizationId: string): Promise<any>;
    deleteTool(toolId: string, userId: string, organizationId: string): Promise<void>;
    getTool(toolId: string, organizationId?: string): Promise<any>;
    searchTools(options: ToolSearchOptions): Promise<any>;
    createToolVersion(toolId: string, versionDto: ToolVersionDto, creatorId: string): Promise<any>;
    getToolVersions(toolId: string, organizationId?: string): Promise<any[]>;
    rollbackToVersion(toolId: string, version: string, userId: string, organizationId: string): Promise<any>;
    importTool(importOptions: ToolImportOptions, creatorId: string, organizationId?: string): Promise<any>;
    exportTool(toolId: string, format: string, organizationId?: string): Promise<any>;
    private getToolWithPermissionCheck;
    private validateToolConfiguration;
    private validateAPIFetchConfig;
    private validateFunctionCallConfig;
    private validateRAGConfig;
    private validateSchema;
    private detectBreakingChanges;
    private isSchemaCompatible;
    private hasBreakingConfigChanges;
    private incrementVersion;
    private generateChangeLog;
    private getBreakingChangesList;
    private parseImportData;
    private parseOpenAPISpec;
    private parsePostmanCollection;
    private parseCurlCommand;
    private exportAsJSON;
    private exportAsOpenAPI;
    private exportAsPostman;
    getToolStats(organizationId?: string): Promise<any>;
    getPopularTools(organizationId?: string, limit?: number): Promise<any[]>;
}
