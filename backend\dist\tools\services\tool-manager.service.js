"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ToolManagerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolManagerService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const client_1 = require("@prisma/client");
const event_emitter_mock_1 = require("../../agents/mocks/event-emitter.mock");
const z = require("zod");
let ToolManagerService = ToolManagerService_1 = class ToolManagerService {
    constructor(prisma, cacheManager, configService, eventEmitter) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(ToolManagerService_1.name);
    }
    async createTool(createDto, creatorId, organizationId) {
        await this.validateToolConfiguration(createDto.type, createDto.config);
        if (createDto.inputSchema) {
            this.validateSchema(createDto.inputSchema);
        }
        if (createDto.outputSchema) {
            this.validateSchema(createDto.outputSchema);
        }
        const tool = await this.prisma.toolDefinition.create({
            data: {
                name: createDto.name,
                description: createDto.description,
                category: createDto.category || client_1.SkillCategory.CUSTOM,
                type: createDto.type,
                config: createDto.config,
                inputSchema: createDto.inputSchema || {},
                outputSchema: createDto.outputSchema || {},
                timeout: createDto.timeout || 30000,
                retryPolicy: createDto.retryPolicy || { maxRetries: 3, backoffStrategy: 'exponential' },
                cacheStrategy: createDto.cacheStrategy || client_1.ToolCacheStrategy.INPUT_HASH,
                cacheTTL: createDto.cacheTTL || 3600,
                tags: createDto.tags || [],
                documentation: createDto.documentation,
                examples: createDto.examples || [],
                isPublic: createDto.isPublic || false,
                dependencies: createDto.dependencies || [],
                requirements: createDto.requirements || {},
                createdBy: creatorId,
                organizationId,
            },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
                organization: true,
            },
        });
        await this.createToolVersion(tool.id, {
            version: '1.0.0',
            description: 'Initial version',
            changes: ['Initial release'],
            config: tool.config,
            inputSchema: tool.inputSchema,
            outputSchema: tool.outputSchema,
            requirements: tool.requirements,
            isStable: true,
            releaseNotes: 'Initial release of the tool',
        }, creatorId);
        await this.cacheManager.set(`tool:${tool.id}`, tool, 300000);
        this.eventEmitter.emit('tool.created', {
            toolId: tool.id,
            type: tool.type,
            organizationId: tool.organizationId,
            createdBy: creatorId,
        });
        this.logger.log(`Created tool: ${tool.id} (${tool.name})`);
        return tool;
    }
    async updateTool(toolId, updateDto, userId, organizationId) {
        const tool = await this.getToolWithPermissionCheck(toolId, userId, organizationId);
        if (updateDto.config) {
            await this.validateToolConfiguration(tool.type, updateDto.config);
        }
        if (updateDto.inputSchema) {
            this.validateSchema(updateDto.inputSchema);
        }
        if (updateDto.outputSchema) {
            this.validateSchema(updateDto.outputSchema);
        }
        const hasBreakingChanges = this.detectBreakingChanges(tool, updateDto);
        const updatedTool = await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                ...updateDto,
                updatedAt: new Date(),
            },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
                organization: true,
                versions: {
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                },
            },
        });
        if (hasBreakingChanges || updateDto.config || updateDto.inputSchema || updateDto.outputSchema) {
            const latestVersion = updatedTool.versions[0];
            const newVersion = this.incrementVersion(latestVersion.version, hasBreakingChanges);
            await this.createToolVersion(toolId, {
                version: newVersion,
                description: `Updated to version ${newVersion}`,
                changes: this.generateChangeLog(tool, updateDto),
                config: updatedTool.config,
                inputSchema: updatedTool.inputSchema,
                outputSchema: updatedTool.outputSchema,
                requirements: updatedTool.requirements,
                breakingChanges: hasBreakingChanges ? this.getBreakingChangesList(tool, updateDto) : [],
            }, userId);
        }
        await this.cacheManager.set(`tool:${toolId}`, updatedTool, 300000);
        this.eventEmitter.emit('tool.updated', {
            toolId,
            version: updatedTool.version,
            changes: this.generateChangeLog(tool, updateDto),
            hasBreakingChanges,
        });
        this.logger.log(`Updated tool: ${toolId}`);
        return updatedTool;
    }
    async deleteTool(toolId, userId, organizationId) {
        const tool = await this.getToolWithPermissionCheck(toolId, userId, organizationId);
        const activeExecutions = await this.prisma.toolExecution.count({
            where: {
                toolId,
                status: {
                    in: ['PENDING', 'RUNNING'],
                },
            },
        });
        if (activeExecutions > 0) {
            throw new common_1.BadRequestException('Cannot delete tool with active executions');
        }
        await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                isActive: false,
                updatedAt: new Date(),
            },
        });
        await this.cacheManager.del(`tool:${toolId}`);
        this.eventEmitter.emit('tool.deleted', {
            toolId,
            organizationId,
            deletedBy: userId,
        });
        this.logger.log(`Deleted tool: ${toolId}`);
    }
    async getTool(toolId, organizationId) {
        let tool = await this.cacheManager.get(`tool:${toolId}`);
        if (!tool) {
            tool = await this.prisma.toolDefinition.findFirst({
                where: {
                    id: toolId,
                    isActive: true,
                    OR: [
                        { isPublic: true },
                        { organizationId },
                    ],
                },
                include: {
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true },
                    },
                    organization: true,
                    versions: {
                        orderBy: { createdAt: 'desc' },
                        take: 5,
                    },
                    analytics: {
                        orderBy: { date: 'desc' },
                        take: 30,
                    },
                    _count: {
                        select: {
                            executions: true,
                            versions: true,
                            cache: true,
                        },
                    },
                },
            });
            if (tool) {
                await this.cacheManager.set(`tool:${toolId}`, tool, 300000);
            }
        }
        if (!tool) {
            throw new common_1.NotFoundException('Tool not found');
        }
        return tool;
    }
    async searchTools(options) {
        const where = {
            isActive: options.isActive !== false,
        };
        if (options.query) {
            where.OR = [
                { name: { contains: options.query, mode: 'insensitive' } },
                { description: { contains: options.query, mode: 'insensitive' } },
                { tags: { has: options.query } },
            ];
        }
        if (options.type) {
            where.type = options.type;
        }
        if (options.category) {
            where.category = options.category;
        }
        if (options.tags && options.tags.length > 0) {
            where.tags = {
                hasEvery: options.tags,
            };
        }
        if (options.isPublic !== undefined) {
            where.isPublic = options.isPublic;
        }
        if (options.createdBy) {
            where.createdBy = options.createdBy;
        }
        if (options.organizationId) {
            where.OR = [
                { organizationId: options.organizationId },
                { isPublic: true },
            ];
        }
        const orderBy = {};
        const sortBy = options.sortBy || 'createdAt';
        const sortOrder = options.sortOrder || 'desc';
        orderBy[sortBy] = sortOrder;
        const [tools, total] = await Promise.all([
            this.prisma.toolDefinition.findMany({
                where,
                include: {
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true },
                    },
                    _count: {
                        select: {
                            executions: true,
                            versions: true,
                        },
                    },
                },
                orderBy,
                take: options.limit || 20,
                skip: options.offset || 0,
            }),
            this.prisma.toolDefinition.count({ where }),
        ]);
        return {
            tools,
            total,
            hasMore: (options.offset || 0) + tools.length < total,
        };
    }
    async createToolVersion(toolId, versionDto, creatorId) {
        const existingVersion = await this.prisma.toolVersion.findUnique({
            where: {
                toolId_version: {
                    toolId,
                    version: versionDto.version,
                },
            },
        });
        if (existingVersion) {
            throw new common_1.BadRequestException(`Version ${versionDto.version} already exists`);
        }
        const version = await this.prisma.toolVersion.create({
            data: {
                toolId,
                version: versionDto.version,
                description: versionDto.description,
                changes: versionDto.changes,
                config: versionDto.config,
                inputSchema: versionDto.inputSchema,
                outputSchema: versionDto.outputSchema,
                requirements: versionDto.requirements || {},
                isStable: versionDto.isStable || false,
                releaseNotes: versionDto.releaseNotes,
                breakingChanges: versionDto.breakingChanges || [],
                createdBy: creatorId,
            },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
            },
        });
        await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                version: versionDto.version,
                config: versionDto.config,
                inputSchema: versionDto.inputSchema,
                outputSchema: versionDto.outputSchema,
                requirements: versionDto.requirements || {},
            },
        });
        await this.cacheManager.del(`tool:${toolId}`);
        this.logger.log(`Created version ${versionDto.version} for tool: ${toolId}`);
        return version;
    }
    async getToolVersions(toolId, organizationId) {
        await this.getTool(toolId, organizationId);
        return this.prisma.toolVersion.findMany({
            where: { toolId },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async rollbackToVersion(toolId, version, userId, organizationId) {
        await this.getToolWithPermissionCheck(toolId, userId, organizationId);
        const targetVersion = await this.prisma.toolVersion.findUnique({
            where: {
                toolId_version: {
                    toolId,
                    version,
                },
            },
        });
        if (!targetVersion) {
            throw new common_1.NotFoundException(`Version ${version} not found`);
        }
        const updatedTool = await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                version: targetVersion.version,
                config: targetVersion.config,
                inputSchema: targetVersion.inputSchema,
                outputSchema: targetVersion.outputSchema,
                requirements: targetVersion.requirements,
                updatedAt: new Date(),
            },
        });
        const rollbackVersion = this.incrementVersion(updatedTool.version, false, 'patch');
        await this.createToolVersion(toolId, {
            version: rollbackVersion,
            description: `Rollback to version ${version}`,
            changes: [`Rolled back to version ${version}`],
            config: targetVersion.config,
            inputSchema: targetVersion.inputSchema,
            outputSchema: targetVersion.outputSchema,
            requirements: targetVersion.requirements,
        }, userId);
        await this.cacheManager.del(`tool:${toolId}`);
        this.logger.log(`Rolled back tool ${toolId} to version ${version}`);
        return updatedTool;
    }
    async importTool(importOptions, creatorId, organizationId) {
        const toolConfig = await this.parseImportData(importOptions);
        const createDto = {
            ...toolConfig,
            ...importOptions.overrides,
        };
        return this.createTool(createDto, creatorId, organizationId);
    }
    async exportTool(toolId, format, organizationId) {
        const tool = await this.getTool(toolId, organizationId);
        switch (format) {
            case 'json':
                return this.exportAsJSON(tool);
            case 'openapi':
                return this.exportAsOpenAPI(tool);
            case 'postman':
                return this.exportAsPostman(tool);
            default:
                throw new common_1.BadRequestException(`Unsupported export format: ${format}`);
        }
    }
    async getToolWithPermissionCheck(toolId, userId, organizationId) {
        const tool = await this.prisma.toolDefinition.findFirst({
            where: {
                id: toolId,
                OR: [
                    { createdBy: userId },
                    { organizationId },
                ],
            },
        });
        if (!tool) {
            throw new common_1.NotFoundException('Tool not found or access denied');
        }
        return tool;
    }
    async validateToolConfiguration(type, config) {
        switch (type) {
            case client_1.ToolType.API_FETCH:
                this.validateAPIFetchConfig(config);
                break;
            case client_1.ToolType.FUNCTION_CALL:
                this.validateFunctionCallConfig(config);
                break;
            case client_1.ToolType.RAG:
                this.validateRAGConfig(config);
                break;
            default:
                break;
        }
    }
    validateAPIFetchConfig(config) {
        const schema = z.object({
            endpoint: z.string().url(),
            method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
            headers: z.record(z.string(), z.string()).optional(),
            authentication: z.object({
                type: z.enum(['none', 'bearer', 'oauth', 'api_key']),
                config: z.record(z.string(), z.any()).optional(),
            }).optional(),
            requestMapping: z.record(z.string(), z.any()).optional(),
            responseMapping: z.record(z.string(), z.any()).optional(),
        });
        try {
            schema.parse(config);
        }
        catch (error) {
            throw new common_1.BadRequestException(`Invalid API fetch configuration: ${error.message}`);
        }
    }
    validateFunctionCallConfig(config) {
        const schema = z.object({
            code: z.string().min(1),
            runtime: z.enum(['javascript', 'python', 'shell']),
            environment: z.record(z.string(), z.any()).optional(),
            packages: z.array(z.string()).optional(),
        });
        try {
            schema.parse(config);
        }
        catch (error) {
            throw new common_1.BadRequestException(`Invalid function call configuration: ${error.message}`);
        }
    }
    validateRAGConfig(config) {
        const schema = z.object({
            vectorStore: z.object({
                type: z.enum(['pinecone', 'weaviate', 'chromadb', 'local']),
                config: z.record(z.string(), z.any()),
            }),
            embedding: z.object({
                provider: z.string(),
                model: z.string(),
            }),
            retrieval: z.object({
                topK: z.number().min(1).max(100),
                scoreThreshold: z.number().min(0).max(1).optional(),
            }),
        });
        try {
            schema.parse(config);
        }
        catch (error) {
            throw new common_1.BadRequestException(`Invalid RAG configuration: ${error.message}`);
        }
    }
    validateSchema(schema) {
        try {
            if (typeof schema !== 'object' || schema === null) {
                throw new Error('Schema must be an object');
            }
            JSON.stringify(schema);
        }
        catch (error) {
            throw new common_1.BadRequestException(`Invalid schema: ${error.message}`);
        }
    }
    detectBreakingChanges(oldTool, updateDto) {
        if (updateDto.inputSchema && !this.isSchemaCompatible(oldTool.inputSchema, updateDto.inputSchema)) {
            return true;
        }
        if (updateDto.outputSchema && !this.isSchemaCompatible(oldTool.outputSchema, updateDto.outputSchema)) {
            return true;
        }
        if (updateDto.config && this.hasBreakingConfigChanges(oldTool.config, updateDto.config)) {
            return true;
        }
        return false;
    }
    isSchemaCompatible(oldSchema, newSchema) {
        try {
            const oldProps = Object.keys(oldSchema.properties || {});
            const newProps = Object.keys(newSchema.properties || {});
            const oldRequired = oldSchema.required || [];
            const newRequired = newSchema.required || [];
            return oldRequired.every((prop) => newRequired.includes(prop));
        }
        catch {
            return false;
        }
    }
    hasBreakingConfigChanges(oldConfig, newConfig) {
        return false;
    }
    incrementVersion(currentVersion, isBreaking, type) {
        const [major, minor, patch] = currentVersion.split('.').map(Number);
        if (isBreaking || type === 'major') {
            return `${major + 1}.0.0`;
        }
        else if (type === 'minor') {
            return `${major}.${minor + 1}.0`;
        }
        else {
            return `${major}.${minor}.${patch + 1}`;
        }
    }
    generateChangeLog(oldTool, updateDto) {
        const changes = [];
        if (updateDto.name && updateDto.name !== oldTool.name) {
            changes.push(`Renamed from "${oldTool.name}" to "${updateDto.name}"`);
        }
        if (updateDto.description !== undefined && updateDto.description !== oldTool.description) {
            changes.push('Updated description');
        }
        if (updateDto.config) {
            changes.push('Updated configuration');
        }
        if (updateDto.inputSchema) {
            changes.push('Updated input schema');
        }
        if (updateDto.outputSchema) {
            changes.push('Updated output schema');
        }
        if (updateDto.tags) {
            changes.push('Updated tags');
        }
        return changes;
    }
    getBreakingChangesList(oldTool, updateDto) {
        const breakingChanges = [];
        if (updateDto.inputSchema && !this.isSchemaCompatible(oldTool.inputSchema, updateDto.inputSchema)) {
            breakingChanges.push('Input schema has breaking changes');
        }
        if (updateDto.outputSchema && !this.isSchemaCompatible(oldTool.outputSchema, updateDto.outputSchema)) {
            breakingChanges.push('Output schema has breaking changes');
        }
        return breakingChanges;
    }
    async parseImportData(importOptions) {
        switch (importOptions.format) {
            case 'json':
                return JSON.parse(importOptions.data);
            case 'openapi':
                return this.parseOpenAPISpec(importOptions.data);
            case 'postman':
                return this.parsePostmanCollection(importOptions.data);
            case 'curl':
                return this.parseCurlCommand(importOptions.data);
            default:
                throw new common_1.BadRequestException(`Unsupported import format: ${importOptions.format}`);
        }
    }
    parseOpenAPISpec(data) {
        const spec = JSON.parse(data);
        return {
            name: spec.info?.title || 'Imported API Tool',
            description: spec.info?.description,
            type: client_1.ToolType.API_FETCH,
            config: {
                endpoint: spec.servers?.[0]?.url || '',
                method: 'GET',
            },
        };
    }
    parsePostmanCollection(data) {
        const collection = JSON.parse(data);
        return {
            name: collection.info?.name || 'Imported Postman Tool',
            description: collection.info?.description,
            type: client_1.ToolType.API_FETCH,
            config: {},
        };
    }
    parseCurlCommand(data) {
        const urlMatch = data.match(/curl\s+(?:-X\s+\w+\s+)?['"]?([^'")\s]+)/);
        const methodMatch = data.match(/-X\s+(\w+)/);
        return {
            name: 'Imported cURL Tool',
            type: client_1.ToolType.API_FETCH,
            config: {
                endpoint: urlMatch?.[1] || '',
                method: methodMatch?.[1] || 'GET',
            },
        };
    }
    exportAsJSON(tool) {
        return {
            name: tool.name,
            description: tool.description,
            type: tool.type,
            config: tool.config,
            inputSchema: tool.inputSchema,
            outputSchema: tool.outputSchema,
            metadata: {
                version: tool.version,
                tags: tool.tags,
                documentation: tool.documentation,
                examples: tool.examples,
            },
        };
    }
    exportAsOpenAPI(tool) {
        return {
            openapi: '3.0.0',
            info: {
                title: tool.name,
                description: tool.description,
                version: tool.version,
            },
        };
    }
    exportAsPostman(tool) {
        return {
            info: {
                name: tool.name,
                description: tool.description,
            },
        };
    }
    async getToolStats(organizationId) {
        const where = {
            isActive: true,
        };
        if (organizationId) {
            where.OR = [
                { organizationId },
                { isPublic: true },
            ];
        }
        const [totalTools, toolsByType, toolsByCategory, recentTools,] = await Promise.all([
            this.prisma.toolDefinition.count({ where }),
            this.prisma.toolDefinition.groupBy({
                by: ['type'],
                where,
                _count: true,
            }),
            this.prisma.toolDefinition.groupBy({
                by: ['category'],
                where,
                _count: true,
            }),
            this.prisma.toolDefinition.findMany({
                where,
                orderBy: { createdAt: 'desc' },
                take: 5,
                select: {
                    id: true,
                    name: true,
                    type: true,
                    usageCount: true,
                    createdAt: true,
                },
            }),
        ]);
        return {
            totalTools,
            toolsByType: Object.fromEntries(toolsByType.map(item => [item.type, item._count])),
            toolsByCategory: Object.fromEntries(toolsByCategory.map(item => [item.category, item._count])),
            recentTools,
        };
    }
    async getPopularTools(organizationId, limit = 10) {
        const where = {
            isActive: true,
        };
        if (organizationId) {
            where.OR = [
                { organizationId },
                { isPublic: true },
            ];
        }
        return this.prisma.toolDefinition.findMany({
            where,
            orderBy: [
                { usageCount: 'desc' },
                { successRate: 'desc' },
            ],
            take: limit,
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true },
                },
                _count: {
                    select: { executions: true },
                },
            },
        });
    }
};
exports.ToolManagerService = ToolManagerService;
exports.ToolManagerService = ToolManagerService = ToolManagerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, config_1.ConfigService,
        event_emitter_mock_1.MockEventEmitter])
], ToolManagerService);
//# sourceMappingURL=tool-manager.service.js.map