import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ToolManagerService } from './services/tool-manager.service';
import { ToolExecutionService, ToolExecutionContext } from './services/tool-execution.service';

@Injectable()
export class ToolsService {
  constructor(
    private prisma: PrismaService,
    private toolManager: ToolManagerService,
    private toolExecution: ToolExecutionService,
  ) { }

  // ============================================================================
  // MAIN TOOL OPERATIONS
  // ============================================================================

  async findAll(organizationId?: string) {
    return this.toolManager.searchTools({
      organizationId,
      limit: 100,
    });
  }

  async findOne(id: string, organizationId?: string) {
    return this.toolManager.getTool(id, organizationId);
  }

  async create(createToolDto: any, creatorId: string, organizationId?: string) {
    return this.toolManager.createTool(createToolDto, creatorId, organizationId);
  }

  async update(id: string, updateToolDto: any, userId: string, organizationId: string) {
    return this.toolManager.updateTool(id, updateToolDto, userId, organizationId);
  }

  async remove(id: string, userId: string, organizationId: string) {
    return this.toolManager.deleteTool(id, userId, organizationId);
  }

  // ============================================================================
  // TOOL EXECUTION
  // ============================================================================

  async executeTool(toolId: string, input: any, context: Partial<ToolExecutionContext>) {
    const executionContext: ToolExecutionContext = {
      toolId,
      input,
      executorType: context.executorType || 'user',
      executorId: context.executorId,
      sessionId: context.sessionId,
      organizationId: context.organizationId || '',
      metadata: context.metadata,
      timeout: context.timeout,
      retryPolicy: context.retryPolicy,
    };

    return this.toolExecution.executeTool(executionContext);
  }

  // ============================================================================
  // TOOL MANAGEMENT
  // ============================================================================

  async getToolsByType(type: string, organizationId?: string) {
    return this.toolManager.searchTools({
      type: type as any,
      organizationId,
    });
  }

  async getPopularTools(organizationId?: string, limit = 10) {
    return this.toolManager.getPopularTools(organizationId, limit);
  }

  async getToolStats(organizationId?: string) {
    return this.toolManager.getToolStats(organizationId);
  }

  async importTool(importOptions: any, creatorId: string, organizationId?: string) {
    return this.toolManager.importTool(importOptions, creatorId, organizationId);
  }

  async exportTool(toolId: string, format: string, organizationId?: string) {
    return this.toolManager.exportTool(toolId, format, organizationId);
  }
}