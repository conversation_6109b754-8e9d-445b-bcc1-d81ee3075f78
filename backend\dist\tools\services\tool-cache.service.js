"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ToolCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolCacheService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const client_1 = require("@prisma/client");
const event_emitter_mock_1 = require("../../agents/mocks/event-emitter.mock");
const crypto = require("crypto");
let ToolCacheService = ToolCacheService_1 = class ToolCacheService {
    constructor(prisma, cacheManager, configService, eventEmitter) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(ToolCacheService_1.name);
        this.defaultTTL = this.configService.get('TOOL_CACHE_TTL', 3600);
        this.maxCacheSize = this.configService.get('TOOL_MAX_CACHE_SIZE', 100 * 1024 * 1024);
    }
    async get(toolId, input) {
        const inputHash = this.generateInputHash(input);
        const cacheKey = `tool_cache:${toolId}:${inputHash}`;
        try {
            const memoryCached = await this.cacheManager.get(cacheKey);
            if (memoryCached) {
                await this.recordCacheHit(toolId, inputHash, 'memory');
                return memoryCached.output;
            }
            const dbCached = await this.prisma.toolCache.findUnique({
                where: {
                    toolId_inputHash: {
                        toolId,
                        inputHash,
                    },
                },
            });
            if (dbCached && this.isCacheValid(dbCached)) {
                await this.prisma.toolCache.update({
                    where: { id: dbCached.id },
                    data: {
                        hits: { increment: 1 },
                        lastAccessed: new Date(),
                    },
                });
                await this.cacheManager.set(cacheKey, dbCached, this.defaultTTL * 1000);
                await this.recordCacheHit(toolId, inputHash, 'database');
                return dbCached.output;
            }
            await this.recordCacheMiss(toolId, inputHash, 'not_found');
            return null;
        }
        catch (error) {
            this.logger.error(`Cache get error for tool ${toolId}:`, error);
            await this.recordCacheMiss(toolId, inputHash, 'error');
            return null;
        }
    }
    async set(toolId, input, output, ttl, options) {
        const inputHash = this.generateInputHash(input);
        const cacheKey = `tool_cache:${toolId}:${inputHash}`;
        const finalTTL = ttl || this.defaultTTL;
        const expiresAt = new Date(Date.now() + finalTTL * 1000);
        try {
            const tool = await this.prisma.toolDefinition.findUnique({
                where: { id: toolId },
                select: { cacheStrategy: true, cacheTTL: true },
            });
            if (!tool || tool.cacheStrategy === client_1.ToolCacheStrategy.NONE) {
                return;
            }
            const entrySize = this.calculateEntrySize(input, output);
            if (entrySize > this.maxCacheSize / 10) {
                this.logger.warn(`Cache entry too large for tool ${toolId}: ${entrySize} bytes`);
                return;
            }
            const cacheData = {
                toolId,
                inputHash,
                input,
                output,
                strategy: options?.strategy || tool.cacheStrategy,
                ttl: finalTTL,
                dependencies: options?.dependencies || [],
                dependencyHash: options?.dependencies ? this.generateDependencyHash(options.dependencies) : null,
                size: entrySize,
                expiresAt,
            };
            await this.prisma.toolCache.upsert({
                where: {
                    toolId_inputHash: {
                        toolId,
                        inputHash,
                    },
                },
                update: {
                    output: cacheData.output,
                    ttl: cacheData.ttl,
                    dependencies: cacheData.dependencies,
                    dependencyHash: cacheData.dependencyHash,
                    size: cacheData.size,
                    lastAccessed: new Date(),
                    expiresAt: cacheData.expiresAt,
                    isValid: true,
                },
                create: cacheData,
            });
            await this.cacheManager.set(cacheKey, cacheData, finalTTL * 1000);
            this.eventEmitter.emit('tool.cache.set', {
                toolId,
                inputHash,
                size: entrySize,
                ttl: finalTTL,
            });
            if (Math.random() < 0.01) {
                this.cleanupExpiredEntries().catch(error => this.logger.error('Cache cleanup error:', error));
            }
        }
        catch (error) {
            this.logger.error(`Cache set error for tool ${toolId}:`, error);
        }
    }
    async invalidate(toolId, inputHash, reason = 'manual') {
        let invalidatedCount = 0;
        try {
            if (inputHash) {
                const result = await this.prisma.toolCache.updateMany({
                    where: {
                        toolId,
                        inputHash,
                        isValid: true,
                    },
                    data: {
                        isValid: false,
                        invalidatedBy: reason,
                        invalidatedAt: new Date(),
                    },
                });
                invalidatedCount = result.count;
                const cacheKey = `tool_cache:${toolId}:${inputHash}`;
                await this.cacheManager.del(cacheKey);
            }
            else {
                const result = await this.prisma.toolCache.updateMany({
                    where: {
                        toolId,
                        isValid: true,
                    },
                    data: {
                        isValid: false,
                        invalidatedBy: reason,
                        invalidatedAt: new Date(),
                    },
                });
                invalidatedCount = result.count;
                await this.clearMemoryCacheForTool(toolId);
            }
            this.logger.log(`Invalidated ${invalidatedCount} cache entries for tool ${toolId}`);
            this.eventEmitter.emit('tool.cache.invalidated', {
                toolId,
                inputHash,
                reason,
                count: invalidatedCount,
            });
        }
        catch (error) {
            this.logger.error(`Cache invalidation error for tool ${toolId}:`, error);
        }
        return invalidatedCount;
    }
    async invalidateByDependency(dependency) {
        try {
            const dependencyHash = this.generateDependencyHash([dependency]);
            const result = await this.prisma.toolCache.updateMany({
                where: {
                    dependencyHash,
                    isValid: true,
                },
                data: {
                    isValid: false,
                    invalidatedBy: `dependency_${dependency}`,
                    invalidatedAt: new Date(),
                },
            });
            this.logger.log(`Invalidated ${result.count} cache entries due to dependency: ${dependency}`);
            return result.count;
        }
        catch (error) {
            this.logger.error(`Dependency invalidation error for ${dependency}:`, error);
            return 0;
        }
    }
    async cleanupExpiredEntries() {
        try {
            const now = new Date();
            const expiredEntries = await this.prisma.toolCache.findMany({
                where: {
                    OR: [
                        { expiresAt: { lt: now } },
                        { isValid: false },
                    ],
                },
                select: { id: true, toolId: true, inputHash: true },
            });
            if (expiredEntries.length === 0) {
                return 0;
            }
            const deleteResult = await this.prisma.toolCache.deleteMany({
                where: {
                    id: {
                        in: expiredEntries.map(entry => entry.id),
                    },
                },
            });
            for (const entry of expiredEntries) {
                const cacheKey = `tool_cache:${entry.toolId}:${entry.inputHash}`;
                await this.cacheManager.del(cacheKey);
            }
            this.logger.log(`Cleaned up ${deleteResult.count} expired cache entries`);
            return deleteResult.count;
        }
        catch (error) {
            this.logger.error('Cache cleanup error:', error);
            return 0;
        }
    }
    async optimizeCache() {
        try {
            const stats = await this.getCacheStats();
            if (stats.totalSize > this.maxCacheSize * 0.8) {
                await this.evictLeastUsedEntries();
            }
            await this.updateCacheAnalytics();
        }
        catch (error) {
            this.logger.error('Cache optimization error:', error);
        }
    }
    async evictLeastUsedEntries() {
        const entriesToEvict = await this.prisma.toolCache.findMany({
            where: { isValid: true },
            orderBy: [
                { hits: 'asc' },
                { lastAccessed: 'asc' },
            ],
            take: 100,
            select: { id: true, toolId: true, inputHash: true },
        });
        if (entriesToEvict.length === 0) {
            return 0;
        }
        const deleteResult = await this.prisma.toolCache.deleteMany({
            where: {
                id: {
                    in: entriesToEvict.map(entry => entry.id),
                },
            },
        });
        for (const entry of entriesToEvict) {
            const cacheKey = `tool_cache:${entry.toolId}:${entry.inputHash}`;
            await this.cacheManager.del(cacheKey);
        }
        this.logger.log(`Evicted ${deleteResult.count} least used cache entries`);
        return deleteResult.count;
    }
    async getCacheStats(toolId) {
        const where = toolId ? { toolId } : {};
        const [totalEntries, totalSize, hitStats,] = await Promise.all([
            this.prisma.toolCache.count({ where: { ...where, isValid: true } }),
            this.prisma.toolCache.aggregate({
                where: { ...where, isValid: true },
                _sum: { size: true },
            }),
            this.prisma.toolCache.aggregate({
                where: { ...where, isValid: true },
                _sum: { hits: true },
            }),
        ]);
        const totalHits = hitStats._sum.hits || 0;
        const estimatedRequests = totalHits + (totalEntries * 0.1);
        const hitRate = estimatedRequests > 0 ? totalHits / estimatedRequests : 0;
        return {
            hits: totalHits,
            misses: Math.max(0, estimatedRequests - totalHits),
            hitRate,
            totalSize: totalSize._sum.size || 0,
            entryCount: totalEntries,
            avgEntrySize: totalEntries > 0 ? (totalSize._sum.size || 0) / totalEntries : 0,
        };
    }
    async getToolCacheStats(toolId) {
        const [basicStats, strategyStats, recentActivity,] = await Promise.all([
            this.getCacheStats(toolId),
            this.prisma.toolCache.groupBy({
                by: ['strategy'],
                where: { toolId, isValid: true },
                _count: true,
                _sum: { hits: true, size: true },
            }),
            this.prisma.toolCache.findMany({
                where: { toolId, isValid: true },
                orderBy: { lastAccessed: 'desc' },
                take: 10,
                select: {
                    inputHash: true,
                    hits: true,
                    lastAccessed: true,
                    size: true,
                },
            }),
        ]);
        return {
            ...basicStats,
            byStrategy: Object.fromEntries(strategyStats.map(stat => [
                stat.strategy,
                {
                    count: stat._count,
                    hits: stat._sum.hits || 0,
                    size: stat._sum.size || 0,
                },
            ])),
            recentActivity,
        };
    }
    async updateCacheAnalytics() {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tools = await this.prisma.toolDefinition.findMany({
            where: { isActive: true },
            select: { id: true },
        });
        for (const tool of tools) {
            const stats = await this.getCacheStats(tool.id);
            await this.prisma.toolAnalytics.upsert({
                where: {
                    toolId_date_hour: {
                        toolId: tool.id,
                        date: today,
                        hour: null,
                    },
                },
                update: {
                    cacheHits: stats.hits,
                    cacheMisses: stats.misses,
                    cacheHitRate: stats.hitRate,
                },
                create: {
                    toolId: tool.id,
                    date: today,
                    cacheHits: stats.hits,
                    cacheMisses: stats.misses,
                    cacheHitRate: stats.hitRate,
                },
            });
        }
    }
    generateInputHash(input) {
        const inputString = JSON.stringify(input, Object.keys(input).sort());
        return crypto.createHash('sha256').update(inputString).digest('hex');
    }
    generateDependencyHash(dependencies) {
        const depString = dependencies.sort().join('|');
        return crypto.createHash('sha256').update(depString).digest('hex');
    }
    calculateEntrySize(input, output) {
        const inputSize = JSON.stringify(input).length;
        const outputSize = JSON.stringify(output).length;
        return inputSize + outputSize;
    }
    isCacheValid(cacheEntry) {
        if (!cacheEntry.isValid) {
            return false;
        }
        if (cacheEntry.expiresAt && new Date() > cacheEntry.expiresAt) {
            return false;
        }
        return true;
    }
    async recordCacheHit(toolId, inputHash, source) {
        this.eventEmitter.emit('tool.cache.hit', {
            toolId,
            inputHash,
            source,
            savedTime: 100,
        });
    }
    async recordCacheMiss(toolId, inputHash, reason) {
        this.eventEmitter.emit('tool.cache.miss', {
            toolId,
            inputHash,
            reason,
        });
    }
    async clearMemoryCacheForTool(toolId) {
        const entries = await this.prisma.toolCache.findMany({
            where: { toolId },
            select: { inputHash: true },
        });
        for (const entry of entries) {
            const cacheKey = `tool_cache:${toolId}:${entry.inputHash}`;
            await this.cacheManager.del(cacheKey);
        }
    }
    async clearAllCache() {
        try {
            const result = await this.prisma.toolCache.deleteMany({});
            this.logger.log(`Cleared all cache: ${result.count} entries`);
            return result.count;
        }
        catch (error) {
            this.logger.error('Clear all cache error:', error);
            return 0;
        }
    }
    async getCacheHealth() {
        try {
            const [totalStats, oldestEntry, newestEntry, topTools,] = await Promise.all([
                this.getCacheStats(),
                this.prisma.toolCache.findFirst({
                    where: { isValid: true },
                    orderBy: { lastAccessed: 'asc' },
                    select: { lastAccessed: true },
                }),
                this.prisma.toolCache.findFirst({
                    where: { isValid: true },
                    orderBy: { lastAccessed: 'desc' },
                    select: { lastAccessed: true },
                }),
                this.prisma.toolCache.groupBy({
                    by: ['toolId'],
                    where: { isValid: true },
                    _count: true,
                    _sum: { hits: true },
                    orderBy: { _sum: { hits: 'desc' } },
                    take: 5,
                }),
            ]);
            return {
                ...totalStats,
                health: {
                    status: totalStats.hitRate > 0.5 ? 'healthy' : 'needs_attention',
                    oldestEntry: oldestEntry?.lastAccessed,
                    newestEntry: newestEntry?.lastAccessed,
                    topCachedTools: topTools.map(tool => ({
                        toolId: tool.toolId,
                        entries: tool._count,
                        hits: tool._sum.hits || 0,
                    })),
                },
            };
        }
        catch (error) {
            this.logger.error('Cache health check error:', error);
            return {
                status: 'error',
                error: error.message,
            };
        }
    }
};
exports.ToolCacheService = ToolCacheService;
exports.ToolCacheService = ToolCacheService = ToolCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, config_1.ConfigService,
        event_emitter_mock_1.MockEventEmitter])
], ToolCacheService);
//# sourceMappingURL=tool-cache.service.js.map