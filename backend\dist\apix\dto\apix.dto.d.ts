export declare enum ClientType {
    WEB_APP = "WEB_APP",
    MOBILE_APP = "MOBILE_APP",
    SDK_WIDGET = "SDK_WIDGET",
    API_CLIENT = "API_CLIENT",
    INTERNAL_SERVICE = "INTERNAL_SERVICE"
}
export declare enum ConnectionStatus {
    CONNECTED = "CONNECTED",
    DISCONNECTED = "DISCONNECTED",
    RECONNECTING = "RECONNECTING",
    SUSPENDED = "SUSPENDED"
}
export declare enum ChannelType {
    AGENT_EVENTS = "AGENT_EVENTS",
    TOOL_EVENTS = "TOOL_EVENTS",
    WORKFLOW_EVENTS = "WORKFLOW_EVENTS",
    PROVIDER_EVENTS = "PROVIDER_EVENTS",
    SYSTEM_EVENTS = "SYSTEM_EVENTS",
    SESSION_EVENTS = "SESSION_EVENTS",
    PRIVATE_USER = "PRIVATE_USER",
    ORGANIZATION = "ORGANIZATION",
    PUBLIC = "PUBLIC"
}
export declare enum EventPriority {
    LOW = "LOW",
    NORMAL = "NORMAL",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
export declare class ApiXConnectionDto {
    sessionId: string;
    clientType: ClientType;
    token: string;
    organizationId?: string;
    subscriptions?: string[];
    metadata?: Record<string, any>;
}
export declare class ApiXEventDto {
    type: string;
    channel: string;
    payload: any;
    sessionId?: string;
    correlationId?: string;
    priority?: EventPriority;
    compressed?: boolean;
    metadata?: Record<string, any>;
}
export declare class ApiXSubscriptionDto {
    channels: string[];
    filters?: Record<string, any>;
    acknowledgment?: boolean;
}
export declare class ApiXChannelDto {
    name: string;
    type: ChannelType;
    organizationId?: string;
    permissions?: Record<string, any>;
    metadata?: Record<string, any>;
}
export declare class ApiXLatencyMetricDto {
    connectionId: string;
    eventType: string;
    latencyMs: number;
    organizationId: string;
}
export declare class ApiXEventReplayDto {
    since?: string;
    eventTypes?: string[];
    limit?: number;
}
export declare class ApiXHeartbeatDto {
    timestamp: number;
    latency?: number;
}
export declare class ApiXErrorDto {
    message: string;
    code?: string;
    stack?: string;
    context?: Record<string, any>;
}
