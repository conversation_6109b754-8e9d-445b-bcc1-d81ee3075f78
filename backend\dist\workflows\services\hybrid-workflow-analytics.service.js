"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var HybridWorkflowAnalyticsService_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HybridWorkflowAnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const event_emitter_1 = require("@nestjs/event-emitter");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
let HybridWorkflowAnalyticsService = HybridWorkflowAnalyticsService_1 = class HybridWorkflowAnalyticsService {
    constructor(prisma, eventEmitter, cacheManager) {
        this.prisma = prisma;
        this.eventEmitter = eventEmitter;
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(HybridWorkflowAnalyticsService_1.name);
        this.metricsCache = new Map();
        this.realTimeMetrics = new Map();
        this.initializeRealTimeMetrics();
    }
    async getHybridAnalytics(organizationId, timeRange, filters) {
        try {
            this.logger.log(`Generating hybrid analytics for organization: ${organizationId}`);
            const cacheKey = `hybrid_analytics:${organizationId}:${timeRange.start.getTime()}:${timeRange.end.getTime()}`;
            let metrics = await this.cacheManager.get(cacheKey);
            if (!metrics) {
                metrics = await this.generateAnalytics(organizationId, timeRange, filters);
                await this.cacheManager.set(cacheKey, metrics, 300000);
            }
            return metrics;
        }
        catch (error) {
            this.logger.error(`Failed to get hybrid analytics: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getExecutionPatternAnalysis(organizationId, timeRange) {
        try {
            const executions = await this.prisma.hybridExecutionAnalytics.findMany({
                where: {
                    organizationId,
                    createdAt: {
                        gte: timeRange.start,
                        lte: timeRange.end,
                    },
                },
                include: {
                    workflow: {
                        select: { name: true },
                    },
                },
            });
            const patternMap = new Map();
            executions.forEach(execution => {
                const pattern = execution.executionPattern;
                if (!patternMap.has(pattern)) {
                    patternMap.set(pattern, {
                        pattern,
                        executions: [],
                        agents: new Set(),
                        tools: new Set(),
                    });
                }
                const patternData = patternMap.get(pattern);
                patternData.executions.push(execution);
                patternData.agents.add(execution.agentId);
                execution.toolIds.forEach((toolId) => patternData.tools.add(toolId));
            });
            const patterns = [];
            for (const [pattern, data] of patternMap.entries()) {
                const successfulExecutions = data.executions.filter((e) => e.success).length;
                const totalDuration = data.executions.reduce((sum, e) => sum + e.duration, 0);
                patterns.push({
                    pattern,
                    count: data.executions.length,
                    successRate: data.executions.length > 0 ? successfulExecutions / data.executions.length : 0,
                    averageDuration: data.executions.length > 0 ? totalDuration / data.executions.length : 0,
                    commonAgents: Array.from(data.agents).slice(0, 5),
                    commonTools: Array.from(data.tools).slice(0, 10),
                    trends: this.calculatePatternTrends(data.executions, timeRange),
                });
            }
            return patterns.sort((a, b) => b.count - a.count);
        }
        catch (error) {
            this.logger.error(`Failed to get execution pattern analysis: ${error.message}`, error.stack);
            return [];
        }
    }
    async getComponentAnalytics(organizationId, componentType, timeRange) {
        try {
            const executions = await this.prisma.hybridExecutionAnalytics.findMany({
                where: {
                    organizationId,
                    createdAt: {
                        gte: timeRange.start,
                        lte: timeRange.end,
                    },
                },
            });
            const componentMap = new Map();
            executions.forEach(execution => {
                if (componentType === 'agent') {
                    const agentId = execution.agentId;
                    if (!componentMap.has(agentId)) {
                        componentMap.set(agentId, {
                            componentId: agentId,
                            executions: [],
                            partners: new Map(),
                        });
                    }
                    const agentData = componentMap.get(agentId);
                    agentData.executions.push(execution);
                    execution.toolIds.forEach((toolId) => {
                        agentData.partners.set(toolId, (agentData.partners.get(toolId) || 0) + 1);
                    });
                }
                else {
                    execution.toolIds.forEach((toolId) => {
                        if (!componentMap.has(toolId)) {
                            componentMap.set(toolId, {
                                componentId: toolId,
                                executions: [],
                                partners: new Map(),
                            });
                        }
                        const toolData = componentMap.get(toolId);
                        toolData.executions.push(execution);
                        toolData.partners.set(execution.agentId, (toolData.partners.get(execution.agentId) || 0) + 1);
                    });
                }
            });
            const analytics = [];
            for (const [componentId, data] of componentMap.entries()) {
                const successfulExecutions = data.executions.filter((e) => e.success).length;
                const totalDuration = data.executions.reduce((sum, e) => sum + e.duration, 0);
                const totalExecutions = data.executions.length;
                const componentName = await this.getComponentName(componentId, componentType, organizationId);
                const successRate = totalExecutions > 0 ? successfulExecutions / totalExecutions : 0;
                const avgDuration = totalExecutions > 0 ? totalDuration / totalExecutions : 0;
                const performanceScore = this.calculatePerformanceScore(successRate, avgDuration, totalExecutions);
                const commonPartners = Array.from(data.partners.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 5)
                    .map(([partnerId, count]) => ({
                    id: partnerId,
                    name: partnerId,
                    count,
                }));
                analytics.push({
                    componentId,
                    componentType,
                    name: componentName,
                    totalExecutions,
                    successfulExecutions,
                    averageExecutionTime: avgDuration,
                    errorRate: 1 - successRate,
                    utilizationRate: this.calculateUtilizationRate(totalExecutions, timeRange),
                    performanceScore,
                    trends: this.calculateComponentTrends(data.executions, timeRange),
                    commonPartners,
                });
            }
            return analytics.sort((a, b) => b.totalExecutions - a.totalExecutions);
        }
        catch (error) {
            this.logger.error(`Failed to get component analytics: ${error.message}`, error.stack);
            return [];
        }
    }
    async getPerformanceInsights(organizationId, timeRange) {
        try {
            const executions = await this.prisma.hybridExecutionAnalytics.findMany({
                where: {
                    organizationId,
                    createdAt: {
                        gte: timeRange.start,
                        lte: timeRange.end,
                    },
                },
                orderBy: { createdAt: 'asc' },
            });
            const bottlenecks = this.identifyBottlenecks(executions);
            const optimizations = this.identifyOptimizations(executions);
            const trends = this.analyzeTrends(executions, timeRange);
            return { bottlenecks, optimizations, trends };
        }
        catch (error) {
            this.logger.error(`Failed to get performance insights: ${error.message}`, error.stack);
            return { bottlenecks: [], optimizations: [], trends: [] };
        }
    }
    async getRealTimeMetrics(organizationId) {
        try {
            const realTimeKey = `realtime:${organizationId}`;
            const metrics = this.realTimeMetrics.get(realTimeKey) || {
                activeExecutions: 0,
                executionsPerMinute: 0,
                averageResponseTime: 0,
                errorRate: 0,
                topPatterns: [],
            };
            return metrics;
        }
        catch (error) {
            this.logger.error(`Failed to get real-time metrics: ${error.message}`, error.stack);
            return {
                activeExecutions: 0,
                executionsPerMinute: 0,
                averageResponseTime: 0,
                errorRate: 0,
                topPatterns: [],
            };
        }
    }
    async generateAnalytics(organizationId, timeRange, filters) {
        const where = {
            organizationId,
            createdAt: {
                gte: timeRange.start,
                lte: timeRange.end,
            },
        };
        if (filters?.agentIds?.length) {
            where.agentId = { in: filters.agentIds };
        }
        if (filters?.executionPatterns?.length) {
            where.executionPattern = { in: filters.executionPatterns };
        }
        const executions = await this.prisma.hybridExecutionAnalytics.findMany({
            where,
            include: {
                workflow: {
                    select: { name: true },
                },
            },
        });
        const humanInputs = await this.prisma.workflowHumanInput.findMany({
            where: {
                organizationId,
                createdAt: {
                    gte: timeRange.start,
                    lte: timeRange.end,
                },
            },
        });
        const totalExecutions = executions.length;
        const successfulExecutions = executions.filter(e => e.success).length;
        const failedExecutions = totalExecutions - successfulExecutions;
        const durations = executions.map(e => e.duration).sort((a, b) => a - b);
        const averageExecutionTime = durations.length > 0
            ? durations.reduce((sum, d) => sum + d, 0) / durations.length
            : 0;
        const patternDistribution = executions.reduce((acc, e) => {
            acc[e.executionPattern] = (acc[e.executionPattern] || 0) + 1;
            return acc;
        }, {});
        const agentUtilization = executions.reduce((acc, e) => {
            acc[e.agentId] = (acc[e.agentId] || 0) + 1;
            return acc;
        }, {});
        const toolUtilization = executions.reduce((acc, e) => {
            e.toolIds.forEach((toolId) => {
                acc[toolId] = (acc[toolId] || 0) + 1;
            });
            return acc;
        }, {});
        const errors = executions.filter(e => !e.success);
        const errorTypes = errors.reduce((acc, e) => {
            const errorType = this.categorizeError(e.errorMessage);
            acc[errorType] = (acc[errorType] || 0) + 1;
            return acc;
        }, {});
        const commonErrors = Object.entries(errorTypes)
            .map(([message, count]) => ({ message, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        const performanceMetrics = {
            p50: durations[Math.floor(durations.length * 0.5)] || 0,
            p95: durations[Math.floor(durations.length * 0.95)] || 0,
            p99: durations[Math.floor(durations.length * 0.99)] || 0,
            minDuration: durations[0] || 0,
            maxDuration: durations[durations.length - 1] || 0,
        };
        const contextSharingStats = {
            totalShares: 0,
            averageShareSize: 0,
            sharesByStrategy: {},
        };
        const totalHumanRequests = humanInputs.length;
        const completedRequests = humanInputs.filter(h => h.status === 'COMPLETED').length;
        const skippedRequests = humanInputs.filter(h => h.status === 'SKIPPED').length;
        const timedOutRequests = humanInputs.filter(h => h.status === 'EXPIRED').length;
        const completedWithTimes = humanInputs.filter(h => h.completedAt && h.status === 'COMPLETED');
        const averageResponseTime = completedWithTimes.length > 0
            ? completedWithTimes.reduce((sum, h) => sum + (h.completedAt.getTime() - h.createdAt.getTime()), 0) / completedWithTimes.length
            : 0;
        const humanInteractionStats = {
            totalRequests: totalHumanRequests,
            completionRate: totalHumanRequests > 0 ? completedRequests / totalHumanRequests : 0,
            averageResponseTime: averageResponseTime / 1000,
            skipRate: totalHumanRequests > 0 ? skippedRequests / totalHumanRequests : 0,
            timeoutRate: totalHumanRequests > 0 ? timedOutRequests / totalHumanRequests : 0,
        };
        const resourceUtilization = {
            averageMemoryUsage: 0,
            averageTokenUsage: 0,
            costAnalysis: {
                totalCost: 0,
                costByComponent: {},
                costTrends: [],
            },
        };
        return {
            totalExecutions,
            successfulExecutions,
            failedExecutions,
            averageExecutionTime,
            patternDistribution,
            agentUtilization,
            toolUtilization,
            errorAnalysis: {
                totalErrors: errors.length,
                errorTypes,
                errorRate: totalExecutions > 0 ? errors.length / totalExecutions : 0,
                commonErrors,
            },
            performanceMetrics,
            contextSharingStats,
            humanInteractionStats,
            resourceUtilization,
        };
    }
    calculatePatternTrends(executions, timeRange) {
        const days = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24));
        const trends = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(timeRange.start.getTime() + i * 24 * 60 * 60 * 1000);
            const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
            const dayExecutions = executions.filter(e => e.createdAt >= dayStart && e.createdAt < dayEnd);
            const successfulCount = dayExecutions.filter(e => e.success).length;
            trends.push({
                date: dayStart.toISOString().split('T')[0],
                count: dayExecutions.length,
                successRate: dayExecutions.length > 0 ? successfulCount / dayExecutions.length : 0,
            });
        }
        return trends;
    }
    calculateComponentTrends(executions, timeRange) {
        const days = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60 * 24));
        const trends = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(timeRange.start.getTime() + i * 24 * 60 * 60 * 1000);
            const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
            const dayExecutions = executions.filter(e => e.createdAt >= dayStart && e.createdAt < dayEnd);
            const successfulCount = dayExecutions.filter(e => e.success).length;
            trends.push({
                date: dayStart.toISOString().split('T')[0],
                executions: dayExecutions.length,
                successRate: dayExecutions.length > 0 ? successfulCount / dayExecutions.length : 0,
            });
        }
        return trends;
    }
    async getComponentName(componentId, componentType, organizationId) {
        try {
            if (componentType === 'agent') {
                const agent = await this.prisma.agent.findFirst({
                    where: { id: componentId, organizationId },
                    select: { name: true },
                });
                return agent?.name || componentId;
            }
            else {
                const tool = await this.prisma.tool.findFirst({
                    where: { id: componentId, organizationId },
                    select: { name: true },
                });
                return tool?.name || componentId;
            }
        }
        catch {
            return componentId;
        }
    }
    calculatePerformanceScore(successRate, avgDuration, totalExecutions) {
        const successScore = successRate * 40;
        const speedScore = Math.max(0, 40 - (avgDuration / 1000) * 2);
        const volumeScore = Math.min(20, totalExecutions / 10);
        return Math.round(successScore + speedScore + volumeScore);
    }
    calculateUtilizationRate(totalExecutions, timeRange) {
        const hours = (timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60);
        return totalExecutions / Math.max(1, hours);
    }
    identifyBottlenecks(executions) {
        const bottlenecks = [];
        const componentTimes = new Map();
        executions.forEach(execution => {
            if (!componentTimes.has(execution.agentId)) {
                componentTimes.set(execution.agentId, []);
            }
            componentTimes.get(execution.agentId).push(execution.duration);
            execution.toolIds.forEach((toolId) => {
                if (!componentTimes.has(toolId)) {
                    componentTimes.set(toolId, []);
                }
                componentTimes.get(toolId).push(execution.duration / execution.toolIds.length);
            });
        });
        for (const [componentId, times] of componentTimes.entries()) {
            const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
            const p95Time = times.sort((a, b) => a - b)[Math.floor(times.length * 0.95)] || 0;
            if (avgTime > 30000) {
                bottlenecks.push({
                    component: componentId,
                    type: 'slow_execution',
                    impact: Math.min(100, avgTime / 1000),
                    recommendation: `Consider optimizing ${componentId} - average execution time is ${Math.round(avgTime / 1000)}s`,
                });
            }
            if (p95Time > 60000) {
                bottlenecks.push({
                    component: componentId,
                    type: 'high_latency_variance',
                    impact: Math.min(100, p95Time / 1000),
                    recommendation: `${componentId} has high latency variance - investigate timeout issues`,
                });
            }
        }
        return bottlenecks.sort((a, b) => b.impact - a.impact).slice(0, 10);
    }
    identifyOptimizations(executions) {
        const optimizations = [];
        const patternStats = executions.reduce((acc, e) => {
            if (!acc[e.executionPattern]) {
                acc[e.executionPattern] = { total: 0, successful: 0, avgDuration: 0 };
            }
            acc[e.executionPattern].total++;
            if (e.success)
                acc[e.executionPattern].successful++;
            acc[e.executionPattern].avgDuration += e.duration;
            return acc;
        }, {});
        Object.entries(patternStats).forEach(([pattern, stats]) => {
            stats.avgDuration /= stats.total;
            const successRate = stats.successful / stats.total;
            if (successRate < 0.8) {
                optimizations.push({
                    area: `${pattern}_pattern_reliability`,
                    potential: Math.round((0.8 - successRate) * 100),
                    description: `${pattern} pattern has ${Math.round(successRate * 100)}% success rate - consider improving error handling`,
                });
            }
            if (stats.avgDuration > 20000) {
                optimizations.push({
                    area: `${pattern}_pattern_performance`,
                    potential: Math.round(stats.avgDuration / 1000),
                    description: `${pattern} pattern averages ${Math.round(stats.avgDuration / 1000)}s - consider parallel execution or caching`,
                });
            }
        });
        return optimizations.sort((a, b) => b.potential - a.potential).slice(0, 10);
    }
    analyzeTrends(executions, timeRange) {
        const trends = [];
        const midpoint = new Date((timeRange.start.getTime() + timeRange.end.getTime()) / 2);
        const firstHalf = executions.filter(e => e.createdAt < midpoint);
        const secondHalf = executions.filter(e => e.createdAt >= midpoint);
        if (firstHalf.length > 0 && secondHalf.length > 0) {
            const firstSuccessRate = firstHalf.filter(e => e.success).length / firstHalf.length;
            const secondSuccessRate = secondHalf.filter(e => e.success).length / secondHalf.length;
            const successRateChange = ((secondSuccessRate - firstSuccessRate) / firstSuccessRate) * 100;
            trends.push({
                metric: 'success_rate',
                trend: successRateChange > 5 ? 'improving' : successRateChange < -5 ? 'declining' : 'stable',
                change: Math.round(successRateChange),
            });
            const firstAvgDuration = firstHalf.reduce((sum, e) => sum + e.duration, 0) / firstHalf.length;
            const secondAvgDuration = secondHalf.reduce((sum, e) => sum + e.duration, 0) / secondHalf.length;
            const durationChange = ((secondAvgDuration - firstAvgDuration) / firstAvgDuration) * 100;
            trends.push({
                metric: 'execution_time',
                trend: durationChange < -5 ? 'improving' : durationChange > 5 ? 'declining' : 'stable',
                change: Math.round(durationChange),
            });
            const volumeChange = ((secondHalf.length - firstHalf.length) / firstHalf.length) * 100;
            trends.push({
                metric: 'execution_volume',
                trend: volumeChange > 10 ? 'improving' : volumeChange < -10 ? 'declining' : 'stable',
                change: Math.round(volumeChange),
            });
        }
        return trends;
    }
    categorizeError(errorMessage) {
        if (!errorMessage)
            return 'unknown';
        const message = errorMessage.toLowerCase();
        if (message.includes('timeout'))
            return 'timeout';
        if (message.includes('not found'))
            return 'not_found';
        if (message.includes('permission') || message.includes('unauthorized'))
            return 'permission';
        if (message.includes('rate limit'))
            return 'rate_limit';
        if (message.includes('network') || message.includes('connection'))
            return 'network';
        if (message.includes('validation'))
            return 'validation';
        if (message.includes('memory') || message.includes('resource'))
            return 'resource';
        return 'other';
    }
    initializeRealTimeMetrics() {
        setInterval(() => {
            this.updateRealTimeMetrics();
        }, 60000);
    }
    async updateRealTimeMetrics() {
        try {
            const recentExecutions = await this.prisma.hybridExecutionAnalytics.findMany({
                where: {
                    createdAt: {
                        gte: new Date(Date.now() - 5 * 60 * 1000),
                    },
                },
                select: {
                    organizationId: true,
                    executionPattern: true,
                    duration: true,
                    success: true,
                },
            });
            const orgMetrics = new Map();
            recentExecutions.forEach(execution => {
                const orgId = execution.organizationId;
                if (!orgMetrics.has(orgId)) {
                    orgMetrics.set(orgId, {
                        executions: [],
                        patterns: new Map(),
                    });
                }
                const metrics = orgMetrics.get(orgId);
                metrics.executions.push(execution);
                const pattern = execution.executionPattern;
                metrics.patterns.set(pattern, (metrics.patterns.get(pattern) || 0) + 1);
            });
            for (const [orgId, data] of orgMetrics.entries()) {
                const executions = data.executions;
                const successfulExecutions = executions.filter((e) => e.success).length;
                const totalDuration = executions.reduce((sum, e) => sum + e.duration, 0);
                const topPatterns = Array.from(data.patterns.entries())
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 5)
                    .map(([pattern, count]) => ({ pattern, count }));
                this.realTimeMetrics.set(`realtime:${orgId}`, {
                    activeExecutions: executions.length,
                    executionsPerMinute: executions.length,
                    averageResponseTime: executions.length > 0 ? totalDuration / executions.length : 0,
                    errorRate: executions.length > 0 ? (executions.length - successfulExecutions) / executions.length : 0,
                    topPatterns,
                });
            }
        }
        catch (error) {
            this.logger.error(`Failed to update real-time metrics: ${error.message}`, error.stack);
        }
    }
    handleExecutionStarted(payload) {
        const orgId = payload.context.organizationId;
        const realTimeKey = `realtime:${orgId}`;
        const metrics = this.realTimeMetrics.get(realTimeKey) || { activeExecutions: 0 };
        metrics.activeExecutions = (metrics.activeExecutions || 0) + 1;
        this.realTimeMetrics.set(realTimeKey, metrics);
    }
    handleExecutionCompleted(payload) {
        const orgId = payload.context.organizationId;
        const realTimeKey = `realtime:${orgId}`;
        const metrics = this.realTimeMetrics.get(realTimeKey) || { activeExecutions: 0 };
        metrics.activeExecutions = Math.max(0, (metrics.activeExecutions || 0) - 1);
        this.realTimeMetrics.set(realTimeKey, metrics);
    }
    handleExecutionFailed(payload) {
        const orgId = payload.context.organizationId;
        const realTimeKey = `realtime:${orgId}`;
        const metrics = this.realTimeMetrics.get(realTimeKey) || { activeExecutions: 0 };
        metrics.activeExecutions = Math.max(0, (metrics.activeExecutions || 0) - 1);
        this.realTimeMetrics.set(realTimeKey, metrics);
    }
    async exportAnalytics(organizationId, timeRange, format = 'json') {
        const analytics = await this.getHybridAnalytics(organizationId, timeRange);
        switch (format) {
            case 'json':
                return analytics;
            case 'csv':
                return this.convertToCSV(analytics);
            case 'pdf':
                return this.generatePDFReport(analytics);
            default:
                return analytics;
        }
    }
    convertToCSV(analytics) {
        const lines = [
            'Metric,Value',
            `Total Executions,${analytics.totalExecutions}`,
            `Successful Executions,${analytics.successfulExecutions}`,
            `Failed Executions,${analytics.failedExecutions}`,
            `Average Execution Time,${analytics.averageExecutionTime}`,
            `Error Rate,${analytics.errorAnalysis.errorRate}`,
        ];
        return lines.join('\n');
    }
    generatePDFReport(analytics) {
        return {
            message: 'PDF generation not implemented in this example',
            data: analytics,
        };
    }
    clearCache() {
        this.metricsCache.clear();
    }
    getCacheStats() {
        return {
            size: this.metricsCache.size,
            keys: Array.from(this.metricsCache.keys()),
        };
    }
};
exports.HybridWorkflowAnalyticsService = HybridWorkflowAnalyticsService;
__decorate([
    (0, event_emitter_1.OnEvent)('hybrid.hybrid_execution_started'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], HybridWorkflowAnalyticsService.prototype, "handleExecutionStarted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('hybrid.hybrid_execution_completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], HybridWorkflowAnalyticsService.prototype, "handleExecutionCompleted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('hybrid.hybrid_execution_failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], HybridWorkflowAnalyticsService.prototype, "handleExecutionFailed", null);
exports.HybridWorkflowAnalyticsService = HybridWorkflowAnalyticsService = HybridWorkflowAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(2, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, typeof (_a = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _a : Object, Object])
], HybridWorkflowAnalyticsService);
//# sourceMappingURL=hybrid-workflow-analytics.service.js.map