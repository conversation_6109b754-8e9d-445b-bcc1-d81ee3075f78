generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  domain      String?
  settings    J<PERSON>     @default("{}")
  branding    J<PERSON>     @default("{}")
  
  // Security settings
  isActive    Boolean  @default(true)
  maxUsers    Int?
  features    String[] @default([])
  
  // Subscription info
  plan        String   @default("free")
  planExpires DateTime?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users       User[]
  workflows   Workflow[]
  agents      Agent[]
  tools       Tool[]
  providers   Provider[]
  sessions    Session[]
  auditLogs   AuditLog[]
  permissions Permission[]
  apixConnections ApiXConnection[]
  apixEvents      ApiXEvent[]
  apixChannels    ApiXChannel[]
  apixLatencyMetrics ApiXLatencyMetric[]
  
  // Agent system relations
  agentTemplates      AgentTemplate[]
  agentInstances      AgentInstance[]
  agentCollaborations AgentCollaboration[]
  
  // Tool system relations
  toolDefinitions     ToolDefinition[]
  toolCompositions    ToolComposition[]
  toolAPIKeys         ToolAPIKey[]

  @@map("organizations")
}

model User {
  id             String   @id @default(cuid())
  email          String   @unique
  password       String
  firstName      String
  lastName       String
  avatar         String?
  role           Role     @default(VIEWER)
  isActive       Boolean  @default(true)
  lastLoginAt    DateTime?
  preferences    Json     @default("{}")
  
  // MFA fields
  mfaEnabled     Boolean  @default(false)
  mfaSecret      String?
  backupCodes    String[] @default([])
  
  // Security fields
  passwordResetToken String?
  passwordResetExpires DateTime?
  emailVerified  Boolean  @default(false)
  emailVerificationToken String?
  
  // Account security
  loginAttempts  Int      @default(0)
  lockoutUntil   DateTime?
  
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  sessions       Session[]
  auditLogs      AuditLog[]
  refreshTokens  RefreshToken[]
  apiKeys        APIKey[]
  createdWorkflows Workflow[] @relation("WorkflowCreator")
  createdAgents    Agent[]    @relation("AgentCreator")
  createdToolsOld  Tool[]     @relation("ToolCreator")
  apixConnections  ApiXConnection[]
  apixEvents       ApiXEvent[]
  
  // Agent system relations
  createdTemplates     AgentTemplate[]           @relation("TemplateCreator")
  createdInstances     AgentInstance[]           @relation("AgentInstanceCreator") 
  createdCollaborations AgentCollaboration[]     @relation("CollaborationCreator")
  agentSessionsUser    AgentSessionNew[]         @relation("AgentSessionUser")
  templateReviews      AgentTemplateReview[]     @relation("TemplateReviewer")
  
  // Tool system relations
  createdTools         ToolDefinition[]          @relation("ToolCreator")
  toolVersions         ToolVersion[]             @relation("ToolVersionCreator")
  toolCompositions     ToolComposition[]         @relation("ToolCompositionCreator")
  toolAPIKeys          ToolAPIKey[]              @relation("ToolAPIKeyCreator")

  @@map("users")
}

model Session {
  id           String   @id @default(cuid())
  userId       String
  sessionData  Json     @default("{}")
  context      Json     @default("{}")
  memory       Json     @default("{}")
  isActive     Boolean  @default(true)
  expiresAt    DateTime
  ipAddress    String?
  userAgent    String?
  deviceFingerprint String?
  location     Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  workflowExecutions WorkflowExecution[]
  agentSessions      AgentSession[]

  @@map("sessions")
}

model RefreshToken {
  id          String   @id @default(cuid())
  token       String   @unique
  userId      String
  isActive    Boolean  @default(true)
  expiresAt   DateTime
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  revokedAt   DateTime?
  replacedBy  String?

  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

model APIKey {
  id          String   @id @default(cuid())
  name        String
  keyHash     String   @unique
  keyPrefix   String
  userId      String
  permissions String[] @default([])
  isActive    Boolean  @default(true)
  expiresAt   DateTime?
  lastUsedAt  DateTime?
  createdAt   DateTime @default(now())
  revokedAt   DateTime?

  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model Workflow {
  id          String   @id @default(cuid())
  name        String
  description String?
  definition  Json
  version     Int      @default(1)
  isActive    Boolean  @default(true)
  tags        String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  creatorId      String
  creator        User         @relation("WorkflowCreator", fields: [creatorId], references: [id])
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  executions WorkflowExecution[]

  @@map("workflows")
}

model WorkflowExecution {
  id          String            @id @default(cuid())
  status      ExecutionStatus   @default(PENDING)
  input       Json
  output      Json?
  error       String?
  startedAt   DateTime          @default(now())
  completedAt DateTime?
  duration    Int?

  workflowId String
  workflow   Workflow @relation(fields: [workflowId], references: [id], onDelete: Cascade)
  sessionId  String
  session    Session  @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  steps WorkflowStep[]

  @@map("workflow_executions")
}

model WorkflowStep {
  id          String            @id @default(cuid())
  stepId      String
  name        String
  type        String
  status      ExecutionStatus   @default(PENDING)
  input       Json
  output      Json?
  error       String?
  startedAt   DateTime          @default(now())
  completedAt DateTime?
  duration    Int?

  executionId String
  execution   WorkflowExecution @relation(fields: [executionId], references: [id], onDelete: Cascade)

  @@map("workflow_steps")
}

model Agent {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        AgentType @default(STANDALONE)
  config      Json
  skills      Json     @default("[]")
  isActive    Boolean  @default(true)
  version     Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  creatorId      String
  creator        User         @relation("AgentCreator", fields: [creatorId], references: [id])
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  sessions AgentSession[]

  @@map("agents")
}

model AgentTemplate {
  id             String        @id @default(cuid())
  name           String
  category       SkillCategory @default(CUSTOM)
  description    String
  config         Json          @default("{}")
  skills         String[]      @default([])
  systemPrompt   String?
  instructions   String?
  tags           String[]      @default([])
  
  // Template metadata
  isPublic       Boolean       @default(false)
  usage          Int           @default(0)
  rating         Float?
  downloads      Int           @default(0)
  featured       Boolean       @default(false)
  
  // Versioning
  version        String        @default("1.0.0")
  changelog      Json          @default("{}")
  
  // Provider requirements
  providers      String[]      @default([])
  minTokens      Int?
  maxTokens      Int?
  
  // Template config schema
  configSchema   Json          @default("{}")
  
  createdBy      String
  creator        User          @relation("TemplateCreator", fields: [createdBy], references: [id])
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  instances      AgentInstance[]
  reviews        AgentTemplateReview[]
  
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([category, isPublic])
  @@index([rating, usage])
  @@index([createdBy, organizationId])
  @@map("agent_templates")
}

model AgentInstance {
  id             String      @id @default(cuid())
  name           String
  description    String?
  
  templateId     String?
  template       AgentTemplate? @relation(fields: [templateId], references: [id])
  
  type           AgentType   @default(STANDALONE)
  status         AgentStatus @default(ACTIVE)
  
  // Configuration
  config         Json        @default("{}")
  systemPrompt   String?
  instructions   String?
  
  // AI Model settings
  provider       String      @default("openai")
  model          String      @default("gpt-4")
  temperature    Float       @default(0.7)
  maxTokens      Int         @default(2000)
  topP           Float       @default(1.0)
  
  // Memory settings
  memoryType     String      @default("conversation")
  maxMemorySize  Int         @default(10000)
  memoryWindow   Int         @default(20)
  
  // Skills and capabilities
  skills         String[]    @default([])
  tools          String[]    @default([])
  capabilities   Json        @default("{}")
  
  // Performance settings
  timeout        Int         @default(30000)
  retryPolicy    Json        @default("{}")
  rateLimits     Json        @default("{}")
  
  // Collaboration settings
  canCollaborate Boolean     @default(false)
  shareContext   Boolean     @default(false)
  priority       Int         @default(1)
  
  // Version and deployment
  version        String      @default("1.0.0")
  isActive       Boolean     @default(true)
  lastDeployed   DateTime?
  deployConfig   Json        @default("{}")
  
  // Ownership
  creatorId      String
  creator        User        @relation("AgentInstanceCreator", fields: [creatorId], references: [id])
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Relations
  sessions       AgentSessionNew[]
  tasks          AgentTask[]
  collaborations AgentCollaborationMember[]
  metrics        AgentMetrics[]
  conversations  AgentConversation[]
  coordinatedCollaborations AgentCollaboration[] @relation("CollaborationCoordinator")
  assignedTasks  AgentCollaborationTask[] @relation("AssignedTasks")
  
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([organizationId, status])
  @@index([type, status])
  @@index([creatorId, createdAt])
  @@map("agent_instances")
}

model AgentCollaboration {
  id             String               @id @default(cuid())
  name           String
  description    String?
  
  // Coordinator agent
  coordinatorId  String
  coordinator    AgentInstance       @relation("CollaborationCoordinator", fields: [coordinatorId], references: [id])
  
  // Workflow configuration
  workflow       Json                @default("{}")
  sharedContext  Json                @default("{}")
  globalMemory   Json                @default("{}")
  
  // Status and control
  status         CollaborationStatus @default(ACTIVE)
  priority       Int                 @default(1)
  maxAgents      Int                 @default(10)
  
  // Execution settings
  timeout        Int                 @default(300000) // 5 minutes
  errorHandling  Json                @default("{}")
  
  // Ownership
  creatorId      String
  creator        User                @relation("CollaborationCreator", fields: [creatorId], references: [id])
  organizationId String
  organization   Organization        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Relations
  members        AgentCollaborationMember[]
  tasks          AgentCollaborationTask[]
  
  startedAt      DateTime?
  completedAt    DateTime?
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt

  @@index([organizationId, status])
  @@index([coordinatorId, status])
  @@map("agent_collaborations")
}

model AgentCollaborationMember {
  id              String            @id @default(cuid())
  
  collaborationId String
  collaboration   AgentCollaboration @relation(fields: [collaborationId], references: [id], onDelete: Cascade)
  
  agentId         String
  agent           AgentInstance     @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  role            String            @default("participant")
  permissions     String[]          @default([])
  status          AgentStatus       @default(ACTIVE)
  
  // Member-specific config
  config          Json              @default("{}")
  priority        Int               @default(1)
  
  joinedAt        DateTime          @default(now())
  leftAt          DateTime?

  @@unique([collaborationId, agentId])
  @@index([collaborationId, status])
  @@map("agent_collaboration_members")
}

model AgentTask {
  id             String      @id @default(cuid())
  name           String
  description    String?
  
  agentId        String
  agent          AgentInstance @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  sessionId      String?
  session        AgentSessionNew? @relation(fields: [sessionId], references: [id])
  
  // Task details
  type           String      @default("general")
  status         TaskStatus  @default(PENDING)
  priority       Int         @default(1)
  
  // Task data
  input          Json        @default("{}")
  output         Json?
  context        Json        @default("{}")
  metadata       Json        @default("{}")
  
  // Execution details
  startedAt      DateTime?
  completedAt    DateTime?
  duration       Int?        // milliseconds
  
  // Error handling
  error          String?
  errorDetails   Json?
  retryCount     Int         @default(0)
  maxRetries     Int         @default(3)
  
  // Parent/child relationships
  parentTaskId   String?
  parentTask     AgentTask?  @relation("TaskHierarchy", fields: [parentTaskId], references: [id])
  childTasks     AgentTask[] @relation("TaskHierarchy")
  
  // Dependencies
  dependencies   String[]    @default([])
  dependents     String[]    @default([])
  
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([agentId, status])
  @@index([sessionId, status])
  @@index([status, priority])
  @@map("agent_tasks")
}

model AgentCollaborationTask {
  id              String            @id @default(cuid())
  name            String
  description     String?
  
  collaborationId String
  collaboration   AgentCollaboration @relation(fields: [collaborationId], references: [id], onDelete: Cascade)
  
  assignedAgentId String?
  assignedAgent   AgentInstance?    @relation("AssignedTasks", fields: [assignedAgentId], references: [id])
  
  // Task details
  type            String            @default("collaboration")
  status          TaskStatus        @default(PENDING)
  priority        Int               @default(1)
  
  // Task data
  input           Json              @default("{}")
  output          Json?
  requirements    Json              @default("{}")
  
  // Execution
  startedAt       DateTime?
  completedAt     DateTime?
  duration        Int?
  
  // Dependencies
  dependencies    String[]          @default([])
  
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  @@index([collaborationId, status])
  @@index([assignedAgentId, status])
  @@map("agent_collaboration_tasks")
}

model AgentSessionNew {
  id             String        @id @default(cuid())
  
  agentId        String
  agent          AgentInstance @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  userId         String?
  user           User?         @relation("AgentSessionUser", fields: [userId], references: [id])
  
  // Session details
  name           String?
  type           String        @default("conversation")
  status         String        @default("active")
  
  // Memory and context
  memory         Json          @default("{}")
  context        Json          @default("{}")
  metadata       Json          @default("{}")
  
  // Session settings
  maxMessages    Int           @default(100)
  memoryStrategy String        @default("sliding_window")
  
  // Collaboration
  collaborationId String?
  isShared       Boolean       @default(false)
  
  // Performance
  messageCount   Int           @default(0)
  tokenUsage     Json          @default("{}")
  
  // Lifecycle
  startedAt      DateTime      @default(now())
  lastActivityAt DateTime      @default(now())
  endedAt        DateTime?
  expiresAt      DateTime?
  
  // Relations
  messages       AgentMessage[]
  tasks          AgentTask[]
  
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([agentId, status])
  @@index([userId, lastActivityAt])
  @@index([collaborationId])
  @@map("agent_sessions_new")
}

model AgentMessage {
  id        String          @id @default(cuid())
  
  sessionId String
  session   AgentSessionNew @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  
  // Message details
  role      String          // user, assistant, system, tool
  content   String
  type      String          @default("text")
  
  // Metadata
  metadata  Json            @default("{}")
  tokens    Int?
  cost      Float?
  
  // Processing
  processed Boolean         @default(false)
  embedding Float[]
  
  // Relations
  parentId  String?
  parent    AgentMessage?   @relation("MessageThread", fields: [parentId], references: [id])
  replies   AgentMessage[]  @relation("MessageThread")
  
  createdAt DateTime        @default(now())

  @@index([sessionId, createdAt])
  @@index([role, type])
  @@map("agent_messages")
}

model AgentConversation {
  id             String        @id @default(cuid())
  
  agentId        String
  agent          AgentInstance @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  // Conversation metadata
  title          String?
  summary        String?
  tags           String[]      @default([])
  
  // Participants
  participants   Json          @default("{}")
  
  // Status
  status         String        @default("active")
  isArchived     Boolean       @default(false)
  
  // Analytics
  messageCount   Int           @default(0)
  avgResponseTime Float?
  satisfaction   Float?
  
  startedAt      DateTime      @default(now())
  lastMessageAt  DateTime      @default(now())
  endedAt        DateTime?
  
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([agentId, status])
  @@index([lastMessageAt])
  @@map("agent_conversations")
}

model AgentMetrics {
  id                String        @id @default(cuid())
  
  agentId           String
  agent             AgentInstance @relation(fields: [agentId], references: [id], onDelete: Cascade)
  
  // Time period
  date              DateTime      @db.Date
  hour              Int?          // For hourly metrics
  
  // Usage metrics
  conversations     Int           @default(0)
  messages          Int           @default(0)
  totalTokens       Int           @default(0)
  inputTokens       Int           @default(0)
  outputTokens      Int           @default(0)
  
  // Performance metrics
  avgResponseTime   Float?
  minResponseTime   Float?
  maxResponseTime   Float?
  
  // Quality metrics
  successRate       Float?
  errorRate         Float?
  satisfactionScore Float?
  
  // Cost metrics
  totalCost         Float?
  costPerToken      Float?
  costPerMessage    Float?
  
  // Collaboration metrics
  collaborations    Int           @default(0)
  tasksCompleted    Int           @default(0)
  tasksAssigned     Int           @default(0)
  
  createdAt         DateTime      @default(now())

  @@unique([agentId, date, hour])
  @@index([agentId, date])
  @@index([date])
  @@map("agent_metrics")
}

model AgentTemplateReview {
  id         String        @id @default(cuid())
  
  templateId String
  template   AgentTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  
  userId     String
  user       User          @relation("TemplateReviewer", fields: [userId], references: [id])
  
  rating     Int           // 1-5 stars
  review     String?
  pros       String[]      @default([])
  cons       String[]      @default([])
  
  helpful    Int           @default(0)
  reported   Boolean       @default(false)
  
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  @@unique([templateId, userId])
  @@index([templateId, rating])
  @@map("agent_template_reviews")
}

// ============================================================================
// TOOL SYSTEM MODELS
// ============================================================================

model ToolDefinition {
  id             String      @id @default(cuid())
  name           String
  description    String?
  category       SkillCategory @default(CUSTOM)
  
  // Tool configuration
  type           ToolType    @default(FUNCTION_CALL)
  config         Json        @default("{}")
  inputSchema    Json        @default("{}")
  outputSchema   Json        @default("{}")
  
  // Execution settings
  timeout        Int         @default(30000) // 30 seconds
  retryPolicy    Json        @default("{}")
  cacheStrategy  ToolCacheStrategy @default(INPUT_HASH)
  cacheTTL       Int         @default(3600) // 1 hour
  
  // Metadata
  version        String      @default("1.0.0")
  tags           String[]    @default([])
  documentation  String?
  examples       Json        @default("[]")
  
  // Access control
  isPublic       Boolean     @default(false)
  isActive       Boolean     @default(true)
  
  // Dependencies
  dependencies   String[]    @default([])
  requirements   Json        @default("{}")
  
  // Usage tracking
  usageCount     Int         @default(0)
  successRate    Float       @default(1.0)
  avgLatency     Float       @default(0)
  
  // Ownership
  createdBy      String
  creator        User        @relation("ToolCreator", fields: [createdBy], references: [id])
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Relations
  versions       ToolVersion[]
  executions     ToolExecution[]
  compositions   ToolCompositionStep[]
  cache          ToolCache[]
  analytics      ToolAnalytics[]
  apiKeys        ToolAPIKey[]        @relation("ToolAPIKeys")
  
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([type, category])
  @@index([organizationId, isActive])
  @@index([createdBy, createdAt])
  @@index([isPublic, category])
  @@map("tool_definitions")
}

model ToolVersion {
  id             String       @id @default(cuid())
  
  toolId         String
  tool           ToolDefinition @relation(fields: [toolId], references: [id], onDelete: Cascade)
  
  version        String
  description    String?
  changes        String[]     @default([])
  
  // Versioned content
  config         Json
  inputSchema    Json
  outputSchema   Json
  requirements   Json         @default("{}")
  
  // Version metadata
  isStable       Boolean      @default(false)
  isDeprecated   Boolean      @default(false)
  releaseNotes   String?
  
  // Migration info
  migrationPath  Json?
  breakingChanges String[]    @default([])
  
  createdBy      String
  creator        User         @relation("ToolVersionCreator", fields: [createdBy], references: [id])
  
  createdAt      DateTime     @default(now())

  @@unique([toolId, version])
  @@index([toolId, createdAt])
  @@map("tool_versions")
}

model ToolComposition {
  id             String      @id @default(cuid())
  name           String
  description    String?
  
  // Composition flow
  flow           Json        @default("{}")
  variables      Json        @default("{}")
  conditions     Json        @default("{}")
  
  // Execution settings
  parallel       Boolean     @default(false)
  timeout        Int         @default(300000) // 5 minutes
  errorHandling  Json        @default("{}")
  
  // Metadata
  tags           String[]    @default([])
  isTemplate     Boolean     @default(false)
  isActive       Boolean     @default(true)
  
  // Usage tracking
  executionCount Int         @default(0)
  successRate    Float       @default(1.0)
  avgDuration    Float       @default(0)
  
  // Ownership
  createdBy      String
  creator        User        @relation("ToolCompositionCreator", fields: [createdBy], references: [id])
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  // Relations
  steps          ToolCompositionStep[]
  executions     ToolCompositionExecution[]
  
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt

  @@index([organizationId, isActive])
  @@index([createdBy, createdAt])
  @@map("tool_compositions")
}

model ToolCompositionStep {
  id              String         @id @default(cuid())
  
  compositionId   String
  composition     ToolComposition @relation(fields: [compositionId], references: [id], onDelete: Cascade)
  
  toolId          String
  tool            ToolDefinition @relation(fields: [toolId], references: [id])
  
  // Step configuration
  stepIndex       Int
  stepName        String?
  
  // Input/Output mapping
  inputMapping    Json           @default("{}")
  outputMapping   Json           @default("{}")
  
  // Conditional execution
  conditions      Json           @default("{}")
  dependencies    String[]       @default([])
  
  // Error handling
  continueOnError Boolean        @default(false)
  retryCount      Int            @default(0)
  fallbackTool    String?
  
  createdAt       DateTime       @default(now())

  @@unique([compositionId, stepIndex])
  @@index([compositionId, stepIndex])
  @@map("tool_composition_steps")
}

model ToolExecution {
  id             String            @id @default(cuid())
  
  toolId         String?
  tool           ToolDefinition?   @relation(fields: [toolId], references: [id])
  
  compositionId  String?
  
  // Execution context
  executorType   String            @default("user") // user, agent, system, workflow
  executorId     String?
  sessionId      String?
  organizationId String
  
  // Execution details
  status         ToolExecutionStatus @default(PENDING)
  input          Json
  output         Json?
  error          String?
  
  // Performance metrics
  startedAt      DateTime?
  completedAt    DateTime?
  duration       Int?              // milliseconds
  retryCount     Int               @default(0)
  
  // Resource usage
  tokensUsed     Int               @default(0)
  cost           Float             @default(0)
  memoryUsed     Int?              // bytes
  cpuTime        Int?              // milliseconds
  
  // Metadata
  metadata       Json              @default("{}")
  traceId        String?
  parentId       String?
  
  // Caching
  cached         Boolean           @default(false)
  cacheKey       String?
  
  // Relations
  parent         ToolExecution?    @relation("ExecutionHierarchy", fields: [parentId], references: [id])
  children       ToolExecution[]   @relation("ExecutionHierarchy")
  
  createdAt      DateTime          @default(now())

  @@index([toolId, status])
  @@index([organizationId, createdAt])
  @@index([executorId, executorType])
  @@index([sessionId])
  @@index([status, startedAt])
  @@map("tool_executions")
}

model ToolCompositionExecution {
  id             String            @id @default(cuid())
  
  compositionId  String
  composition    ToolComposition   @relation(fields: [compositionId], references: [id], onDelete: Cascade)
  
  // Execution context
  executorType   String            @default("user")
  executorId     String?
  sessionId      String?
  organizationId String
  
  // Execution details
  status         ToolExecutionStatus @default(PENDING)
  input          Json
  output         Json?
  error          String?
  
  // Flow execution
  currentStep    Int               @default(0)
  stepResults    Json              @default("{}")
  variables      Json              @default("{}")
  
  // Performance
  startedAt      DateTime?
  completedAt    DateTime?
  duration       Int?
  totalSteps     Int               @default(0)
  
  // Metadata
  metadata       Json              @default("{}")
  traceId        String?
  
  createdAt      DateTime          @default(now())

  @@index([compositionId, status])
  @@index([organizationId, createdAt])
  @@index([executorId, executorType])
  @@map("tool_composition_executions")
}

model ToolCache {
  id             String         @id @default(cuid())
  
  toolId         String
  tool           ToolDefinition @relation(fields: [toolId], references: [id], onDelete: Cascade)
  
  // Cache key and data
  inputHash      String
  input          Json
  output         Json
  
  // Cache metadata
  strategy       ToolCacheStrategy
  ttl            Int            // seconds
  hits           Int            @default(0)
  lastAccessed   DateTime       @default(now())
  
  // Invalidation
  isValid        Boolean        @default(true)
  invalidatedBy  String?        // Reason for invalidation
  invalidatedAt  DateTime?
  
  // Dependencies for cache invalidation
  dependencies   String[]       @default([])
  dependencyHash String?
  
  // Size and performance
  size           Int?           // bytes
  compressionRatio Float?
  
  createdAt      DateTime       @default(now())
  expiresAt      DateTime

  @@unique([toolId, inputHash])
  @@index([toolId, lastAccessed])
  @@index([expiresAt, isValid])
  @@map("tool_cache")
}

model ToolAnalytics {
  id             String         @id @default(cuid())
  
  toolId         String
  tool           ToolDefinition @relation(fields: [toolId], references: [id], onDelete: Cascade)
  
  // Time period
  date           DateTime       @db.Date
  hour           Int?
  
  // Usage metrics
  executionCount Int            @default(0)
  uniqueUsers    Int            @default(0)
  totalDuration  Int            @default(0)
  avgDuration    Float          @default(0)
  
  // Success/failure rates
  successCount   Int            @default(0)
  errorCount     Int            @default(0)
  timeoutCount   Int            @default(0)
  retryCount     Int            @default(0)
  
  // Performance metrics
  minDuration    Int?
  maxDuration    Int?
  p95Duration    Int?           // 95th percentile
  p99Duration    Int?           // 99th percentile
  
  // Resource usage
  totalTokens    Int            @default(0)
  totalCost      Float          @default(0)
  avgMemoryUsed  Float          @default(0)
  avgCpuTime     Float          @default(0)
  
  // Cache metrics
  cacheHits      Int            @default(0)
  cacheMisses    Int            @default(0)
  cacheHitRate   Float          @default(0)
  
  // Error breakdown
  errorTypes     Json           @default("{}")
  errorMessages  Json           @default("{}")
  
  createdAt      DateTime       @default(now())

  @@unique([toolId, date, hour])
  @@index([toolId, date])
  @@index([date])
  @@map("tool_analytics")
}

model ToolAPIKey {
  id             String         @id @default(cuid())
  
  toolId         String
  tool           ToolDefinition @relation("ToolAPIKeys", fields: [toolId], references: [id], onDelete: Cascade)
  
  name           String
  description    String?
  
  // Key configuration
  keyType        String         @default("external") // external, internal, oauth
  encryptedKey   String
  keyHash        String
  
  // OAuth specific
  oauthConfig    Json?
  refreshToken   String?
  tokenExpiry    DateTime?
  
  // Access control
  permissions    String[]       @default([])
  rateLimits     Json           @default("{}")
  
  // Usage tracking
  lastUsed       DateTime?
  usageCount     Int            @default(0)
  
  // Status
  isActive       Boolean        @default(true)
  expiresAt      DateTime?
  
  createdBy      String
  creator        User           @relation("ToolAPIKeyCreator", fields: [createdBy], references: [id])
  organizationId String
  organization   Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@index([toolId, isActive])
  @@index([organizationId, isActive])
  @@index([keyHash])
  @@map("tool_api_keys")
}

model AgentSession {
  id          String   @id @default(cuid())
  context     Json     @default("{}")
  memory      Json     @default("{}")
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  agentId   String
  agent     Agent   @relation(fields: [agentId], references: [id], onDelete: Cascade)
  sessionId String
  session   Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("agent_sessions")
}

model Tool {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        ToolType @default(FUNCTION_CALL)
  schema      Json
  config      Json     @default("{}")
  isActive    Boolean  @default(true)
  version     Int      @default(1)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  creatorId      String
  creator        User         @relation("ToolCreator", fields: [creatorId], references: [id])
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("tools")
}

model Provider {
  id          String      @id @default(cuid())
  name        String
  type        ProviderType
  config      Json
  isActive    Boolean     @default(true)
  priority    Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("providers")
}

model AuditLog {
  id          String   @id @default(cuid())
  action      String
  resource    String
  resourceId  String?
  details     Json     @default("{}")
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([userId, createdAt])
  @@index([organizationId, createdAt])
  @@index([action, resource])
  @@map("audit_logs")
}

model Permission {
  id             String   @id @default(cuid())
  resource       String   // e.g., 'workflow', 'agent', 'user'
  action         String   // e.g., 'create', 'read', 'update', 'delete'
  scope          String   @default("organization") // 'global', 'organization', 'own'
  conditions     Json     @default("{}")
  organizationId String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([resource, action, scope, organizationId])
  @@map("permissions")
}

enum Role {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum AgentType {
  STANDALONE
  TOOL_DRIVEN
  HYBRID
  MULTI_TASKING
  MULTI_PROVIDER
}

enum AgentStatus {
  ACTIVE
  INACTIVE
  ERROR
  PAUSED
  TESTING
  ARCHIVED
}

enum CollaborationStatus {
  ACTIVE
  PAUSED
  COMPLETED
  FAILED
  CANCELLED
}

enum TaskStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
  PAUSED
}

enum SkillCategory {
  COMMUNICATION
  DATA_ANALYSIS
  AUTOMATION
  CONTENT_GENERATION
  DECISION_MAKING
  INTEGRATION
  CUSTOM
}

enum ToolExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
  TIMEOUT
}

enum ToolCacheStrategy {
  NONE
  INPUT_HASH
  TIME_BASED
  DEPENDENCY_BASED
  CUSTOM
}

enum ToolType {
  FUNCTION_CALL
  RAG
  API_FETCH
  BROWSER_AUTOMATION
  DATABASE
  CUSTOM_LOGIC
}

enum ProviderType {
  OPENAI
  CLAUDE
  GEMINI
  MISTRAL
  GROQ
  DEEPSEEK
  HUGGING_FACE
  OLLAMA
  LOCAL_AI
}

model ApiXConnection {
  id              String           @id @default(cuid())
  sessionId       String           @unique
  organizationId  String
  userId          String?
  clientType      ClientType
  channels        String[]
  metadata        Json             @default("{}")
  connectedAt     DateTime         @default(now())
  lastHeartbeat   DateTime         @default(now())
  status          ConnectionStatus @default(CONNECTED)
  ipAddress       String?
  userAgent       String?
  reconnectCount  Int              @default(0)
  latencyMs       Float?
  
  organization    Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user            User?            @relation(fields: [userId], references: [id], onDelete: Cascade)
  events          ApiXEvent[]
  subscriptions   ApiXSubscription[]
  
  @@map("apix_connections")
}

model ApiXEvent {
  id              String           @id @default(cuid())
  eventType       String
  channel         String
  payload         Json
  sessionId       String?
  connectionId    String?
  organizationId  String?
  userId          String?
  acknowledged    Boolean          @default(false)
  retryCount      Int              @default(0)
  maxRetries      Int              @default(3)
  priority        EventPriority    @default(NORMAL)
  compressed      Boolean          @default(false)
  createdAt       DateTime         @default(now())
  processedAt     DateTime?
  metadata        Json             @default("{}")
  correlationId   String?
  
  connection      ApiXConnection?  @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  organization    Organization?    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user            User?            @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([eventType, channel])
  @@index([organizationId, createdAt])
  @@index([sessionId, createdAt])
  @@map("apix_events")
}

model ApiXChannel {
  id             String      @id @default(cuid())
  name           String      @unique
  type           ChannelType
  organizationId String?
  permissions    Json        @default("{}")
  subscribers    Int         @default(0)
  isActive       Boolean     @default(true)
  metadata       Json        @default("{}")
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  subscriptions  ApiXSubscription[]
  
  @@map("apix_channels")
}

model ApiXSubscription {
  id           String         @id @default(cuid())
  connectionId String
  channelId    String
  filters      Json           @default("{}")
  isActive     Boolean        @default(true)
  createdAt    DateTime       @default(now())
  
  connection   ApiXConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  channel      ApiXChannel    @relation(fields: [channelId], references: [id], onDelete: Cascade)
  
  @@unique([connectionId, channelId])
  @@map("apix_subscriptions")
}

model ApiXLatencyMetric {
  id             String   @id @default(cuid())
  connectionId   String
  eventType      String
  latencyMs      Float
  timestamp      DateTime @default(now())
  organizationId String
  
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  
  @@index([organizationId, timestamp])
  @@index([connectionId, timestamp])
  @@map("apix_latency_metrics")
}

model ApiXMessageQueue {
  id             String        @id @default(cuid())
  connectionId   String
  eventId        String
  priority       EventPriority @default(NORMAL)
  attempts       Int           @default(0)
  maxAttempts    Int           @default(3)
  nextRetryAt    DateTime?
  status         QueueStatus   @default(PENDING)
  createdAt      DateTime      @default(now())
  processedAt    DateTime?
  error          String?
  
  @@index([status, nextRetryAt])
  @@index([connectionId, status])
  @@map("apix_message_queue")
}

enum ClientType {
  WEB_APP
  MOBILE_APP
  SDK_WIDGET
  API_CLIENT
  INTERNAL_SERVICE
}

enum ConnectionStatus {
  CONNECTED
  DISCONNECTED
  RECONNECTING
  SUSPENDED
}

enum ChannelType {
  AGENT_EVENTS
  TOOL_EVENTS
  WORKFLOW_EVENTS
  PROVIDER_EVENTS
  SYSTEM_EVENTS
  SESSION_EVENTS
  PRIVATE_USER
  ORGANIZATION
  PUBLIC
}

enum EventPriority {
  LOW
  NORMAL
  HIGH
  CRITICAL
}

enum QueueStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}