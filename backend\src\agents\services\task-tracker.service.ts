import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ApixGateway } from '../../apix/apix.gateway';
import { TaskStatus } from '@prisma/client';

export interface TaskDefinition {
    name: string;
    description?: string;
    type: string;
    priority: number;
    input: any;
    context?: any;
    metadata?: any;
    dependencies?: string[];
    maxRetries?: number;
    timeout?: number;
}

export interface TaskProgress {
    taskId: string;
    status: TaskStatus;
    progress: number; // 0-100
    message?: string;
    startedAt?: Date;
    estimatedCompletion?: Date;
    output?: any;
    error?: string;
}

@Injectable()
export class TaskTrackerService {
    private readonly logger = new Logger(TaskTrackerService.name);

    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private apixGateway: ApixGateway,
    ) { }

    async analyzeAndCreateTasks(
        agentId: string,
        sessionId: string,
        input: any,
    ): Promise<any[]> {
        // Analyze input and break down into tasks
        const tasks = this.analyzeInput(input);

        const createdTasks = [];

        for (const taskDef of tasks) {
            const task = await this.createTask(agentId, sessionId, taskDef);
            createdTasks.push(task);
        }

        return createdTasks;
    }

    async createTask(
        agentId: string,
        sessionId: string,
        taskDef: TaskDefinition,
    ): Promise<any> {
        const task = await this.prisma.agentTask.create({
            data: {
                name: taskDef.name,
                description: taskDef.description,
                agentId,
                sessionId,
                type: taskDef.type,
                status: TaskStatus.PENDING,
                priority: taskDef.priority,
                input: taskDef.input,
                context: taskDef.context || {},
                metadata: taskDef.metadata || {},
                dependencies: taskDef.dependencies || [],
                maxRetries: taskDef.maxRetries || 3,
            },
        });

        // Cache task for quick access
        await this.cacheManager.set(`task:${task.id}`, task, 3600000);

        await this.apixGateway.emitAgentEvent(
            task.organizationId || 'system',
            agentId,
            'task.created',
            {
                taskId: task.id,
                agentId,
                sessionId,
                type: taskDef.type,
            },
            { priority: 'normal' }
        );

        return task;
    }

    async updateTaskProgress(taskId: string, progress: Partial<TaskProgress>): Promise<void> {
        await this.prisma.agentTask.update({
            where: { id: taskId },
            data: {
                status: progress.status,
                ...(progress.output && { output: progress.output }),
                ...(progress.error && { error: progress.error }),
                ...(progress.status === TaskStatus.RUNNING && !progress.startedAt && { startedAt: new Date() }),
                ...(progress.status === TaskStatus.COMPLETED && { completedAt: new Date() }),
                updatedAt: new Date(),
            },
        });

        // Update cache
        const cachedTask = await this.cacheManager.get(`task:${taskId}`) as any;
        if (cachedTask) {
            Object.assign(cachedTask, progress);
            await this.cacheManager.set(`task:${taskId}`, cachedTask, 3600000);
        }

        // Get task to find organization ID
        const task = await this.prisma.agentTask.findUnique({
            where: { id: taskId },
            select: { organizationId: true, agentId: true },
        });

        if (task) {
            await this.apixGateway.emitAgentEvent(
                task.organizationId || 'system',
                task.agentId,
                'task.progress',
                {
                    taskId,
                    ...progress,
                },
                { priority: 'high' }
            );
        }
    }

    private analyzeInput(input: any): TaskDefinition[] {
        // Simple task analysis - can be enhanced with NLP
        const inputText = typeof input === 'string' ? input : JSON.stringify(input);

        // Check for multiple tasks indicators
        const sentences = inputText.split(/[.!?]+/).filter(s => s.trim().length > 0);

        if (sentences.length <= 1) {
            // Single task
            return [{
                name: 'Process Input',
                description: 'Process the provided input',
                type: 'general',
                priority: 1,
                input,
            }];
        }

        // Multiple tasks
        return sentences.map((sentence, index) => ({
            name: `Task ${index + 1}`,
            description: sentence.trim(),
            type: 'subtask',
            priority: index + 1,
            input: sentence.trim(),
        }));
    }
}