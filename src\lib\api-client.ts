interface RequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

interface APIResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  statusCode: number;
}

interface APIError {
  message: string;
  statusCode: number;
  errors?: any[];
  timestamp: string;
}

class APIClient {
  private baseURL: string;
  private token: string | null = null;
  private defaultTimeout = 30000; // 30 seconds
  private defaultRetries = 3;
  private defaultRetryDelay = 1000; // 1 second

  constructor(baseURL: string = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001') {
    this.baseURL = baseURL.replace(/\/$/, ''); // Remove trailing slash
    this.token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('authToken', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
    }
  }

  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = this.defaultRetryDelay,
      ...options
    } = config;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Client-Version': '1.0.0',
      'X-Request-ID': this.generateRequestId(),
      ...options.headers as Record<string, string>,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const requestConfig: RequestInit = {
      ...options,
      headers,
    };

    return this.executeWithRetry<T>(url, requestConfig, retries, retryDelay, timeout);
  }

  private generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private async executeWithRetry<T>(
    url: string,
    config: RequestInit,
    retries: number,
    retryDelay: number,
    timeout: number
  ): Promise<T> {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          ...config,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);
        return await this.handleResponse<T>(response);
      } catch (error) {
        const isLastAttempt = attempt === retries;

        if (isLastAttempt) {
          throw this.createAPIError(error);
        }

        // Only retry on network errors or 5xx server errors
        if (this.shouldRetry(error)) {
          await this.delay(retryDelay * Math.pow(2, attempt)); // Exponential backoff
          continue;
        }

        throw this.createAPIError(error);
      }
    }

    throw new Error('Max retries exceeded');
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    const isJSON = contentType?.includes('application/json');

    if (!response.ok) {
      let errorData: any = {};

      if (isJSON) {
        try {
          errorData = await response.json();
        } catch {
          // If JSON parsing fails, use default error
        }
      }

      // Handle authentication errors
      if (response.status === 401) {
        this.clearToken();
        if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth')) {
          window.location.href = '/auth/login';
        }
      }

      const apiError: APIError = {
        message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        statusCode: response.status,
        errors: errorData.errors,
        timestamp: new Date().toISOString(),
      };

      throw apiError;
    }

    if (isJSON) {
      const data = await response.json();

      // Handle different response formats
      if (data && typeof data === 'object' && 'data' in data) {
        return data.data; // Unwrap data property
      }

      return data;
    }

    // Handle non-JSON responses
    return response.text() as unknown as T;
  }

  private shouldRetry(error: any): boolean {
    // Retry on network errors
    if (error.name === 'AbortError' || error.name === 'TypeError') {
      return true;
    }

    // Retry on 5xx server errors
    if (error.statusCode >= 500) {
      return true;
    }

    // Retry on specific 4xx errors
    if (error.statusCode === 408 || error.statusCode === 429) {
      return true;
    }

    return false;
  }

  private createAPIError(error: any): APIError {
    if (error.statusCode) {
      return error; // Already an API error
    }

    let message = 'Network error occurred';
    let statusCode = 0;

    if (error.name === 'AbortError') {
      message = 'Request timeout';
      statusCode = 408;
    } else if (error instanceof TypeError) {
      message = 'Network connection failed';
      statusCode = 0;
    } else if (error.message) {
      message = error.message;
    }

    return {
      message,
      statusCode,
      timestamp: new Date().toISOString(),
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // HTTP verb helpers for better API
  protected async get<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  protected async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  protected async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  protected async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  protected async delete<T>(endpoint: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // Authentication endpoints
  async login(credentials: {
    email: string;
    password: string;
    organizationSlug?: string;
    mfaCode?: string;
    rememberMe?: boolean;
  }) {
    return this.post<{
      user: any;
      expiresAt: number;
      session?: any;
    }>('/api/auth/login', credentials);
  }

  async register(userData: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    company: string;
    subscribeNewsletter?: boolean;
  }) {
    return this.post<{
      user: any;
      organization: any;
      expiresAt: number;
      session?: any;
    }>('/api/auth/register', userData);
  }

  async getProfile() {
    return this.get<{
      user: any;
      organization: any;
    }>('/auth/profile');
  }

  async logout() {
    const result = await this.post('/api/auth/logout', {});
    this.clearToken(); // Clear any local token storage
    return result;
  }

  async refreshToken() {
    return this.post<{
      user: any;
      expiresAt: number;
      session?: any;
    }>('/api/auth/refresh', {});
  }

  // Workflow endpoints
  async getWorkflows(page = 1, limit = 10) {
    return this.request<{
      workflows: any[];
      pagination: any;
    }>(`/workflows?page=${page}&limit=${limit}`);
  }

  async getWorkflow(id: string) {
    return this.request<any>(`/workflows/${id}`);
  }

  async createWorkflow(workflowData: any) {
    return this.request<any>('/workflows', {
      method: 'POST',
      body: JSON.stringify(workflowData),
    });
  }

  async updateWorkflow(id: string, workflowData: any) {
    return this.request<any>(`/workflows/${id}`, {
      method: 'PUT',
      body: JSON.stringify(workflowData),
    });
  }

  async deleteWorkflow(id: string) {
    return this.request(`/workflows/${id}`, {
      method: 'DELETE',
    });
  }

  async executeWorkflow(id: string, input: any) {
    return this.request<{
      executionId: string;
      status: string;
      startedAt: string;
    }>(`/workflows/${id}/execute`, {
      method: 'POST',
      body: JSON.stringify({
        input,
        options: {
          async: true,
          priority: 'normal',
        }
      }),
    });
  }

  // Get workflow execution status with real-time updates
  async getWorkflowExecutionStatus(workflowId: string, executionId: string) {
    return this.request<{
      id: string;
      status: string;
      startedAt: string;
      completedAt?: string;
      duration?: number;
      error?: string;
      steps: any[];
      isActive: boolean;
      currentNode?: string;
      completedNodes: string[];
      failedNodes: string[];
      variables: Record<string, any>;
    }>(`/workflows/${workflowId}/executions/${executionId}`);
  }

  // Cancel workflow execution
  async cancelWorkflowExecution(workflowId: string, executionId: string) {
    return this.request(`/workflows/${workflowId}/executions/${executionId}/cancel`, {
      method: 'POST',
    });
  }

  // Pause workflow execution
  async pauseWorkflowExecution(workflowId: string, executionId: string) {
    return this.request(`/workflows/${workflowId}/executions/${executionId}/pause`, {
      method: 'POST',
    });
  }

  // Resume workflow execution
  async resumeWorkflowExecution(workflowId: string, executionId: string) {
    return this.request(`/workflows/${workflowId}/executions/${executionId}/resume`, {
      method: 'POST',
    });
  }

  // Get workflow templates
  async getWorkflowTemplates() {
    return this.request<{
      templates: Array<{
        id: string;
        name: string;
        description: string;
        category: string;
        definition: any;
      }>;
    }>('/workflows/templates');
  }

  // Create workflow from template
  async createWorkflowFromTemplate(templateId: string, name: string, variables?: Record<string, any>) {
    return this.request<any>(`/workflows/templates/${templateId}/use`, {
      method: 'POST',
      body: JSON.stringify({ name, variables }),
    });
  }

  // Get available node types for workflow builder
  async getAvailableNodeTypes() {
    return this.request<{
      nodeTypes: Array<{
        type: string;
        name: string;
        description: string;
        category: string;
        configSchema: any;
      }>;
      agents: Array<{
        id: string;
        name: string;
        type: string;
      }>;
      tools: Array<{
        id: string;
        name: string;
        type: string;
      }>;
    }>('/workflows/node-types');
  }

  // Duplicate workflow
  async duplicateWorkflow(id: string, name?: string) {
    return this.request<any>(`/workflows/${id}/duplicate`, {
      method: 'POST',
      body: JSON.stringify({ name }),
    });
  }

  // Import workflow
  async importWorkflow(data: string, format: 'json' | 'yaml' = 'json') {
    return this.request<any>('/workflows/import', {
      method: 'POST',
      body: JSON.stringify({ data, format }),
    });
  }

  // Export workflow
  async exportWorkflow(id: string, format: 'json' | 'yaml' = 'json', includeMetadata = false) {
    return this.request<{
      format: string;
      data: string;
      filename: string;
    }>(`/workflows/${id}/export`, {
      method: 'POST',
      body: JSON.stringify({ format, includeMetadata }),
    });
  }

  // Enhanced agent endpoints with execution capabilities
  async executeAgent(id: string, input: {
    input: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
    variables?: Record<string, any>;
  }) {
    return this.request<{
      response: string;
      usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
      };
      executionTime: number;
    }>(`/agents/${id}/execute`, {
      method: 'POST',
      body: JSON.stringify(input),
    });
  }

  // Enhanced tool endpoints with execution capabilities
  async executeTool(id: string, parameters: Record<string, any>) {
    return this.request<any>(`/tools/${id}/execute`, {
      method: 'POST',
      body: JSON.stringify({ parameters }),
    });
  }

  // Get tool schema for configuration
  async getToolSchema(id: string) {
    return this.request<{
      inputSchema: any;
      outputSchema: any;
      parameters: Record<string, any>;
    }>(`/tools/${id}/schema`);
  }

  // Human input endpoints for workflow execution
  async submitHumanInput(executionId: string, nodeId: string, response: any) {
    return this.request(`/workflows/executions/${executionId}/human-input/${nodeId}`, {
      method: 'POST',
      body: JSON.stringify({ response }),
    });
  }

  async getHumanInputRequest(executionId: string, nodeId: string) {
    return this.request<{
      id: string;
      prompt: string;
      inputType: string;
      status: string;
      expiresAt: string;
      options?: any;
    }>(`/workflows/executions/${executionId}/human-input/${nodeId}`);
  }

  // Provider management endpoints
  async testProviderConnection(id: string) {
    return this.request<{
      success: boolean;
      latency?: number;
      error?: string;
      capabilities?: string[];
    }>(`/providers/${id}/test`, {
      method: 'POST',
    });
  }

  async getProviderHealth(id: string) {
    return this.request<{
      status: 'healthy' | 'degraded' | 'unhealthy';
      latency: number;
      uptime: number;
      errorRate: number;
      lastCheck: string;
    }>(`/providers/${id}/health`);
  }

  // Enhanced analytics endpoints
  async getWorkflowAnalytics(workflowId?: string, timeRange = '7d') {
    const endpoint = workflowId
      ? `/analytics/workflows/${workflowId}?timeRange=${timeRange}`
      : `/analytics/workflows?timeRange=${timeRange}`;

    return this.request<{
      totalExecutions: number;
      successfulExecutions: number;
      failedExecutions: number;
      averageExecutionTime: number;
      executionsByStatus: Record<string, number>;
      executionsByDay: Array<{
        date: string;
        executions: number;
        successful: number;
        failed: number;
      }>;
      topFailureReasons: Array<{
        reason: string;
        count: number;
      }>;
      nodePerformance: Array<{
        nodeId: string;
        nodeType: string;
        averageExecutionTime: number;
        successRate: number;
      }>;
    }>(endpoint);
  }


  async getToolAnalytics(toolId?: string, timeRange = '7d') {
    const endpoint = toolId
      ? `/analytics/tools/${toolId}?timeRange=${timeRange}`
      : `/analytics/tools?timeRange=${timeRange}`;

    return this.request<{
      totalExecutions: number;
      averageExecutionTime: number;
      successRate: number;
      executionsByDay: Array<{
        date: string;
        executions: number;
        averageTime: number;
      }>;
      errorDistribution: Record<string, number>;
      performanceMetrics: {
        p50: number;
        p95: number;
        p99: number;
      };
    }>(endpoint);
  }

  // Real-time session management
  async createSession(sessionData: {
    type: 'workflow' | 'agent' | 'tool';
    entityId: string;
    metadata?: Record<string, any>;
  }) {
    return this.request<{
      id: string;
      type: string;
      entityId: string;
      status: string;
      createdAt: string;
      metadata: Record<string, any>;
    }>('/sessions', {
      method: 'POST',
      body: JSON.stringify(sessionData),
    });
  }

  async getSessionStatus(sessionId: string) {
    return this.request<{
      id: string;
      status: string;
      startedAt: string;
      lastActivity: string;
      metadata: Record<string, any>;
      events: Array<{
        type: string;
        timestamp: string;
        data: any;
      }>;
    }>(`/sessions/${sessionId}`);
  }

  // Organization and user management
  async getOrganizationSettings() {
    return this.request<{
      id: string;
      name: string;
      slug: string;
      settings: {
        features: string[];
        limits: Record<string, number>;
        branding: Record<string, any>;
      };
      subscription: {
        plan: string;
        status: string;
        expiresAt?: string;
      };
    }>('/organization');
  }

  async updateOrganizationSettings(settings: any) {
    return this.request<any>('/organization', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  async getOrganizationUsage(timeRange = '30d') {
    return this.request<{
      workflowExecutions: number;
      agentInteractions: number;
      toolExecutions: number;
      storageUsed: number;
      apiCalls: number;
      limits: Record<string, number>;
      usageByDay: Array<{
        date: string;
        workflowExecutions: number;
        agentInteractions: number;
        toolExecutions: number;
      }>;
    }>(`/organization/usage?timeRange=${timeRange}`);
  }

  // Health check endpoint
  async getSystemHealth() {
    return this.request<{
      status: 'healthy' | 'degraded' | 'unhealthy';
      version: string;
      uptime: number;
      services: Record<string, {
        status: string;
        latency?: number;
        error?: string;
      }>;
      metrics: {
        activeConnections: number;
        activeExecutions: number;
        queueSize: number;
        memoryUsage: number;
        cpuUsage: number;
      };
    }>('/health');
  }

  // Agent endpoints
  async getAgents(page = 1, limit = 10) {
    return this.request<{
      agents: any[];
      pagination: any;
    }>(`/agents?page=${page}&limit=${limit}`);
  }

  async getAgent(id: string) {
    return this.request<any>(`/agents/${id}`);
  }

  async createAgent(agentData: any) {
    return this.request<any>('/agents', {
      method: 'POST',
      body: JSON.stringify(agentData),
    });
  }

  async updateAgent(id: string, agentData: any) {
    return this.request<any>(`/agents/${id}`, {
      method: 'PUT',
      body: JSON.stringify(agentData),
    });
  }

  async deleteAgent(id: string) {
    return this.request(`/agents/${id}`, {
      method: 'DELETE',
    });
  }

  // Tool endpoints
  async getTools(page = 1, limit = 10) {
    return this.request<{
      tools: any[];
      pagination: any;
    }>(`/tools?page=${page}&limit=${limit}`);
  }

  async getTool(id: string) {
    return this.request<any>(`/tools/${id}`);
  }

  async createTool(toolData: any) {
    return this.request<any>('/tools', {
      method: 'POST',
      body: JSON.stringify(toolData),
    });
  }

  async updateTool(id: string, toolData: any) {
    return this.request<any>(`/tools/${id}`, {
      method: 'PUT',
      body: JSON.stringify(toolData),
    });
  }

  async deleteTool(id: string) {
    return this.request(`/tools/${id}`, {
      method: 'DELETE',
    });
  }

  // Analytics endpoints
  async getAnalytics(timeRange = '7d') {
    return this.request<any>(`/analytics?timeRange=${timeRange}`);
  }


  async getAgentAnalytics(agentId: string, timeRange = '7d') {
    return this.request<any>(`/analytics/agents/${agentId}?timeRange=${timeRange}`);
  }

  // Session endpoints
  async getSessions(page = 1, limit = 10) {
    return this.request<{
      sessions: any[];
      pagination: any;
    }>(`/sessions?page=${page}&limit=${limit}`);
  }

  async getSession(id: string) {
    return this.request<any>(`/sessions/${id}`);
  }

  async updateSession(id: string, sessionData: any) {
    return this.request<any>(`/sessions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(sessionData),
    });
  }

  // Provider endpoints
  async getProviders() {
    return this.request<any[]>('/providers');
  }

  async getProvider(id: string) {
    return this.request<any>(`/providers/${id}`);
  }

  async updateProvider(id: string, providerData: any) {
    return this.request<any>(`/providers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(providerData),
    });
  }

  async testProvider(id: string) {
    return this.request<any>(`/providers/${id}/test`, {
      method: 'POST',
    });
  }

  // Organization endpoints
  async getOrganization() {
    return this.request<any>('/organization');
  }

  async updateOrganization(organizationData: any) {
    return this.request<any>('/organization', {
      method: 'PUT',
      body: JSON.stringify(organizationData),
    });
  }

  async getOrganizationUsers(page = 1, limit = 10) {
    return this.request<{
      users: any[];
      pagination: any;
    }>(`/organization/users?page=${page}&limit=${limit}`);
  }

  async inviteUser(userData: { email: string; role: string }) {
    return this.request<any>('/organization/users/invite', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(userId: string, userData: any) {
    return this.request<any>(`/organization/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(userId: string) {
    return this.request(`/organization/users/${userId}`, {
      method: 'DELETE',
    });
  }
}

// Create singleton instance
const apiClient = new APIClient();

export default apiClient;