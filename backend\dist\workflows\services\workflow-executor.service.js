"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WorkflowExecutorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowExecutorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const apix_gateway_1 = require("../../apix/apix.gateway");
const sessions_service_1 = require("../../sessions/sessions.service");
const client_1 = require("@prisma/client");
let WorkflowExecutorService = WorkflowExecutorService_1 = class WorkflowExecutorService {
    constructor(prisma, apixGateway, sessionsService) {
        this.prisma = prisma;
        this.apixGateway = apixGateway;
        this.sessionsService = sessionsService;
        this.logger = new common_1.Logger(WorkflowExecutorService_1.name);
    }
    async executeWorkflow(executionId, workflowId, sessionId, organizationId, input = {}) {
        try {
            const workflow = await this.prisma.workflow.findUnique({
                where: { id: workflowId },
            });
            if (!workflow) {
                throw new Error(`Workflow ${workflowId} not found`);
            }
            await this.prisma.workflowExecution.update({
                where: { id: executionId },
                data: { status: client_1.ExecutionStatus.RUNNING },
            });
            await this.apixGateway.emitWorkflowEvent(organizationId, workflowId, 'workflow_execution_started', {
                executionId,
                workflowName: workflow.name,
                input,
            });
            const definition = workflow.definition;
            const executionContext = {
                executionId,
                sessionId,
                organizationId,
                workflowId,
                variables: { ...input },
                nodeResults: new Map(),
            };
            const startNodes = definition.nodes.filter(node => node.inputs.length === 0 ||
                !definition.edges.some(edge => edge.target === node.id));
            if (startNodes.length === 0) {
                throw new Error('No start nodes found in workflow');
            }
            const results = await Promise.all(startNodes.map(node => this.executeNode(node, definition, executionContext)));
            const completedAt = new Date();
            const duration = completedAt.getTime() - (await this.prisma.workflowExecution.findUnique({
                where: { id: executionId },
                select: { startedAt: true },
            })).startedAt.getTime();
            await this.prisma.workflowExecution.update({
                where: { id: executionId },
                data: {
                    status: client_1.ExecutionStatus.COMPLETED,
                    output: { results },
                    completedAt,
                    duration,
                },
            });
            await this.apixGateway.emitWorkflowEvent(organizationId, workflowId, 'workflow_execution_completed', {
                executionId,
                workflowName: workflow.name,
                duration,
                results,
            });
            return results;
        }
        catch (error) {
            const completedAt = new Date();
            const duration = completedAt.getTime() - (await this.prisma.workflowExecution.findUnique({
                where: { id: executionId },
                select: { startedAt: true },
            })).startedAt.getTime();
            await this.prisma.workflowExecution.update({
                where: { id: executionId },
                data: {
                    status: client_1.ExecutionStatus.FAILED,
                    error: error.message,
                    completedAt,
                    duration,
                },
            });
            await this.apixGateway.emitWorkflowEvent(organizationId, workflowId, 'workflow_execution_failed', {
                executionId,
                error: error.message,
                duration,
            });
            throw error;
        }
    }
    async executeNode(node, definition, context) {
        const stepStartTime = Date.now();
        const step = await this.prisma.workflowStep.create({
            data: {
                executionId: context.executionId,
                stepId: node.id,
                name: node.name,
                type: node.type,
                status: client_1.ExecutionStatus.RUNNING,
                input: node.config,
            },
        });
        try {
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'workflow_node_started', {
                executionId: context.executionId,
                nodeId: node.id,
                nodeName: node.name,
                nodeType: node.type,
            });
            let result;
            switch (node.type) {
                case 'agent':
                    result = await this.executeAgentNode(node, context);
                    break;
                case 'tool':
                    result = await this.executeToolNode(node, context);
                    break;
                case 'condition':
                    result = await this.executeConditionNode(node, context);
                    break;
                case 'parallel':
                    result = await this.executeParallelNode(node, definition, context);
                    break;
                case 'human_input':
                    result = await this.executeHumanInputNode(node, context);
                    break;
                case 'delay':
                    result = await this.executeDelayNode(node, context);
                    break;
                case 'hybrid':
                    result = await this.executeHybridNode(node, definition, context);
                    break;
                default:
                    throw new Error(`Unknown node type: ${node.type}`);
            }
            const completedAt = new Date();
            await this.prisma.workflowStep.update({
                where: { id: step.id },
                data: {
                    status: client_1.ExecutionStatus.COMPLETED,
                    output: result,
                    completedAt,
                    duration: completedAt.getTime() - stepStartTime,
                },
            });
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'workflow_node_completed', {
                executionId: context.executionId,
                nodeId: node.id,
                nodeName: node.name,
                nodeType: node.type,
                duration: completedAt.getTime() - stepStartTime,
            });
            context.nodeResults.set(node.id, result);
            context.variables[`node_${node.id}_result`] = result;
            const nextNodes = this.getNextNodes(node.id, definition, result);
            if (nextNodes.length > 0) {
                await Promise.all(nextNodes.map(nextNode => this.executeNode(nextNode, definition, context)));
            }
            return result;
        }
        catch (error) {
            const completedAt = new Date();
            await this.prisma.workflowStep.update({
                where: { id: step.id },
                data: {
                    status: client_1.ExecutionStatus.FAILED,
                    error: error.message,
                    completedAt,
                    duration: completedAt.getTime() - stepStartTime,
                },
            });
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'workflow_node_failed', {
                executionId: context.executionId,
                nodeId: node.id,
                nodeName: node.name,
                nodeType: node.type,
                error: error.message,
            });
            throw error;
        }
    }
    async executeAgentNode(node, context) {
        const { agentId, input, systemPrompt, maxTokens } = node.config;
        await this.apixGateway.emitAgentEvent(context.organizationId, agentId, 'agent_execution_started', {
            nodeId: node.id,
            executionId: context.executionId,
            config: node.config,
        });
        try {
            const agent = await this.prisma.agent.findUnique({
                where: { id: agentId },
            });
            if (!agent) {
                throw new Error(`Agent ${agentId} not found`);
            }
            const processedInput = this.processVariables(input, context.variables);
            await new Promise(resolve => setTimeout(resolve, 1000));
            const result = {
                response: `Agent ${agentId} executed successfully with input: ${JSON.stringify(processedInput)}`,
                data: processedInput,
                timestamp: Date.now(),
            };
            await this.apixGateway.emitAgentEvent(context.organizationId, agentId, 'agent_execution_completed', {
                nodeId: node.id,
                executionId: context.executionId,
                result,
            });
            return result;
        }
        catch (error) {
            await this.apixGateway.emitAgentEvent(context.organizationId, agentId, 'agent_execution_failed', {
                nodeId: node.id,
                executionId: context.executionId,
                error: error.message,
            });
            throw error;
        }
    }
    async executeToolNode(node, context) {
        const { toolId, parameters } = node.config;
        await this.apixGateway.emitToolEvent(context.organizationId, toolId, 'tool_execution_started', {
            nodeId: node.id,
            executionId: context.executionId,
            config: node.config,
        });
        try {
            const tool = await this.prisma.tool.findUnique({
                where: { id: toolId },
            });
            if (!tool) {
                throw new Error(`Tool ${toolId} not found`);
            }
            const processedParameters = this.processVariables(parameters, context.variables);
            await new Promise(resolve => setTimeout(resolve, 500));
            const result = {
                output: `Tool ${toolId} executed successfully with parameters: ${JSON.stringify(processedParameters)}`,
                data: processedParameters,
                timestamp: Date.now(),
            };
            await this.apixGateway.emitToolEvent(context.organizationId, toolId, 'tool_execution_completed', {
                nodeId: node.id,
                executionId: context.executionId,
                result,
            });
            return result;
        }
        catch (error) {
            await this.apixGateway.emitToolEvent(context.organizationId, toolId, 'tool_execution_failed', {
                nodeId: node.id,
                executionId: context.executionId,
                error: error.message,
            });
            throw error;
        }
    }
    async executeConditionNode(node, context) {
        const { condition, paths } = node.config;
        const processedCondition = this.processVariables(condition, context.variables);
        let result;
        try {
            result = this.evaluateCondition(processedCondition, context.variables);
        }
        catch (error) {
            throw new Error(`Condition evaluation failed: ${error.message}`);
        }
        return {
            condition: processedCondition,
            result,
            evaluatedPath: result ? 'true' : 'false',
            timestamp: Date.now(),
        };
    }
    async executeParallelNode(node, definition, context) {
        const { nodes: parallelNodeIds, aggregateResults } = node.config;
        const parallelResults = await Promise.all(parallelNodeIds.map(async (nodeId) => {
            const parallelNode = definition.nodes.find(n => n.id === nodeId);
            if (!parallelNode) {
                throw new Error(`Parallel node ${nodeId} not found`);
            }
            const nodeContext = { ...context };
            return {
                nodeId,
                result: await this.executeNode(parallelNode, definition, nodeContext)
            };
        }));
        let aggregatedResult = parallelResults;
        if (aggregateResults) {
        }
        return {
            parallelResults,
            aggregatedResult,
            timestamp: Date.now(),
        };
    }
    async executeHumanInputNode(node, context) {
        const { prompt, inputType, timeout = 300000 } = node.config;
        const processedPrompt = this.processVariables(prompt, context.variables);
        await this.apixGateway.emitUserInputRequest(context.organizationId, context.sessionId, processedPrompt, inputType || 'text', {
            nodeId: node.id,
            executionId: context.executionId,
            timeout,
        });
        await new Promise(resolve => setTimeout(resolve, 2000));
        return {
            userInput: 'Simulated user input for prompt: ' + processedPrompt,
            timestamp: Date.now(),
        };
    }
    async executeDelayNode(node, context) {
        const { delay = 1000 } = node.config;
        await new Promise(resolve => setTimeout(resolve, delay));
        return {
            delayed: delay,
            timestamp: Date.now(),
        };
    }
    async executeHybridNode(node, definition, context) {
        const { agentId, toolIds, executionPattern, coordination } = node.config;
        await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'hybrid_node_started', {
            executionId: context.executionId,
            nodeId: node.id,
            agentId,
            toolIds,
            executionPattern,
        });
        let result;
        try {
            switch (executionPattern) {
                case 'agent-first':
                    const agentResult = await this.executeAgentNode({
                        ...node,
                        type: 'agent',
                        config: { agentId, ...node.config.agentConfig },
                    }, context);
                    const toolParams = this.extractToolParameters(agentResult, toolIds);
                    const toolResults = await Promise.all(toolIds.map(async (toolId) => {
                        const toolResult = await this.executeToolNode({
                            ...node,
                            type: 'tool',
                            config: {
                                toolId,
                                parameters: toolParams[toolId] || {},
                            },
                        }, context);
                        return { toolId, result: toolResult };
                    }));
                    result = {
                        pattern: 'agent-first',
                        agentResult,
                        toolResults,
                        timestamp: Date.now(),
                    };
                    break;
                case 'tool-first':
                    const initialToolResults = await Promise.all(toolIds.map(async (toolId) => {
                        const toolResult = await this.executeToolNode({
                            ...node,
                            type: 'tool',
                            config: {
                                toolId,
                                parameters: node.config.toolConfigs?.[toolId] || {},
                            },
                        }, context);
                        return { toolId, result: toolResult };
                    }));
                    const toolResultsMap = {};
                    initialToolResults.forEach(tr => {
                        toolResultsMap[tr.toolId] = tr.result;
                        context.variables[`tool_${tr.toolId}_result`] = tr.result;
                    });
                    const finalAgentResult = await this.executeAgentNode({
                        ...node,
                        type: 'agent',
                        config: {
                            agentId,
                            input: {
                                ...node.config.agentConfig?.input,
                                toolResults: toolResultsMap,
                            },
                        },
                    }, context);
                    result = {
                        pattern: 'tool-first',
                        toolResults: initialToolResults,
                        agentResult: finalAgentResult,
                        timestamp: Date.now(),
                    };
                    break;
                case 'parallel':
                    const [parallelAgentResult, parallelToolResults] = await Promise.all([
                        this.executeAgentNode({
                            ...node,
                            type: 'agent',
                            config: { agentId, ...node.config.agentConfig },
                        }, { ...context }),
                        Promise.all(toolIds.map(async (toolId) => {
                            const toolResult = await this.executeToolNode({
                                ...node,
                                type: 'tool',
                                config: {
                                    toolId,
                                    parameters: node.config.toolConfigs?.[toolId] || {},
                                },
                            }, { ...context });
                            return { toolId, result: toolResult };
                        }))
                    ]);
                    result = {
                        pattern: 'parallel',
                        agentResult: parallelAgentResult,
                        toolResults: parallelToolResults,
                        timestamp: Date.now(),
                    };
                    break;
                case 'multi-tool-orchestration':
                    const orchestrationResult = await this.orchestrateMultipleTools(agentId, toolIds, node.config, context);
                    result = {
                        pattern: 'multi-tool-orchestration',
                        orchestrationResult,
                        timestamp: Date.now(),
                    };
                    break;
                default:
                    throw new Error(`Unknown hybrid execution pattern: ${executionPattern}`);
            }
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'hybrid_node_completed', {
                executionId: context.executionId,
                nodeId: node.id,
                result,
            });
            return result;
        }
        catch (error) {
            await this.apixGateway.emitWorkflowEvent(context.organizationId, context.workflowId, 'hybrid_node_failed', {
                executionId: context.executionId,
                nodeId: node.id,
                error: error.message,
            });
            throw error;
        }
    }
    async orchestrateMultipleTools(agentId, toolIds, config, context) {
        const results = [];
        let currentInput = config.agentConfig?.input || {};
        let agentResult = await this.executeAgentNode({
            id: `${context.executionId}_agent_initial`,
            type: 'agent',
            name: 'Orchestrator Agent Initial',
            config: { agentId, input: currentInput },
            position: { x: 0, y: 0 },
            inputs: [],
            outputs: [],
        }, context);
        results.push({ type: 'agent', result: agentResult });
        let nextToolId = this.determineNextTool(agentResult, toolIds);
        let iterationCount = 0;
        const maxIterations = config.maxIterations || 5;
        while (nextToolId && iterationCount < maxIterations) {
            const toolResult = await this.executeToolNode({
                id: `${context.executionId}_tool_${nextToolId}_${iterationCount}`,
                type: 'tool',
                name: `Tool ${nextToolId} Execution`,
                config: {
                    toolId: nextToolId,
                    parameters: config.toolConfigs?.[nextToolId] || {},
                },
                position: { x: 0, y: 0 },
                inputs: [],
                outputs: [],
            }, context);
            results.push({ type: 'tool', toolId: nextToolId, result: toolResult });
            context.variables[`tool_${nextToolId}_result`] = toolResult;
            currentInput = {
                ...currentInput,
                lastToolResult: toolResult,
                availableTools: toolIds,
                executionHistory: results,
            };
            agentResult = await this.executeAgentNode({
                id: `${context.executionId}_agent_iteration_${iterationCount}`,
                type: 'agent',
                name: `Orchestrator Agent Iteration ${iterationCount + 1}`,
                config: { agentId, input: currentInput },
                position: { x: 0, y: 0 },
                inputs: [],
                outputs: [],
            }, context);
            results.push({ type: 'agent', result: agentResult });
            nextToolId = this.determineNextTool(agentResult, toolIds);
            iterationCount++;
        }
        const finalAgentResult = await this.executeAgentNode({
            id: `${context.executionId}_agent_final`,
            type: 'agent',
            name: 'Orchestrator Agent Final',
            config: {
                agentId,
                input: {
                    ...currentInput,
                    executionHistory: results,
                    task: 'summarize_results',
                },
            },
            position: { x: 0, y: 0 },
            inputs: [],
            outputs: [],
        }, context);
        results.push({ type: 'agent_final', result: finalAgentResult });
        return {
            iterations: iterationCount,
            results,
            summary: finalAgentResult,
        };
    }
    determineNextTool(agentResult, availableToolIds) {
        if (!agentResult || !agentResult.response) {
            return null;
        }
        const response = agentResult.response.toLowerCase();
        for (const toolId of availableToolIds) {
            if (response.includes(toolId.toLowerCase())) {
                return toolId;
            }
        }
        if (response.includes('use tool') || response.includes('execute tool')) {
            return availableToolIds[0];
        }
        return null;
    }
    extractToolParameters(agentResult, toolIds) {
        const params = {};
        toolIds.forEach(toolId => {
            params[toolId] = {
                query: `Extracted from agent response: ${agentResult.response.substring(0, 50)}...`,
                timestamp: Date.now(),
            };
        });
        return params;
    }
    getNextNodes(currentNodeId, definition, nodeResult) {
        const outgoingEdges = definition.edges.filter(edge => edge.source === currentNodeId);
        const nextNodes = [];
        for (const edge of outgoingEdges) {
            if (edge.condition) {
                try {
                    const conditionMet = this.evaluateEdgeCondition(edge.condition, nodeResult);
                    if (!conditionMet)
                        continue;
                }
                catch (error) {
                    this.logger.warn(`Edge condition evaluation failed: ${error.message}`);
                    continue;
                }
            }
            const nextNode = definition.nodes.find(node => node.id === edge.target);
            if (nextNode) {
                nextNodes.push(nextNode);
            }
        }
        return nextNodes;
    }
    evaluateEdgeCondition(condition, result) {
        try {
            const conditionWithResult = condition.replace(/\$result/g, JSON.stringify(result));
            return eval(conditionWithResult);
        }
        catch (error) {
            throw new Error(`Edge condition evaluation failed: ${error.message}`);
        }
    }
    evaluateCondition(condition, variables) {
        try {
            const keys = Object.keys(variables);
            const values = Object.values(variables);
            const evalFunction = new Function(...keys, `return ${condition};`);
            return evalFunction(...values);
        }
        catch (error) {
            throw new Error(`Condition evaluation failed: ${error.message}`);
        }
    }
    processVariables(template, variables) {
        if (typeof template === 'string') {
            return template.replace(/\${(\w+)}/g, (match, varName) => {
                return variables[varName] !== undefined ? variables[varName] : match;
            });
        }
        else if (Array.isArray(template)) {
            return template.map(item => this.processVariables(item, variables));
        }
        else if (template && typeof template === 'object') {
            const result = {};
            for (const key in template) {
                result[key] = this.processVariables(template[key], variables);
            }
            return result;
        }
        return template;
    }
};
exports.WorkflowExecutorService = WorkflowExecutorService;
exports.WorkflowExecutorService = WorkflowExecutorService = WorkflowExecutorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        apix_gateway_1.ApixGateway,
        sessions_service_1.SessionsService])
], WorkflowExecutorService);
//# sourceMappingURL=workflow-executor.service.js.map