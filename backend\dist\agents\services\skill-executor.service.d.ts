import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { MockEventEmitter } from '../mocks/event-emitter.mock';
import * as z from 'zod';
export interface SkillDefinition {
    id: string;
    name: string;
    description: string;
    category: string;
    version: string;
    parameters: z.ZodSchema;
    returns: z.ZodSchema;
    implementation: string | Function;
    timeout?: number;
    retryPolicy?: {
        maxRetries: number;
        backoffStrategy: 'linear' | 'exponential';
        initialDelay: number;
    };
    permissions: string[];
    rateLimits?: {
        requestsPerMinute: number;
        requestsPerHour: number;
    };
    tags: string[];
    author: string;
    documentation?: string;
    examples?: any[];
    dependencies?: string[];
    requirements?: {
        providers?: string[];
        tools?: string[];
        capabilities?: string[];
    };
}
export interface SkillExecutionContext {
    agentId: string;
    sessionId: string;
    userId?: string;
    organizationId: string;
    metadata?: any;
}
export interface SkillExecutionResult {
    success: boolean;
    result?: any;
    error?: string;
    duration: number;
    metadata?: any;
    usage?: {
        tokensUsed?: number;
        apiCalls?: number;
        cost?: number;
    };
}
export declare class SkillExecutorService {
    private prisma;
    private cacheManager;
    private configService;
    private eventEmitter;
    private readonly logger;
    private readonly skillRegistry;
    private readonly executionStats;
    constructor(prisma: PrismaService, cacheManager: Cache, configService: ConfigService, eventEmitter: MockEventEmitter);
    registerSkill(skill: SkillDefinition): Promise<void>;
    unregisterSkill(skillId: string): Promise<void>;
    getSkill(skillId: string): Promise<SkillDefinition | null>;
    getSkillsByCategory(category: string): Promise<SkillDefinition[]>;
    getSkillsForAgent(agentId: string): Promise<SkillDefinition[]>;
    getAgentTools(agentId: string): Promise<any[]>;
    executeSkill(agentId: string, skillId: string, parameters: any, context?: SkillExecutionContext): Promise<SkillExecutionResult>;
    private performSkillExecution;
    private executeWithRetry;
    private calculateBackoffDelay;
    private initializeBuiltInSkills;
    private registerBuiltInSkill;
    private textAnalyzerSkill;
    private analyzeSentiment;
    private extractEntities;
    private extractKeywords;
    private webScraperSkill;
    private calculatorSkill;
    private fileProcessorSkill;
    private validateSkillDefinition;
    private validateSkillAccess;
    private validateParameters;
    private validateResult;
    private checkRateLimits;
    private trackSkillExecution;
    private zodSchemaToOpenAPISchema;
    private executeSandboxedCode;
    getAvailableSkills(category?: string): Promise<SkillDefinition[]>;
    getSkillCategories(): Promise<string[]>;
    getSkillStats(agentId: string, skillId: string): Promise<any>;
}
