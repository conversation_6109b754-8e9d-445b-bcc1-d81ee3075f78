import { PrismaService } from '../prisma/prisma.service';
import { Cache } from 'cache-manager';
export interface SessionData {
    loginMethod: string;
    userAgent?: string;
    ipAddress?: string;
    mfaVerified?: boolean;
    rememberMe?: boolean;
    deviceFingerprint?: string;
    location?: {
        country?: string;
        city?: string;
        timezone?: string;
    };
}
export interface SessionContext {
    currentWorkflow?: string;
    currentAgent?: string;
    activeTools?: string[];
    breadcrumbs?: Array<{
        path: string;
        timestamp: number;
    }>;
    preferences?: any;
}
export interface SessionMemory {
    conversationHistory?: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string;
        timestamp: number;
    }>;
    workflowState?: any;
    agentMemory?: any;
    userInputs?: any;
    temporaryData?: any;
}
export declare class SessionsService {
    private prisma;
    private cacheManager;
    constructor(prisma: PrismaService, cacheManager: Cache);
    createSession(userId: string, organizationId: string, sessionData: SessionData, expirationHours?: number): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    getSession(sessionId: string): Promise<unknown>;
    getUserActiveSession(userId: string): Promise<unknown>;
    updateSessionContext(sessionId: string, context: Partial<SessionContext>): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    updateSessionMemory(sessionId: string, memory: Partial<SessionMemory>): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    addConversationMessage(sessionId: string, role: 'user' | 'assistant' | 'system', content: string, metadata?: any): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    setWorkflowState(sessionId: string, workflowId: string, state: any): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    getWorkflowState(sessionId: string, workflowId: string): Promise<any>;
    setAgentMemory(sessionId: string, agentId: string, memory: any): Promise<{
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    }>;
    getAgentMemory(sessionId: string, agentId: string): Promise<any>;
    invalidateSession(sessionId: string): Promise<void>;
    invalidateUserSessions(userId: string): Promise<void>;
    getUserSessions(userId: string, includeInactive?: boolean): Promise<{
        id: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    getOrganizationSessions(organizationId: string, limit?: number): Promise<({
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        userId: string;
        sessionData: import("@prisma/client/runtime/library").JsonValue;
        context: import("@prisma/client/runtime/library").JsonValue;
        memory: import("@prisma/client/runtime/library").JsonValue;
        isActive: boolean;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        deviceFingerprint: string | null;
        location: import("@prisma/client/runtime/library").JsonValue | null;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
    })[]>;
    cleanupExpiredSessions(): Promise<number>;
    getSessionAnalytics(organizationId: string, days?: number): Promise<{
        totalSessions: number;
        activeSessions: number;
        avgSessionDuration: number;
        dailyStats: Record<string, {
            count: number;
            active: number;
        }>;
    }>;
    private pruneMemory;
}
