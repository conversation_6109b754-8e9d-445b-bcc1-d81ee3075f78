import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { MockEventEmitter } from '../mocks/event-emitter.mock';

export interface ProviderConfig {
    id: string;
    name: string;
    type: 'openai' | 'anthropic' | 'google' | 'azure' | 'local' | 'custom';

    // Connection details
    endpoint?: string;
    apiKey: string;
    organization?: string;
    project?: string;

    // Capabilities
    models: string[];
    features: {
        chat: boolean;
        completion: boolean;
        embedding: boolean;
        image: boolean;
        audio: boolean;
        functions: boolean;
        streaming: boolean;
    };

    // Limits and pricing
    limits: {
        requestsPerMinute: number;
        requestsPerHour: number;
        tokensPerMinute: number;
        contextWindow: number;
    };

    pricing: {
        inputTokenPrice: number;  // per 1K tokens
        outputTokenPrice: number; // per 1K tokens
        imagePrice?: number;      // per image
        audioPrice?: number;      // per minute
    };

    // Quality and performance
    quality: {
        accuracy: number;     // 0-1
        speed: number;        // 0-1
        reliability: number;  // 0-1
    };

    // Configuration
    isActive: boolean;
    priority: number; // Higher = preferred
    healthCheckUrl?: string;

    // Metadata
    tags: string[];
    description?: string;
    version: string;
}

export interface ProviderMetrics {
    providerId: string;
    timestamp: Date;

    // Performance metrics
    responseTime: number;
    successRate: number;
    errorRate: number;
    throughput: number;

    // Usage metrics
    requestCount: number;
    tokenCount: number;
    cost: number;

    // Quality metrics
    averageQuality?: number;
    userSatisfaction?: number;
}

export interface RoutingRequest {
    task: any;
    requirements: {
        model?: string;
        features?: string[];
        maxCost?: number;
        maxLatency?: number;
        minQuality?: number;
        preferredProviders?: string[];
        excludedProviders?: string[];
    };
    context?: {
        agentId?: string;
        userId?: string;
        organizationId?: string;
        priority?: number;
    };
}

export interface ProviderClient {
    id: string;
    config: ProviderConfig;

    // Core methods
    chat(params: any): Promise<any>;
    completion?(params: any): Promise<any>;
    embedding?(params: any): Promise<any>;

    // Health check
    healthCheck(): Promise<boolean>;

    // Metrics
    getMetrics(): Promise<any>;
}

@Injectable()
export class ProviderRouterService {
    private readonly logger = new Logger(ProviderRouterService.name);
    private readonly providers = new Map<string, ProviderClient>();
    private readonly routingCache = new Map<string, string>();

    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configService: ConfigService,
        private eventEmitter: MockEventEmitter,
    ) {
        this.initializeProviders();
    }

    // ============================================================================
    // PROVIDER MANAGEMENT
    // ============================================================================

    async registerProvider(config: ProviderConfig): Promise<void> {
        // Validate provider configuration
        this.validateProviderConfig(config);

        // Create provider client
        const client = await this.createProviderClient(config);

        // Test connection
        const isHealthy = await client.healthCheck();
        if (!isHealthy) {
            throw new BadRequestException(`Provider ${config.id} health check failed`);
        }

        // Store provider
        this.providers.set(config.id, client);

        // Cache configuration
        await this.cacheManager.set(`provider:${config.id}`, config, 3600000); // 1 hour

        this.logger.log(`Registered provider: ${config.id} (${config.name})`);

        this.eventEmitter.emit('provider.registered', {
            providerId: config.id,
            name: config.name,
            type: config.type,
        });
    }

    async unregisterProvider(providerId: string): Promise<void> {
        this.providers.delete(providerId);
        await this.cacheManager.del(`provider:${providerId}`);

        this.logger.log(`Unregistered provider: ${providerId}`);

        this.eventEmitter.emit('provider.unregistered', { providerId });
    }

    async getProvider(providerId: string, organizationId?: string): Promise<ProviderClient> {
        const provider = this.providers.get(providerId);

        if (!provider) {
            throw new NotFoundException(`Provider not found: ${providerId}`);
        }

        // Check if organization has access to this provider
        if (organizationId) {
            const hasAccess = await this.checkProviderAccess(organizationId, providerId);
            if (!hasAccess) {
                throw new BadRequestException(`Organization does not have access to provider: ${providerId}`);
            }
        }

        return provider;
    }

    async getAvailableProviders(organizationId: string): Promise<ProviderClient[]> {
        const providers: ProviderClient[] = [];

        for (const [providerId, provider] of this.providers.entries()) {
            if (provider.config.isActive) {
                const hasAccess = await this.checkProviderAccess(organizationId, providerId);
                if (hasAccess) {
                    providers.push(provider);
                }
            }
        }

        return providers.sort((a, b) => b.config.priority - a.config.priority);
    }

    // ============================================================================
    // INTELLIGENT ROUTING
    // ============================================================================

    async selectBestProvider(
        providers: ProviderClient[],
        request: RoutingRequest,
    ): Promise<ProviderClient> {
        if (providers.length === 0) {
            throw new BadRequestException('No providers available');
        }

        if (providers.length === 1) {
            return providers[0];
        }

        // Check cache for similar requests
        const cacheKey = this.generateRoutingCacheKey(request);
        const cachedProviderId = this.routingCache.get(cacheKey);

        if (cachedProviderId) {
            const cachedProvider = providers.find(p => p.id === cachedProviderId);
            if (cachedProvider) {
                return cachedProvider;
            }
        }

        // Score each provider
        const scoredProviders = await Promise.all(
            providers.map(async (provider) => ({
                provider,
                score: await this.scoreProvider(provider, request),
            }))
        );

        // Filter out providers that don't meet requirements
        const qualifiedProviders = scoredProviders.filter(sp => sp.score > 0);

        if (qualifiedProviders.length === 0) {
            throw new BadRequestException('No providers meet the requirements');
        }

        // Sort by score (descending)
        qualifiedProviders.sort((a, b) => b.score - a.score);

        const selectedProvider = qualifiedProviders[0].provider;

        // Cache the decision
        this.routingCache.set(cacheKey, selectedProvider.id);

        this.logger.debug(
            `Selected provider ${selectedProvider.id} with score ${qualifiedProviders[0].score} for request`,
        );

        this.eventEmitter.emit('provider.selected', {
            providerId: selectedProvider.id,
            score: qualifiedProviders[0].score,
            alternatives: qualifiedProviders.slice(1).map(sp => ({
                providerId: sp.provider.id,
                score: sp.score,
            })),
        });

        return selectedProvider;
    }

    private async scoreProvider(provider: ProviderClient, request: RoutingRequest): Promise<number> {
        let score = 0;
        const config = provider.config;
        const requirements = request.requirements;

        // Check hard requirements first
        if (requirements.model && !config.models.includes(requirements.model)) {
            return 0; // Provider doesn't support required model
        }

        if (requirements.features) {
            for (const feature of requirements.features) {
                if (feature === 'functions' && !config.features.functions) return 0;
                if (feature === 'streaming' && !config.features.streaming) return 0;
                if (feature === 'image' && !config.features.image) return 0;
                if (feature === 'audio' && !config.features.audio) return 0;
            }
        }

        if (requirements.excludedProviders?.includes(provider.id)) {
            return 0; // Provider is explicitly excluded
        }

        // Base score from priority
        score += config.priority * 10;

        // Preferred provider bonus
        if (requirements.preferredProviders?.includes(provider.id)) {
            score += 50;
        }

        // Quality scores
        score += config.quality.accuracy * 30;
        score += config.quality.speed * 20;
        score += config.quality.reliability * 25;

        // Cost consideration
        if (requirements.maxCost) {
            const estimatedCost = this.estimateCost(provider, request.task);
            if (estimatedCost > requirements.maxCost) {
                return 0; // Too expensive
            }

            // Lower cost is better
            const costScore = Math.max(0, (requirements.maxCost - estimatedCost) / requirements.maxCost * 20);
            score += costScore;
        }

        // Quality requirement
        if (requirements.minQuality) {
            if (config.quality.accuracy < requirements.minQuality) {
                return 0; // Quality too low
            }
        }

        // Get recent performance metrics
        const metrics = await this.getProviderMetrics(provider.id);
        if (metrics) {
            score += metrics.successRate * 15;
            score -= metrics.errorRate * 10;

            // Latency consideration
            if (requirements.maxLatency && metrics.responseTime > requirements.maxLatency) {
                return 0; // Too slow
            }

            if (metrics.responseTime < 1000) {
                score += 10; // Fast response bonus
            }
        }

        // Load balancing - prefer less loaded providers
        const currentLoad = await this.getCurrentLoad(provider.id);
        const loadPenalty = Math.min(currentLoad * 5, 20);
        score -= loadPenalty;

        return Math.max(score, 0);
    }

    private estimateCost(provider: ProviderClient, task: any): number {
        const config = provider.config;

        // Estimate token count (simplified)
        const taskText = typeof task === 'string' ? task : JSON.stringify(task);
        const estimatedTokens = Math.ceil(taskText.length / 4); // Rough estimate

        // Calculate cost
        const inputCost = (estimatedTokens / 1000) * config.pricing.inputTokenPrice;
        const outputCost = (estimatedTokens * 0.5 / 1000) * config.pricing.outputTokenPrice; // Assume 50% output

        return inputCost + outputCost;
    }

    private generateRoutingCacheKey(request: RoutingRequest): string {
        const keyData = {
            model: request.requirements.model,
            features: request.requirements.features?.sort(),
            agentId: request.context?.agentId,
        };

        return `routing:${Buffer.from(JSON.stringify(keyData)).toString('base64')}`;
    }

    // ============================================================================
    // PROVIDER IMPLEMENTATIONS
    // ============================================================================

    private async createProviderClient(config: ProviderConfig): Promise<ProviderClient> {
        switch (config.type) {
            case 'openai':
                return this.createOpenAIClient(config);
            case 'anthropic':
                return this.createAnthropicClient(config);
            case 'google':
                return this.createGoogleClient(config);
            case 'azure':
                return this.createAzureClient(config);
            case 'local':
                return this.createLocalClient(config);
            case 'custom':
                return this.createCustomClient(config);
            default:
                throw new BadRequestException(`Unsupported provider type: ${config.type}`);
        }
    }

    private createOpenAIClient(config: ProviderConfig): ProviderClient {
        return {
            id: config.id,
            config,

            async chat(params: any): Promise<any> {
                const url = config.endpoint || 'https://api.openai.com/v1/chat/completions';

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${config.apiKey}`,
                        ...(config.organization && { 'OpenAI-Organization': config.organization }),
                    },
                    body: JSON.stringify({
                        model: params.model || 'gpt-4',
                        messages: params.messages,
                        temperature: params.temperature ?? 0.7,
                        max_tokens: params.max_tokens || 2000,
                        top_p: params.top_p ?? 1,
                        tools: params.tools,
                        tool_choice: params.tool_choice,
                        stream: params.stream || false,
                    }),
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`OpenAI API error: ${response.status} ${error}`);
                }

                const result = await response.json();

                return {
                    content: result.choices[0]?.message?.content || '',
                    tool_calls: result.choices[0]?.message?.tool_calls,
                    tokens: result.usage?.total_tokens || 0,
                    cost: this.calculateOpenAICost(result.usage, params.model),
                    model: result.model,
                };
            },

            async healthCheck(): Promise<boolean> {
                try {
                    const response = await fetch('https://api.openai.com/v1/models', {
                        headers: {
                            'Authorization': `Bearer ${config.apiKey}`,
                        },
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                } catch {
                    return false;
                }
            },

            async getMetrics(): Promise<any> {
                // Implementation would fetch real metrics
                return {
                    responseTime: 800,
                    successRate: 0.98,
                    errorRate: 0.02,
                    throughput: 100,
                };
            },
        };
    }

    private createAnthropicClient(config: ProviderConfig): ProviderClient {
        return {
            id: config.id,
            config,

            async chat(params: any): Promise<any> {
                const url = config.endpoint || 'https://api.anthropic.com/v1/messages';

                // Convert OpenAI format to Anthropic format
                const messages = params.messages.filter((m: any) => m.role !== 'system');
                const systemMessage = params.messages.find((m: any) => m.role === 'system')?.content;

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-api-key': config.apiKey,
                        'anthropic-version': '2023-06-01',
                    },
                    body: JSON.stringify({
                        model: params.model || 'claude-3-sonnet-20240229',
                        max_tokens: params.max_tokens || 2000,
                        temperature: params.temperature ?? 0.7,
                        system: systemMessage,
                        messages,
                    }),
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`Anthropic API error: ${response.status} ${error}`);
                }

                const result = await response.json();

                return {
                    content: result.content[0]?.text || '',
                    tokens: result.usage?.input_tokens + result.usage?.output_tokens || 0,
                    cost: this.calculateAnthropicCost(result.usage, params.model),
                    model: result.model,
                };
            },

            async healthCheck(): Promise<boolean> {
                try {
                    // Anthropic doesn't have a models endpoint, so we'll use a minimal message
                    const response = await fetch('https://api.anthropic.com/v1/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'x-api-key': config.apiKey,
                            'anthropic-version': '2023-06-01',
                        },
                        body: JSON.stringify({
                            model: 'claude-3-haiku-20240307',
                            max_tokens: 1,
                            messages: [{ role: 'user', content: 'Hi' }],
                        }),
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                } catch {
                    return false;
                }
            },

            async getMetrics(): Promise<any> {
                return {
                    responseTime: 1200,
                    successRate: 0.97,
                    errorRate: 0.03,
                    throughput: 80,
                };
            },
        };
    }

    private createGoogleClient(config: ProviderConfig): ProviderClient {
        return {
            id: config.id,
            config,

            async chat(params: any): Promise<any> {
                // Google Gemini implementation
                throw new Error('Google provider not implemented yet');
            },

            async healthCheck(): Promise<boolean> {
                return false; // Not implemented
            },

            async getMetrics(): Promise<any> {
                return {
                    responseTime: 1000,
                    successRate: 0.95,
                    errorRate: 0.05,
                    throughput: 60,
                };
            },
        };
    }

    private createAzureClient(config: ProviderConfig): ProviderClient {
        return {
            id: config.id,
            config,

            async chat(params: any): Promise<any> {
                // Azure OpenAI implementation
                const url = `${config.endpoint}/openai/deployments/${params.model}/chat/completions?api-version=2023-12-01-preview`;

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'api-key': config.apiKey,
                    },
                    body: JSON.stringify({
                        messages: params.messages,
                        temperature: params.temperature ?? 0.7,
                        max_tokens: params.max_tokens || 2000,
                        top_p: params.top_p ?? 1,
                        tools: params.tools,
                        tool_choice: params.tool_choice,
                    }),
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`Azure OpenAI API error: ${response.status} ${error}`);
                }

                const result = await response.json();

                return {
                    content: result.choices[0]?.message?.content || '',
                    tool_calls: result.choices[0]?.message?.tool_calls,
                    tokens: result.usage?.total_tokens || 0,
                    cost: this.calculateAzureCost(result.usage, params.model),
                    model: result.model,
                };
            },

            async healthCheck(): Promise<boolean> {
                try {
                    const response = await fetch(`${config.endpoint}/openai/deployments?api-version=2023-12-01-preview`, {
                        headers: {
                            'api-key': config.apiKey,
                        },
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                } catch {
                    return false;
                }
            },

            async getMetrics(): Promise<any> {
                return {
                    responseTime: 900,
                    successRate: 0.96,
                    errorRate: 0.04,
                    throughput: 90,
                };
            },
        };
    }

    private createLocalClient(config: ProviderConfig): ProviderClient {
        return {
            id: config.id,
            config,

            async chat(params: any): Promise<any> {
                // Local model implementation (e.g., Ollama)
                const url = `${config.endpoint}/api/chat`;

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: params.model,
                        messages: params.messages,
                        stream: false,
                        options: {
                            temperature: params.temperature ?? 0.7,
                            num_predict: params.max_tokens || 2000,
                            top_p: params.top_p ?? 1,
                        },
                    }),
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`Local API error: ${response.status} ${error}`);
                }

                const result = await response.json();

                return {
                    content: result.message?.content || '',
                    tokens: 0, // Local models don't usually report token usage
                    cost: 0,   // Local models are free
                    model: result.model,
                };
            },

            async healthCheck(): Promise<boolean> {
                try {
                    const response = await fetch(`${config.endpoint}/api/tags`, {
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                } catch {
                    return false;
                }
            },

            async getMetrics(): Promise<any> {
                return {
                    responseTime: 2000,
                    successRate: 0.90,
                    errorRate: 0.10,
                    throughput: 30,
                };
            },
        };
    }

    private createCustomClient(config: ProviderConfig): ProviderClient {
        return {
            id: config.id,
            config,

            async chat(params: any): Promise<any> {
                // Custom provider implementation
                throw new Error('Custom provider implementation not available');
            },

            async healthCheck(): Promise<boolean> {
                return false;
            },

            async getMetrics(): Promise<any> {
                return {
                    responseTime: 1500,
                    successRate: 0.85,
                    errorRate: 0.15,
                    throughput: 40,
                };
            },
        };
    }

    // ============================================================================
    // UTILITY METHODS
    // ============================================================================

    private calculateOpenAICost(usage: any, model: string): number {
        if (!usage) return 0;

        // Simplified cost calculation
        const inputTokens = usage.prompt_tokens || 0;
        const outputTokens = usage.completion_tokens || 0;

        // GPT-4 pricing (example)
        const inputPrice = 0.03 / 1000;  // $0.03 per 1K tokens
        const outputPrice = 0.06 / 1000; // $0.06 per 1K tokens

        return (inputTokens * inputPrice) + (outputTokens * outputPrice);
    }

    private calculateAnthropicCost(usage: any, model: string): number {
        if (!usage) return 0;

        const inputTokens = usage.input_tokens || 0;
        const outputTokens = usage.output_tokens || 0;

        // Claude pricing (example)
        const inputPrice = 0.015 / 1000;
        const outputPrice = 0.075 / 1000;

        return (inputTokens * inputPrice) + (outputTokens * outputPrice);
    }

    private calculateAzureCost(usage: any, model: string): number {
        // Azure pricing is similar to OpenAI but may vary
        return this.calculateOpenAICost(usage, model);
    }

    private validateProviderConfig(config: ProviderConfig): void {
        if (!config.id || !config.name || !config.type) {
            throw new BadRequestException('Provider must have id, name, and type');
        }

        if (!config.apiKey) {
            throw new BadRequestException('Provider must have an API key');
        }

        if (!config.models || config.models.length === 0) {
            throw new BadRequestException('Provider must support at least one model');
        }
    }

    private async checkProviderAccess(organizationId: string, providerId: string): Promise<boolean> {
        // In a real implementation, this would check organization permissions
        // For now, return true (all providers available to all organizations)
        return true;
    }

    private async getProviderMetrics(providerId: string): Promise<ProviderMetrics | null> {
        const metricsKey = `provider_metrics:${providerId}`;
        return this.cacheManager.get(metricsKey) as Promise<ProviderMetrics | null>;
    }

    private async getCurrentLoad(providerId: string): Promise<number> {
        const loadKey = `provider_load:${providerId}`;
        const load = await this.cacheManager.get(loadKey) as number;
        return load || 0;
    }

    private async initializeProviders(): Promise<void> {
        // Initialize default providers from configuration
        const providers = this.configService.get<any>('PROVIDERS', {});

        for (const [key, config] of Object.entries(providers)) {
            try {
                await this.registerProvider(config as ProviderConfig);
            } catch (error) {
                this.logger.warn(`Failed to initialize provider ${key}: ${error.message}`);
            }
        }

        this.logger.log('Provider router initialized');
    }

    // ============================================================================
    // PUBLIC QUERY METHODS
    // ============================================================================

    async getProviderConfigs(): Promise<ProviderConfig[]> {
        return Array.from(this.providers.values()).map(provider => provider.config);
    }

    async getProviderStatus(providerId: string): Promise<any> {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new NotFoundException(`Provider not found: ${providerId}`);
        }

        const [isHealthy, metrics, load] = await Promise.all([
            provider.healthCheck(),
            this.getProviderMetrics(providerId),
            this.getCurrentLoad(providerId),
        ]);

        return {
            id: providerId,
            name: provider.config.name,
            type: provider.config.type,
            isHealthy,
            isActive: provider.config.isActive,
            currentLoad: load,
            metrics,
            lastChecked: new Date(),
        };
    }

    async getRoutingStats(): Promise<any> {
        const providers = Array.from(this.providers.keys());
        const stats: any = {};

        for (const providerId of providers) {
            const metrics = await this.getProviderMetrics(providerId);
            const load = await this.getCurrentLoad(providerId);

            stats[providerId] = {
                load,
                metrics,
            };
        }

        return {
            totalProviders: providers.length,
            activeProviders: providers.filter(id => this.providers.get(id)?.config.isActive).length,
            providerStats: stats,
            cacheSize: this.routingCache.size,
        };
    }
}