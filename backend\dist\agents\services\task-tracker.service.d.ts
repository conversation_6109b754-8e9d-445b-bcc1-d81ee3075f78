import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { MockEventEmitter } from '../mocks/event-emitter.mock';
import { TaskStatus } from '@prisma/client';
export interface TaskDefinition {
    name: string;
    description?: string;
    type: string;
    priority: number;
    input: any;
    context?: any;
    metadata?: any;
    dependencies?: string[];
    maxRetries?: number;
    timeout?: number;
}
export interface TaskProgress {
    taskId: string;
    status: TaskStatus;
    progress: number;
    message?: string;
    startedAt?: Date;
    estimatedCompletion?: Date;
    output?: any;
    error?: string;
}
export declare class TaskTrackerService {
    private prisma;
    private cacheManager;
    private eventEmitter;
    private readonly logger;
    constructor(prisma: PrismaService, cacheManager: Cache, eventEmitter: MockEventEmitter);
    analyzeAndCreateTasks(agentId: string, sessionId: string, input: any): Promise<any[]>;
    createTask(agentId: string, sessionId: string, taskDef: TaskDefinition): Promise<any>;
    updateTaskProgress(taskId: string, progress: Partial<TaskProgress>): Promise<void>;
    private analyzeInput;
}
