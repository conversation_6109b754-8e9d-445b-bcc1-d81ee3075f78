import { NextRequest, NextResponse } from "next/server";

/**
 * 🔌 WebSocket Token Endpoint
 * 
 * Provides a temporary token for WebSocket authentication
 * using the existing httpOnly access token.
 */

export async function GET(request: NextRequest) {
    try {
        const accessToken = request.cookies.get('access_token')?.value;

        if (!accessToken) {
            return NextResponse.json(
                { message: "Not authenticated" },
                { status: 401 }
            );
        }

        // For WebSocket connections, we can provide the access token
        // since WebSockets can't use httpOnly cookies
        return NextResponse.json({
            token: accessToken,
            expiresIn: 15 * 60, // 15 minutes
        });

    } catch (error) {
        console.error("WebSocket token error:", error);
        return NextResponse.json(
            { message: "Failed to generate WebSocket token" },
            { status: 500 }
        );
    }
}