"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ProviderRouterService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderRouterService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const event_emitter_mock_1 = require("../mocks/event-emitter.mock");
let ProviderRouterService = ProviderRouterService_1 = class ProviderRouterService {
    constructor(prisma, cacheManager, configService, eventEmitter) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.configService = configService;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(ProviderRouterService_1.name);
        this.providers = new Map();
        this.routingCache = new Map();
        this.initializeProviders();
    }
    async registerProvider(config) {
        this.validateProviderConfig(config);
        const client = await this.createProviderClient(config);
        const isHealthy = await client.healthCheck();
        if (!isHealthy) {
            throw new common_1.BadRequestException(`Provider ${config.id} health check failed`);
        }
        this.providers.set(config.id, client);
        await this.cacheManager.set(`provider:${config.id}`, config, 3600000);
        this.logger.log(`Registered provider: ${config.id} (${config.name})`);
        this.eventEmitter.emit('provider.registered', {
            providerId: config.id,
            name: config.name,
            type: config.type,
        });
    }
    async unregisterProvider(providerId) {
        this.providers.delete(providerId);
        await this.cacheManager.del(`provider:${providerId}`);
        this.logger.log(`Unregistered provider: ${providerId}`);
        this.eventEmitter.emit('provider.unregistered', { providerId });
    }
    async getProvider(providerId, organizationId) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new common_1.NotFoundException(`Provider not found: ${providerId}`);
        }
        if (organizationId) {
            const hasAccess = await this.checkProviderAccess(organizationId, providerId);
            if (!hasAccess) {
                throw new common_1.BadRequestException(`Organization does not have access to provider: ${providerId}`);
            }
        }
        return provider;
    }
    async getAvailableProviders(organizationId) {
        const providers = [];
        for (const [providerId, provider] of this.providers.entries()) {
            if (provider.config.isActive) {
                const hasAccess = await this.checkProviderAccess(organizationId, providerId);
                if (hasAccess) {
                    providers.push(provider);
                }
            }
        }
        return providers.sort((a, b) => b.config.priority - a.config.priority);
    }
    async selectBestProvider(providers, request) {
        if (providers.length === 0) {
            throw new common_1.BadRequestException('No providers available');
        }
        if (providers.length === 1) {
            return providers[0];
        }
        const cacheKey = this.generateRoutingCacheKey(request);
        const cachedProviderId = this.routingCache.get(cacheKey);
        if (cachedProviderId) {
            const cachedProvider = providers.find(p => p.id === cachedProviderId);
            if (cachedProvider) {
                return cachedProvider;
            }
        }
        const scoredProviders = await Promise.all(providers.map(async (provider) => ({
            provider,
            score: await this.scoreProvider(provider, request),
        })));
        const qualifiedProviders = scoredProviders.filter(sp => sp.score > 0);
        if (qualifiedProviders.length === 0) {
            throw new common_1.BadRequestException('No providers meet the requirements');
        }
        qualifiedProviders.sort((a, b) => b.score - a.score);
        const selectedProvider = qualifiedProviders[0].provider;
        this.routingCache.set(cacheKey, selectedProvider.id);
        this.logger.debug(`Selected provider ${selectedProvider.id} with score ${qualifiedProviders[0].score} for request`);
        this.eventEmitter.emit('provider.selected', {
            providerId: selectedProvider.id,
            score: qualifiedProviders[0].score,
            alternatives: qualifiedProviders.slice(1).map(sp => ({
                providerId: sp.provider.id,
                score: sp.score,
            })),
        });
        return selectedProvider;
    }
    async scoreProvider(provider, request) {
        let score = 0;
        const config = provider.config;
        const requirements = request.requirements;
        if (requirements.model && !config.models.includes(requirements.model)) {
            return 0;
        }
        if (requirements.features) {
            for (const feature of requirements.features) {
                if (feature === 'functions' && !config.features.functions)
                    return 0;
                if (feature === 'streaming' && !config.features.streaming)
                    return 0;
                if (feature === 'image' && !config.features.image)
                    return 0;
                if (feature === 'audio' && !config.features.audio)
                    return 0;
            }
        }
        if (requirements.excludedProviders?.includes(provider.id)) {
            return 0;
        }
        score += config.priority * 10;
        if (requirements.preferredProviders?.includes(provider.id)) {
            score += 50;
        }
        score += config.quality.accuracy * 30;
        score += config.quality.speed * 20;
        score += config.quality.reliability * 25;
        if (requirements.maxCost) {
            const estimatedCost = this.estimateCost(provider, request.task);
            if (estimatedCost > requirements.maxCost) {
                return 0;
            }
            const costScore = Math.max(0, (requirements.maxCost - estimatedCost) / requirements.maxCost * 20);
            score += costScore;
        }
        if (requirements.minQuality) {
            if (config.quality.accuracy < requirements.minQuality) {
                return 0;
            }
        }
        const metrics = await this.getProviderMetrics(provider.id);
        if (metrics) {
            score += metrics.successRate * 15;
            score -= metrics.errorRate * 10;
            if (requirements.maxLatency && metrics.responseTime > requirements.maxLatency) {
                return 0;
            }
            if (metrics.responseTime < 1000) {
                score += 10;
            }
        }
        const currentLoad = await this.getCurrentLoad(provider.id);
        const loadPenalty = Math.min(currentLoad * 5, 20);
        score -= loadPenalty;
        return Math.max(score, 0);
    }
    estimateCost(provider, task) {
        const config = provider.config;
        const taskText = typeof task === 'string' ? task : JSON.stringify(task);
        const estimatedTokens = Math.ceil(taskText.length / 4);
        const inputCost = (estimatedTokens / 1000) * config.pricing.inputTokenPrice;
        const outputCost = (estimatedTokens * 0.5 / 1000) * config.pricing.outputTokenPrice;
        return inputCost + outputCost;
    }
    generateRoutingCacheKey(request) {
        const keyData = {
            model: request.requirements.model,
            features: request.requirements.features?.sort(),
            agentId: request.context?.agentId,
        };
        return `routing:${Buffer.from(JSON.stringify(keyData)).toString('base64')}`;
    }
    async createProviderClient(config) {
        switch (config.type) {
            case 'openai':
                return this.createOpenAIClient(config);
            case 'anthropic':
                return this.createAnthropicClient(config);
            case 'google':
                return this.createGoogleClient(config);
            case 'azure':
                return this.createAzureClient(config);
            case 'local':
                return this.createLocalClient(config);
            case 'custom':
                return this.createCustomClient(config);
            default:
                throw new common_1.BadRequestException(`Unsupported provider type: ${config.type}`);
        }
    }
    createOpenAIClient(config) {
        return {
            id: config.id,
            config,
            async chat(params) {
                const url = config.endpoint || 'https://api.openai.com/v1/chat/completions';
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${config.apiKey}`,
                        ...(config.organization && { 'OpenAI-Organization': config.organization }),
                    },
                    body: JSON.stringify({
                        model: params.model || 'gpt-4',
                        messages: params.messages,
                        temperature: params.temperature ?? 0.7,
                        max_tokens: params.max_tokens || 2000,
                        top_p: params.top_p ?? 1,
                        tools: params.tools,
                        tool_choice: params.tool_choice,
                        stream: params.stream || false,
                    }),
                });
                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`OpenAI API error: ${response.status} ${error}`);
                }
                const result = await response.json();
                return {
                    content: result.choices[0]?.message?.content || '',
                    tool_calls: result.choices[0]?.message?.tool_calls,
                    tokens: result.usage?.total_tokens || 0,
                    cost: this.calculateOpenAICost(result.usage, params.model),
                    model: result.model,
                };
            },
            async healthCheck() {
                try {
                    const response = await fetch('https://api.openai.com/v1/models', {
                        headers: {
                            'Authorization': `Bearer ${config.apiKey}`,
                        },
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                }
                catch {
                    return false;
                }
            },
            async getMetrics() {
                return {
                    responseTime: 800,
                    successRate: 0.98,
                    errorRate: 0.02,
                    throughput: 100,
                };
            },
        };
    }
    createAnthropicClient(config) {
        return {
            id: config.id,
            config,
            async chat(params) {
                const url = config.endpoint || 'https://api.anthropic.com/v1/messages';
                const messages = params.messages.filter((m) => m.role !== 'system');
                const systemMessage = params.messages.find((m) => m.role === 'system')?.content;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-api-key': config.apiKey,
                        'anthropic-version': '2023-06-01',
                    },
                    body: JSON.stringify({
                        model: params.model || 'claude-3-sonnet-20240229',
                        max_tokens: params.max_tokens || 2000,
                        temperature: params.temperature ?? 0.7,
                        system: systemMessage,
                        messages,
                    }),
                });
                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`Anthropic API error: ${response.status} ${error}`);
                }
                const result = await response.json();
                return {
                    content: result.content[0]?.text || '',
                    tokens: result.usage?.input_tokens + result.usage?.output_tokens || 0,
                    cost: this.calculateAnthropicCost(result.usage, params.model),
                    model: result.model,
                };
            },
            async healthCheck() {
                try {
                    const response = await fetch('https://api.anthropic.com/v1/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'x-api-key': config.apiKey,
                            'anthropic-version': '2023-06-01',
                        },
                        body: JSON.stringify({
                            model: 'claude-3-haiku-20240307',
                            max_tokens: 1,
                            messages: [{ role: 'user', content: 'Hi' }],
                        }),
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                }
                catch {
                    return false;
                }
            },
            async getMetrics() {
                return {
                    responseTime: 1200,
                    successRate: 0.97,
                    errorRate: 0.03,
                    throughput: 80,
                };
            },
        };
    }
    createGoogleClient(config) {
        return {
            id: config.id,
            config,
            async chat(params) {
                throw new Error('Google provider not implemented yet');
            },
            async healthCheck() {
                return false;
            },
            async getMetrics() {
                return {
                    responseTime: 1000,
                    successRate: 0.95,
                    errorRate: 0.05,
                    throughput: 60,
                };
            },
        };
    }
    createAzureClient(config) {
        return {
            id: config.id,
            config,
            async chat(params) {
                const url = `${config.endpoint}/openai/deployments/${params.model}/chat/completions?api-version=2023-12-01-preview`;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'api-key': config.apiKey,
                    },
                    body: JSON.stringify({
                        messages: params.messages,
                        temperature: params.temperature ?? 0.7,
                        max_tokens: params.max_tokens || 2000,
                        top_p: params.top_p ?? 1,
                        tools: params.tools,
                        tool_choice: params.tool_choice,
                    }),
                });
                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`Azure OpenAI API error: ${response.status} ${error}`);
                }
                const result = await response.json();
                return {
                    content: result.choices[0]?.message?.content || '',
                    tool_calls: result.choices[0]?.message?.tool_calls,
                    tokens: result.usage?.total_tokens || 0,
                    cost: this.calculateAzureCost(result.usage, params.model),
                    model: result.model,
                };
            },
            async healthCheck() {
                try {
                    const response = await fetch(`${config.endpoint}/openai/deployments?api-version=2023-12-01-preview`, {
                        headers: {
                            'api-key': config.apiKey,
                        },
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                }
                catch {
                    return false;
                }
            },
            async getMetrics() {
                return {
                    responseTime: 900,
                    successRate: 0.96,
                    errorRate: 0.04,
                    throughput: 90,
                };
            },
        };
    }
    createLocalClient(config) {
        return {
            id: config.id,
            config,
            async chat(params) {
                const url = `${config.endpoint}/api/chat`;
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: params.model,
                        messages: params.messages,
                        stream: false,
                        options: {
                            temperature: params.temperature ?? 0.7,
                            num_predict: params.max_tokens || 2000,
                            top_p: params.top_p ?? 1,
                        },
                    }),
                });
                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(`Local API error: ${response.status} ${error}`);
                }
                const result = await response.json();
                return {
                    content: result.message?.content || '',
                    tokens: 0,
                    cost: 0,
                    model: result.model,
                };
            },
            async healthCheck() {
                try {
                    const response = await fetch(`${config.endpoint}/api/tags`, {
                        signal: AbortSignal.timeout(5000),
                    });
                    return response.ok;
                }
                catch {
                    return false;
                }
            },
            async getMetrics() {
                return {
                    responseTime: 2000,
                    successRate: 0.90,
                    errorRate: 0.10,
                    throughput: 30,
                };
            },
        };
    }
    createCustomClient(config) {
        return {
            id: config.id,
            config,
            async chat(params) {
                throw new Error('Custom provider implementation not available');
            },
            async healthCheck() {
                return false;
            },
            async getMetrics() {
                return {
                    responseTime: 1500,
                    successRate: 0.85,
                    errorRate: 0.15,
                    throughput: 40,
                };
            },
        };
    }
    calculateOpenAICost(usage, model) {
        if (!usage)
            return 0;
        const inputTokens = usage.prompt_tokens || 0;
        const outputTokens = usage.completion_tokens || 0;
        const inputPrice = 0.03 / 1000;
        const outputPrice = 0.06 / 1000;
        return (inputTokens * inputPrice) + (outputTokens * outputPrice);
    }
    calculateAnthropicCost(usage, model) {
        if (!usage)
            return 0;
        const inputTokens = usage.input_tokens || 0;
        const outputTokens = usage.output_tokens || 0;
        const inputPrice = 0.015 / 1000;
        const outputPrice = 0.075 / 1000;
        return (inputTokens * inputPrice) + (outputTokens * outputPrice);
    }
    calculateAzureCost(usage, model) {
        return this.calculateOpenAICost(usage, model);
    }
    validateProviderConfig(config) {
        if (!config.id || !config.name || !config.type) {
            throw new common_1.BadRequestException('Provider must have id, name, and type');
        }
        if (!config.apiKey) {
            throw new common_1.BadRequestException('Provider must have an API key');
        }
        if (!config.models || config.models.length === 0) {
            throw new common_1.BadRequestException('Provider must support at least one model');
        }
    }
    async checkProviderAccess(organizationId, providerId) {
        return true;
    }
    async getProviderMetrics(providerId) {
        const metricsKey = `provider_metrics:${providerId}`;
        return this.cacheManager.get(metricsKey);
    }
    async getCurrentLoad(providerId) {
        const loadKey = `provider_load:${providerId}`;
        const load = await this.cacheManager.get(loadKey);
        return load || 0;
    }
    async initializeProviders() {
        const providers = this.configService.get('PROVIDERS', {});
        for (const [key, config] of Object.entries(providers)) {
            try {
                await this.registerProvider(config);
            }
            catch (error) {
                this.logger.warn(`Failed to initialize provider ${key}: ${error.message}`);
            }
        }
        this.logger.log('Provider router initialized');
    }
    async getProviderConfigs() {
        return Array.from(this.providers.values()).map(provider => provider.config);
    }
    async getProviderStatus(providerId) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new common_1.NotFoundException(`Provider not found: ${providerId}`);
        }
        const [isHealthy, metrics, load] = await Promise.all([
            provider.healthCheck(),
            this.getProviderMetrics(providerId),
            this.getCurrentLoad(providerId),
        ]);
        return {
            id: providerId,
            name: provider.config.name,
            type: provider.config.type,
            isHealthy,
            isActive: provider.config.isActive,
            currentLoad: load,
            metrics,
            lastChecked: new Date(),
        };
    }
    async getRoutingStats() {
        const providers = Array.from(this.providers.keys());
        const stats = {};
        for (const providerId of providers) {
            const metrics = await this.getProviderMetrics(providerId);
            const load = await this.getCurrentLoad(providerId);
            stats[providerId] = {
                load,
                metrics,
            };
        }
        return {
            totalProviders: providers.length,
            activeProviders: providers.filter(id => this.providers.get(id)?.config.isActive).length,
            providerStats: stats,
            cacheSize: this.routingCache.size,
        };
    }
};
exports.ProviderRouterService = ProviderRouterService;
exports.ProviderRouterService = ProviderRouterService = ProviderRouterService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, config_1.ConfigService,
        event_emitter_mock_1.MockEventEmitter])
], ProviderRouterService);
//# sourceMappingURL=provider-router.service.js.map