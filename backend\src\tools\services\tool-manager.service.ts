import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ToolType, SkillCategory, ToolCacheStrategy, Prisma } from '@prisma/client';
import { ApixGateway } from '../../apix/apix.gateway';
import * as z from 'zod';
import * as crypto from 'crypto';

export interface CreateToolDto {
    name: string;
    description?: string;
    category?: SkillCategory;
    type: ToolType;
    config: any;
    inputSchema?: any;
    outputSchema?: any;
    timeout?: number;
    retryPolicy?: any;
    cacheStrategy?: ToolCacheStrategy;
    cacheTTL?: number;
    tags?: string[];
    documentation?: string;
    examples?: any[];
    isPublic?: boolean;
    dependencies?: string[];
    requirements?: any;
}

export interface UpdateToolDto {
    name?: string;
    description?: string;
    category?: SkillCategory;
    config?: any;
    inputSchema?: any;
    outputSchema?: any;
    timeout?: number;
    retryPolicy?: any;
    cacheStrategy?: ToolCacheStrategy;
    cacheTTL?: number;
    tags?: string[];
    documentation?: string;
    examples?: any[];
    isPublic?: boolean;
    isActive?: boolean;
    dependencies?: string[];
    requirements?: any;
}

export interface ToolVersionDto {
    version: string;
    description?: string;
    changes: string[];
    config: any;
    inputSchema: any;
    outputSchema: any;
    requirements?: any;
    isStable?: boolean;
    releaseNotes?: string;
    breakingChanges?: string[];
}

export interface ToolSearchOptions {
    query?: string;
    type?: ToolType;
    category?: SkillCategory;
    tags?: string[];
    isPublic?: boolean;
    isActive?: boolean;
    createdBy?: string;
    organizationId?: string;
    limit?: number;
    offset?: number;
    sortBy?: 'name' | 'createdAt' | 'usageCount' | 'successRate';
    sortOrder?: 'asc' | 'desc';
}

export interface ToolImportOptions {
    format: 'openapi' | 'postman' | 'curl' | 'json';
    data: string;
    overrides?: Partial<CreateToolDto>;
}

@Injectable()
export class ToolManagerService {
    private readonly logger = new Logger(ToolManagerService.name);

    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configService: ConfigService,
        private apixGateway: ApixGateway,
    ) { }

    // ============================================================================
    // TOOL CRUD OPERATIONS
    // ============================================================================

    async createTool(
        createDto: CreateToolDto,
        creatorId: string,
        organizationId?: string,
    ): Promise<any> {
        // Validate tool configuration
        await this.validateToolConfiguration(createDto.type, createDto.config);

        // Validate schemas if provided
        if (createDto.inputSchema) {
            this.validateSchema(createDto.inputSchema);
        }
        if (createDto.outputSchema) {
            this.validateSchema(createDto.outputSchema);
        }

        // Create tool
        const tool = await this.prisma.toolDefinition.create({
            data: {
                name: createDto.name,
                description: createDto.description,
                category: createDto.category || SkillCategory.CUSTOM,
                type: createDto.type,
                config: createDto.config,
                inputSchema: createDto.inputSchema || {},
                outputSchema: createDto.outputSchema || {},
                timeout: createDto.timeout || 30000,
                retryPolicy: createDto.retryPolicy || { maxRetries: 3, backoffStrategy: 'exponential' },
                cacheStrategy: createDto.cacheStrategy || ToolCacheStrategy.INPUT_HASH,
                cacheTTL: createDto.cacheTTL || 3600,
                tags: createDto.tags || [],
                documentation: createDto.documentation,
                examples: createDto.examples || [],
                isPublic: createDto.isPublic || false,
                dependencies: createDto.dependencies || [],
                requirements: createDto.requirements || {},
                createdBy: creatorId,
                organizationId,
            },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
                organization: true,
            },
        });

        // Create initial version
        await this.createToolVersion(tool.id, {
            version: '1.0.0',
            description: 'Initial version',
            changes: ['Initial release'],
            config: tool.config,
            inputSchema: tool.inputSchema,
            outputSchema: tool.outputSchema,
            requirements: tool.requirements,
            isStable: true,
            releaseNotes: 'Initial release of the tool',
        }, creatorId);

        // Cache the tool
        await this.cacheManager.set(`tool:${tool.id}`, tool, 300000);

        // Emit real-time event
        await this.apixGateway.emitToolEvent(
            tool.organizationId || 'system',
            tool.id,
            'tool.created',
            {
                toolId: tool.id,
                type: tool.type,
                organizationId: tool.organizationId,
                createdBy: creatorId,
            },
            { priority: 'normal' }
        );

        this.logger.log(`Created tool: ${tool.id} (${tool.name})`);

        return tool;
    }

    async updateTool(
        toolId: string,
        updateDto: UpdateToolDto,
        userId: string,
        organizationId: string,
    ): Promise<any> {
        // Check permissions
        const tool = await this.getToolWithPermissionCheck(toolId, userId, organizationId);

        // Validate configuration if being updated
        if (updateDto.config) {
            await this.validateToolConfiguration(tool.type, updateDto.config);
        }

        // Validate schemas if being updated
        if (updateDto.inputSchema) {
            this.validateSchema(updateDto.inputSchema);
        }
        if (updateDto.outputSchema) {
            this.validateSchema(updateDto.outputSchema);
        }

        // Check if this is a breaking change
        const hasBreakingChanges = this.detectBreakingChanges(tool, updateDto);

        // Update tool
        const updatedTool = await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                ...updateDto,
                updatedAt: new Date(),
            },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
                organization: true,
                versions: {
                    orderBy: { createdAt: 'desc' },
                    take: 1,
                },
            },
        });

        // Create new version if significant changes
        if (hasBreakingChanges || updateDto.config || updateDto.inputSchema || updateDto.outputSchema) {
            const latestVersion = updatedTool.versions[0];
            const newVersion = this.incrementVersion(latestVersion.version, hasBreakingChanges);

            await this.createToolVersion(toolId, {
                version: newVersion,
                description: `Updated to version ${newVersion}`,
                changes: this.generateChangeLog(tool, updateDto),
                config: updatedTool.config,
                inputSchema: updatedTool.inputSchema,
                outputSchema: updatedTool.outputSchema,
                requirements: updatedTool.requirements,
                breakingChanges: hasBreakingChanges ? this.getBreakingChangesList(tool, updateDto) : [],
            }, userId);
        }

        // Update cache
        await this.cacheManager.set(`tool:${toolId}`, updatedTool, 300000);

        // Emit real-time event
        await this.apixGateway.emitToolEvent(
            updatedTool.organizationId || 'system',
            toolId,
            'tool.updated',
            {
                toolId,
                version: updatedTool.version,
                changes: this.generateChangeLog(tool, updateDto),
                hasBreakingChanges,
            },
            { priority: 'normal' }
        );

        this.logger.log(`Updated tool: ${toolId}`);

        return updatedTool;
    }

    async deleteTool(toolId: string, userId: string, organizationId: string): Promise<void> {
        // Check permissions
        const tool = await this.getToolWithPermissionCheck(toolId, userId, organizationId);

        // Check if tool has active executions
        const activeExecutions = await this.prisma.toolExecution.count({
            where: {
                toolId,
                status: {
                    in: ['PENDING', 'RUNNING'],
                },
            },
        });

        if (activeExecutions > 0) {
            throw new BadRequestException('Cannot delete tool with active executions');
        }

        // Soft delete by deactivating
        await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                isActive: false,
                updatedAt: new Date(),
            },
        });

        // Remove from cache
        await this.cacheManager.del(`tool:${toolId}`);

        // Emit real-time event
        await this.apixGateway.emitToolEvent(
            organizationId,
            toolId,
            'tool.deleted',
            {
                toolId,
                organizationId,
                deletedBy: userId,
            },
            { priority: 'high' }
        );

        this.logger.log(`Deleted tool: ${toolId}`);
    }

    async getTool(toolId: string, organizationId?: string): Promise<any> {
        // Try cache first
        let tool = await this.cacheManager.get(`tool:${toolId}`) as any;

        if (!tool) {
            tool = await this.prisma.toolDefinition.findFirst({
                where: {
                    id: toolId,
                    isActive: true,
                    OR: [
                        { isPublic: true },
                        { organizationId },
                    ],
                },
                include: {
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true },
                    },
                    organization: true,
                    versions: {
                        orderBy: { createdAt: 'desc' },
                        take: 5,
                    },
                    analytics: {
                        orderBy: { date: 'desc' },
                        take: 30,
                    },
                    _count: {
                        select: {
                            executions: true,
                            versions: true,
                            cache: true,
                        },
                    },
                },
            });

            if (tool) {
                await this.cacheManager.set(`tool:${toolId}`, tool, 300000);
            }
        }

        if (!tool) {
            throw new NotFoundException('Tool not found');
        }

        return tool;
    }

    async searchTools(options: ToolSearchOptions): Promise<any> {
        const where: Prisma.ToolDefinitionWhereInput = {
            isActive: options.isActive !== false,
        };

        // Apply filters
        if (options.query) {
            where.OR = [
                { name: { contains: options.query, mode: 'insensitive' } },
                { description: { contains: options.query, mode: 'insensitive' } },
                { tags: { has: options.query } },
            ];
        }

        if (options.type) {
            where.type = options.type;
        }

        if (options.category) {
            where.category = options.category;
        }

        if (options.tags && options.tags.length > 0) {
            where.tags = {
                hasEvery: options.tags,
            };
        }

        if (options.isPublic !== undefined) {
            where.isPublic = options.isPublic;
        }

        if (options.createdBy) {
            where.createdBy = options.createdBy;
        }

        if (options.organizationId) {
            where.OR = [
                { organizationId: options.organizationId },
                { isPublic: true },
            ];
        }

        // Sorting
        const orderBy: Prisma.ToolDefinitionOrderByWithRelationInput = {};
        const sortBy = options.sortBy || 'createdAt';
        const sortOrder = options.sortOrder || 'desc';
        orderBy[sortBy] = sortOrder;

        const [tools, total] = await Promise.all([
            this.prisma.toolDefinition.findMany({
                where,
                include: {
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true },
                    },
                    _count: {
                        select: {
                            executions: true,
                            versions: true,
                        },
                    },
                },
                orderBy,
                take: options.limit || 20,
                skip: options.offset || 0,
            }),
            this.prisma.toolDefinition.count({ where }),
        ]);

        return {
            tools,
            total,
            hasMore: (options.offset || 0) + tools.length < total,
        };
    }

    // ============================================================================
    // TOOL VERSIONING
    // ============================================================================

    async createToolVersion(
        toolId: string,
        versionDto: ToolVersionDto,
        creatorId: string,
    ): Promise<any> {
        // Check if version already exists
        const existingVersion = await this.prisma.toolVersion.findUnique({
            where: {
                toolId_version: {
                    toolId,
                    version: versionDto.version,
                },
            },
        });

        if (existingVersion) {
            throw new BadRequestException(`Version ${versionDto.version} already exists`);
        }

        const version = await this.prisma.toolVersion.create({
            data: {
                toolId,
                version: versionDto.version,
                description: versionDto.description,
                changes: versionDto.changes,
                config: versionDto.config,
                inputSchema: versionDto.inputSchema,
                outputSchema: versionDto.outputSchema,
                requirements: versionDto.requirements || {},
                isStable: versionDto.isStable || false,
                releaseNotes: versionDto.releaseNotes,
                breakingChanges: versionDto.breakingChanges || [],
                createdBy: creatorId,
            },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
            },
        });

        // Update tool's current version
        await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                version: versionDto.version,
                config: versionDto.config,
                inputSchema: versionDto.inputSchema,
                outputSchema: versionDto.outputSchema,
                requirements: versionDto.requirements || {},
            },
        });

        // Invalidate cache
        await this.cacheManager.del(`tool:${toolId}`);

        this.logger.log(`Created version ${versionDto.version} for tool: ${toolId}`);

        return version;
    }

    async getToolVersions(toolId: string, organizationId?: string): Promise<any[]> {
        // Check tool access
        await this.getTool(toolId, organizationId);

        return this.prisma.toolVersion.findMany({
            where: { toolId },
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
            },
            orderBy: { createdAt: 'desc' },
        });
    }

    async rollbackToVersion(
        toolId: string,
        version: string,
        userId: string,
        organizationId: string,
    ): Promise<any> {
        // Check permissions
        await this.getToolWithPermissionCheck(toolId, userId, organizationId);

        // Get the target version
        const targetVersion = await this.prisma.toolVersion.findUnique({
            where: {
                toolId_version: {
                    toolId,
                    version,
                },
            },
        });

        if (!targetVersion) {
            throw new NotFoundException(`Version ${version} not found`);
        }

        // Update tool to the target version
        const updatedTool = await this.prisma.toolDefinition.update({
            where: { id: toolId },
            data: {
                version: targetVersion.version,
                config: targetVersion.config,
                inputSchema: targetVersion.inputSchema,
                outputSchema: targetVersion.outputSchema,
                requirements: targetVersion.requirements,
                updatedAt: new Date(),
            },
        });

        // Create a new version entry for this rollback
        const rollbackVersion = this.incrementVersion(updatedTool.version, false, 'patch');
        await this.createToolVersion(toolId, {
            version: rollbackVersion,
            description: `Rollback to version ${version}`,
            changes: [`Rolled back to version ${version}`],
            config: targetVersion.config,
            inputSchema: targetVersion.inputSchema,
            outputSchema: targetVersion.outputSchema,
            requirements: targetVersion.requirements,
        }, userId);

        // Invalidate cache
        await this.cacheManager.del(`tool:${toolId}`);

        this.logger.log(`Rolled back tool ${toolId} to version ${version}`);

        return updatedTool;
    }

    // ============================================================================
    // TOOL IMPORT/EXPORT
    // ============================================================================

    async importTool(
        importOptions: ToolImportOptions,
        creatorId: string,
        organizationId?: string,
    ): Promise<any> {
        const toolConfig = await this.parseImportData(importOptions);

        const createDto: CreateToolDto = {
            ...toolConfig,
            ...importOptions.overrides,
        };

        return this.createTool(createDto, creatorId, organizationId);
    }

    async exportTool(toolId: string, format: string, organizationId?: string): Promise<any> {
        const tool = await this.getTool(toolId, organizationId);

        switch (format) {
            case 'json':
                return this.exportAsJSON(tool);
            case 'openapi':
                return this.exportAsOpenAPI(tool);
            case 'postman':
                return this.exportAsPostman(tool);
            default:
                throw new BadRequestException(`Unsupported export format: ${format}`);
        }
    }

    // ============================================================================
    // HELPER METHODS
    // ============================================================================

    private async getToolWithPermissionCheck(
        toolId: string,
        userId: string,
        organizationId: string,
    ): Promise<any> {
        const tool = await this.prisma.toolDefinition.findFirst({
            where: {
                id: toolId,
                OR: [
                    { createdBy: userId },
                    { organizationId },
                ],
            },
        });

        if (!tool) {
            throw new NotFoundException('Tool not found or access denied');
        }

        return tool;
    }

    private async validateToolConfiguration(type: ToolType, config: any): Promise<void> {
        switch (type) {
            case ToolType.API_FETCH:
                this.validateAPIFetchConfig(config);
                break;
            case ToolType.FUNCTION_CALL:
                this.validateFunctionCallConfig(config);
                break;
            case ToolType.RAG:
                this.validateRAGConfig(config);
                break;
            default:
                // Custom validation can be added here
                break;
        }
    }

    private validateAPIFetchConfig(config: any): void {
        const schema = z.object({
            endpoint: z.string().url(),
            method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
            headers: z.record(z.string(), z.string()).optional(),
            authentication: z.object({
                type: z.enum(['none', 'bearer', 'oauth', 'api_key']),
                config: z.record(z.string(), z.any()).optional(),
            }).optional(),
            requestMapping: z.record(z.string(), z.any()).optional(),
            responseMapping: z.record(z.string(), z.any()).optional(),
        });

        try {
            schema.parse(config);
        } catch (error) {
            throw new BadRequestException(`Invalid API fetch configuration: ${error.message}`);
        }
    }

    private validateFunctionCallConfig(config: any): void {
        const schema = z.object({
            code: z.string().min(1),
            runtime: z.enum(['javascript', 'python', 'shell']),
            environment: z.record(z.string(), z.any()).optional(),
            packages: z.array(z.string()).optional(),
        });

        try {
            schema.parse(config);
        } catch (error) {
            throw new BadRequestException(`Invalid function call configuration: ${error.message}`);
        }
    }

    private validateRAGConfig(config: any): void {
        const schema = z.object({
            vectorStore: z.object({
                type: z.enum(['pinecone', 'weaviate', 'chromadb', 'local']),
                config: z.record(z.string(), z.any()),
            }),
            embedding: z.object({
                provider: z.string(),
                model: z.string(),
            }),
            retrieval: z.object({
                topK: z.number().min(1).max(100),
                scoreThreshold: z.number().min(0).max(1).optional(),
            }),
        });

        try {
            schema.parse(config);
        } catch (error) {
            throw new BadRequestException(`Invalid RAG configuration: ${error.message}`);
        }
    }

    private validateSchema(schema: any): void {
        try {
            // Basic JSON schema validation
            if (typeof schema !== 'object' || schema === null) {
                throw new Error('Schema must be an object');
            }

            // Additional validation can be added here
            JSON.stringify(schema); // Ensure it's serializable
        } catch (error) {
            throw new BadRequestException(`Invalid schema: ${error.message}`);
        }
    }

    private detectBreakingChanges(oldTool: any, updateDto: UpdateToolDto): boolean {
        // Check if input/output schemas have breaking changes
        if (updateDto.inputSchema && !this.isSchemaCompatible(oldTool.inputSchema, updateDto.inputSchema)) {
            return true;
        }

        if (updateDto.outputSchema && !this.isSchemaCompatible(oldTool.outputSchema, updateDto.outputSchema)) {
            return true;
        }

        // Check if configuration has breaking changes
        if (updateDto.config && this.hasBreakingConfigChanges(oldTool.config, updateDto.config)) {
            return true;
        }

        return false;
    }

    private isSchemaCompatible(oldSchema: any, newSchema: any): boolean {
        // Simplified compatibility check
        // In a real implementation, this would be more sophisticated
        try {
            const oldProps = Object.keys(oldSchema.properties || {});
            const newProps = Object.keys(newSchema.properties || {});

            // Check if any required properties were removed
            const oldRequired = oldSchema.required || [];
            const newRequired = newSchema.required || [];

            return oldRequired.every((prop: string) => newRequired.includes(prop));
        } catch {
            return false;
        }
    }

    private hasBreakingConfigChanges(oldConfig: any, newConfig: any): boolean {
        // Check for breaking changes in configuration
        // This is tool-type specific logic
        return false; // Simplified for now
    }

    private incrementVersion(currentVersion: string, isBreaking: boolean, type?: string): string {
        const [major, minor, patch] = currentVersion.split('.').map(Number);

        if (isBreaking || type === 'major') {
            return `${major + 1}.0.0`;
        } else if (type === 'minor') {
            return `${major}.${minor + 1}.0`;
        } else {
            return `${major}.${minor}.${patch + 1}`;
        }
    }

    private generateChangeLog(oldTool: any, updateDto: UpdateToolDto): string[] {
        const changes: string[] = [];

        if (updateDto.name && updateDto.name !== oldTool.name) {
            changes.push(`Renamed from "${oldTool.name}" to "${updateDto.name}"`);
        }

        if (updateDto.description !== undefined && updateDto.description !== oldTool.description) {
            changes.push('Updated description');
        }

        if (updateDto.config) {
            changes.push('Updated configuration');
        }

        if (updateDto.inputSchema) {
            changes.push('Updated input schema');
        }

        if (updateDto.outputSchema) {
            changes.push('Updated output schema');
        }

        if (updateDto.tags) {
            changes.push('Updated tags');
        }

        return changes;
    }

    private getBreakingChangesList(oldTool: any, updateDto: UpdateToolDto): string[] {
        const breakingChanges: string[] = [];

        if (updateDto.inputSchema && !this.isSchemaCompatible(oldTool.inputSchema, updateDto.inputSchema)) {
            breakingChanges.push('Input schema has breaking changes');
        }

        if (updateDto.outputSchema && !this.isSchemaCompatible(oldTool.outputSchema, updateDto.outputSchema)) {
            breakingChanges.push('Output schema has breaking changes');
        }

        return breakingChanges;
    }

    private async parseImportData(importOptions: ToolImportOptions): Promise<any> {
        switch (importOptions.format) {
            case 'json':
                return JSON.parse(importOptions.data);
            case 'openapi':
                return this.parseOpenAPISpec(importOptions.data);
            case 'postman':
                return this.parsePostmanCollection(importOptions.data);
            case 'curl':
                return this.parseCurlCommand(importOptions.data);
            default:
                throw new BadRequestException(`Unsupported import format: ${importOptions.format}`);
        }
    }

    private parseOpenAPISpec(data: string): any {
        // Simplified OpenAPI parsing
        const spec = JSON.parse(data);

        return {
            name: spec.info?.title || 'Imported API Tool',
            description: spec.info?.description,
            type: ToolType.API_FETCH,
            config: {
                endpoint: spec.servers?.[0]?.url || '',
                method: 'GET',
                // Additional parsing logic here
            },
        };
    }

    private parsePostmanCollection(data: string): any {
        // Simplified Postman parsing
        const collection = JSON.parse(data);

        return {
            name: collection.info?.name || 'Imported Postman Tool',
            description: collection.info?.description,
            type: ToolType.API_FETCH,
            config: {
                // Postman parsing logic here
            },
        };
    }

    private parseCurlCommand(data: string): any {
        // Simplified cURL parsing
        const urlMatch = data.match(/curl\s+(?:-X\s+\w+\s+)?['"]?([^'")\s]+)/);
        const methodMatch = data.match(/-X\s+(\w+)/);

        return {
            name: 'Imported cURL Tool',
            type: ToolType.API_FETCH,
            config: {
                endpoint: urlMatch?.[1] || '',
                method: methodMatch?.[1] || 'GET',
            },
        };
    }

    private exportAsJSON(tool: any): any {
        return {
            name: tool.name,
            description: tool.description,
            type: tool.type,
            config: tool.config,
            inputSchema: tool.inputSchema,
            outputSchema: tool.outputSchema,
            metadata: {
                version: tool.version,
                tags: tool.tags,
                documentation: tool.documentation,
                examples: tool.examples,
            },
        };
    }

    private exportAsOpenAPI(tool: any): any {
        // Generate OpenAPI spec from tool
        return {
            openapi: '3.0.0',
            info: {
                title: tool.name,
                description: tool.description,
                version: tool.version,
            },
            // Additional OpenAPI spec generation
        };
    }

    private exportAsPostman(tool: any): any {
        // Generate Postman collection from tool
        return {
            info: {
                name: tool.name,
                description: tool.description,
            },
            // Additional Postman collection generation
        };
    }

    // ============================================================================
    // PUBLIC QUERY METHODS
    // ============================================================================

    async getToolStats(organizationId?: string): Promise<any> {
        const where: Prisma.ToolDefinitionWhereInput = {
            isActive: true,
        };

        if (organizationId) {
            where.OR = [
                { organizationId },
                { isPublic: true },
            ];
        }

        const [
            totalTools,
            toolsByType,
            toolsByCategory,
            recentTools,
        ] = await Promise.all([
            this.prisma.toolDefinition.count({ where }),
            this.prisma.toolDefinition.groupBy({
                by: ['type'],
                where,
                _count: true,
            }),
            this.prisma.toolDefinition.groupBy({
                by: ['category'],
                where,
                _count: true,
            }),
            this.prisma.toolDefinition.findMany({
                where,
                orderBy: { createdAt: 'desc' },
                take: 5,
                select: {
                    id: true,
                    name: true,
                    type: true,
                    usageCount: true,
                    createdAt: true,
                },
            }),
        ]);

        return {
            totalTools,
            toolsByType: Object.fromEntries(
                toolsByType.map(item => [item.type, item._count])
            ),
            toolsByCategory: Object.fromEntries(
                toolsByCategory.map(item => [item.category, item._count])
            ),
            recentTools,
        };
    }

    async getPopularTools(organizationId?: string, limit = 10): Promise<any[]> {
        const where: Prisma.ToolDefinitionWhereInput = {
            isActive: true,
        };

        if (organizationId) {
            where.OR = [
                { organizationId },
                { isPublic: true },
            ];
        }

        return this.prisma.toolDefinition.findMany({
            where,
            orderBy: [
                { usageCount: 'desc' },
                { successRate: 'desc' },
            ],
            take: limit,
            include: {
                creator: {
                    select: { id: true, firstName: true, lastName: true },
                },
                _count: {
                    select: { executions: true },
                },
            },
        });
    }
}