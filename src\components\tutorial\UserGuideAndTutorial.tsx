import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Play,
  Pause,
  RotateCcw,
  CheckCircle,
  ArrowRight,
  Lightbulb,
  BookOpen,
  Video,
  HelpCircle,
  Zap,
  Bot,
  Wrench,
  GitBranch,
  User,
  Clock,
  Layers,
  MousePointer,
  Keyboard,
  Eye,
  Target,
  Workflow,
  MessageSquare,
  Mail,
  Calendar,
  Phone
} from 'lucide-react';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  component: string;
  action: string;
  visual: {
    type: 'highlight' | 'arrow' | 'circle' | 'tooltip';
    target: string;
    position: 'top' | 'bottom' | 'left' | 'right';
  };
  validation?: () => boolean;
  tips: string[];
}

interface TutorialSection {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  steps: TutorialStep[];
}

const tutorialSections: TutorialSection[] = [
  {
    id: 'getting-started',
    title: 'Getting Started with SynapseAI',
    description: 'Learn the basics of creating your first AI workflow',
    icon: BookOpen,
    difficulty: 'beginner',
    estimatedTime: '10 minutes',
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to SynapseAI',
        description: 'SynapseAI is a no-code platform that lets you create powerful AI workflows by simply dragging and connecting components.',
        component: 'dashboard',
        action: 'Click "Create New Workflow" to begin',
        visual: {
          type: 'highlight',
          target: '.create-workflow-button',
          position: 'bottom'
        },
        tips: [
          'No coding experience required',
          'Visual drag-and-drop interface',
          'Real-time execution monitoring',
          'Enterprise-grade security'
        ]
      },
      {
        id: 'workflow-canvas',
        title: 'Understanding the Workflow Canvas',
        description: 'The canvas is where you build your AI workflows by connecting different nodes together.',
        component: 'workflow-builder',
        action: 'Explore the canvas area',
        visual: {
          type: 'circle',
          target: '.workflow-canvas',
          position: 'top'
        },
        tips: [
          'Drag nodes from the left panel',
          'Connect nodes by drawing lines between them',
          'Right-click for context menus',
          'Use mouse wheel to zoom in/out'
        ]
      },
      {
        id: 'node-palette',
        title: 'Node Palette Overview',
        description: 'The left panel contains all available components you can use in your workflows.',
        component: 'workflow-builder',
        action: 'Browse the available nodes',
        visual: {
          type: 'highlight',
          target: '.node-palette',
          position: 'right'
        },
        tips: [
          'Agents: AI-powered decision makers',
          'Tools: Specific actions and operations',
          'Flow Control: Logic and branching',
          'Human Input: Manual intervention points'
        ]
      }
    ]
  },
  {
    id: 'building-first-workflow',
    title: 'Building Your First Workflow',
    description: 'Create a simple workflow that processes text using AI',
    icon: Zap,
    difficulty: 'beginner',
    estimatedTime: '15 minutes',
    steps: [
      {
        id: 'add-start-node',
        title: 'Add a Start Node',
        description: 'Every workflow begins with a Start node that defines the entry point.',
        component: 'workflow-builder',
        action: 'Drag a Start node to the canvas',
        visual: {
          type: 'arrow',
          target: '.start-node',
          position: 'right'
        },
        tips: [
          'Start nodes are automatically created',
          'They define workflow input parameters',
          'Only one start node per workflow',
          'Green color indicates entry points'
        ]
      },
      {
        id: 'add-agent-node',
        title: 'Add an AI Agent',
        description: 'AI Agents are the brain of your workflow - they make intelligent decisions and process information.',
        component: 'workflow-builder',
        action: 'Drag an Agent node from the palette',
        visual: {
          type: 'highlight',
          target: '.agent-node-palette',
          position: 'right'
        },
        tips: [
          'Agents can understand natural language',
          'They can analyze, summarize, and transform data',
          'Configure them with custom instructions',
          'Blue color indicates AI processing'
        ]
      },
      {
        id: 'connect-nodes',
        title: 'Connect the Nodes',
        description: 'Draw a connection from the Start node to the Agent node to create the workflow flow.',
        component: 'workflow-builder',
        action: 'Click and drag from Start output to Agent input',
        visual: {
          type: 'arrow',
          target: '.node-connection-point',
          position: 'top'
        },
        tips: [
          'Click on the output handle (right side)',
          'Drag to the input handle (left side)',
          'Connections show data flow direction',
          'Animated lines indicate active connections'
        ]
      },
      {
        id: 'configure-agent',
        title: 'Configure the Agent',
        description: 'Set up the agent with instructions on what it should do with the input data.',
        component: 'workflow-builder',
        action: 'Click on the Agent node to open configuration',
        visual: {
          type: 'highlight',
          target: '.agent-config-panel',
          position: 'left'
        },
        tips: [
          'Write clear, specific instructions',
          'Use natural language descriptions',
          'Set appropriate response length limits',
          'Test different creativity levels'
        ]
      }
    ]
  },
  {
    id: 'advanced-workflows',
    title: 'Advanced Workflow Patterns',
    description: 'Learn to build complex workflows with branching, parallel processing, and human input',
    icon: Layers,
    difficulty: 'intermediate',
    estimatedTime: '25 minutes',
    steps: [
      {
        id: 'conditional-branching',
        title: 'Adding Conditional Logic',
        description: 'Use Condition nodes to create different paths based on data or AI decisions.',
        component: 'workflow-builder',
        action: 'Add a Condition node and configure the logic',
        visual: {
          type: 'highlight',
          target: '.condition-node',
          position: 'top'
        },
        tips: [
          'Conditions create true/false branches',
          'Use simple expressions like "score > 80"',
          'JavaScript expressions are supported',
          'Purple color indicates decision points'
        ]
      },
      {
        id: 'parallel-processing',
        title: 'Parallel Processing',
        description: 'Execute multiple operations simultaneously to improve efficiency.',
        component: 'workflow-builder',
        action: 'Add a Parallel node and configure multiple branches',
        visual: {
          type: 'circle',
          target: '.parallel-node',
          position: 'bottom'
        },
        tips: [
          'Run multiple tasks at the same time',
          'Combine results automatically',
          'Faster execution for independent tasks',
          'Orange color indicates parallel execution'
        ]
      },
      {
        id: 'human-input',
        title: 'Human-in-the-Loop',
        description: 'Add human decision points where manual approval or input is required.',
        component: 'workflow-builder',
        action: 'Add a Human Input node for manual review',
        visual: {
          type: 'highlight',
          target: '.human-input-node',
          position: 'right'
        },
        tips: [
          'Pause workflow for human decisions',
          'Configure input types (text, choice, file)',
          'Set timeout limits',
          'Indigo color indicates human interaction'
        ]
      }
    ]
  },
  {
    id: 'hybrid-ai-workflows',
    title: 'Hybrid AI-Tool Workflows',
    description: 'Combine AI agents with specialized tools for powerful automation',
    icon: Bot,
    difficulty: 'advanced',
    estimatedTime: '30 minutes',
    steps: [
      {
        id: 'hybrid-node-intro',
        title: 'Understanding Hybrid Nodes',
        description: 'Hybrid nodes combine AI intelligence with tool capabilities for sophisticated processing.',
        component: 'workflow-builder',
        action: 'Add a Hybrid node to your workflow',
        visual: {
          type: 'highlight',
          target: '.hybrid-node',
          position: 'top'
        },
        tips: [
          'Combines agent reasoning with tool execution',
          'Multiple execution patterns available',
          'Automatic coordination between AI and tools',
          'Purple gradient indicates hybrid processing'
        ]
      },
      {
        id: 'execution-patterns',
        title: 'Execution Patterns',
        description: 'Choose how the AI agent and tools should work together.',
        component: 'workflow-builder',
        action: 'Configure the execution pattern in the hybrid node',
        visual: {
          type: 'tooltip',
          target: '.execution-pattern-selector',
          position: 'bottom'
        },
        tips: [
          'Agent-First: AI decides, then uses tools',
          'Tool-First: Gather data, then AI processes',
          'Parallel: AI and tools work simultaneously',
          'Orchestration: AI coordinates multiple tools'
        ]
      }
    ]
  }
];

const interactiveExamples = [
  {
    id: 'text-analysis',
    title: 'Text Analysis Workflow',
    description: 'Analyze customer feedback and categorize sentiment',
    difficulty: 'beginner',
    nodes: ['Start', 'AI Agent', 'Condition', 'End'],
    useCase: 'Customer Support',
    estimatedTime: '5 minutes'
  },
  {
    id: 'data-processing',
    title: 'Data Processing Pipeline',
    description: 'Extract, transform, and analyze data from multiple sources',
    difficulty: 'intermediate',
    nodes: ['Start', 'Tool', 'Parallel', 'AI Agent', 'End'],
    useCase: 'Data Analytics',
    estimatedTime: '10 minutes'
  },
  {
    id: 'approval-workflow',
    title: 'Document Approval Process',
    description: 'Automated document review with human approval steps',
    difficulty: 'intermediate',
    nodes: ['Start', 'AI Agent', 'Human Input', 'Condition', 'Tool', 'End'],
    useCase: 'Document Management',
    estimatedTime: '15 minutes'
  }
];

export default function UserGuideAndTutorial() {
  const [activeSection, setActiveSection] = useState('getting-started');
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [isPlaying, setIsPlaying] = useState(false);
  const [showInteractiveMode, setShowInteractiveMode] = useState(false);

  const currentSection = tutorialSections.find(s => s.id === activeSection);
  const progress = currentSection ? (currentStep / currentSection.steps.length) * 100 : 0;

  const handleStepComplete = (stepId: string) => {
    setCompletedSteps(prev => new Set(Array.from(prev).concat(stepId)));
    if (currentSection && currentStep < currentSection.steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const startInteractiveDemo = (exampleId: string) => {
    setShowInteractiveMode(true);
    // This would launch an interactive demo
    console.log('Starting interactive demo:', exampleId);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-background min-h-screen w-full">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BookOpen className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">SynapseAI User Guide</h1>
              <p className="text-muted-foreground">
                Learn to build powerful AI workflows with our no-code platform
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold">4</div>
                    <div className="text-xs text-muted-foreground">Tutorial Sections</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold">80</div>
                    <div className="text-xs text-muted-foreground">Minutes Total</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-purple-600" />
                  <div>
                    <div className="text-2xl font-bold">3</div>
                    <div className="text-xs text-muted-foreground">Interactive Examples</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-orange-600" />
                  <div>
                    <div className="text-2xl font-bold">{completedSteps.size}</div>
                    <div className="text-xs text-muted-foreground">Steps Completed</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <Tabs value={activeSection} onValueChange={setActiveSection} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            {tutorialSections.map((section) => (
              <TabsTrigger key={section.id} value={section.id} className="flex items-center space-x-2">
                <section.icon className="h-4 w-4" />
                <span className="hidden sm:inline">{section.title}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {tutorialSections.map((section) => (
            <TabsContent key={section.id} value={section.id} className="mt-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Tutorial Content */}
                <div className="lg:col-span-2">
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <section.icon className="h-6 w-6 text-primary" />
                          <div>
                            <CardTitle>{section.title}</CardTitle>
                            <p className="text-sm text-muted-foreground mt-1">
                              {section.description}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getDifficultyColor(section.difficulty)}>
                            {section.difficulty}
                          </Badge>
                          <Badge variant="outline">
                            <Clock className="h-3 w-3 mr-1" />
                            {section.estimatedTime}
                          </Badge>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{Math.round(progress)}%</span>
                        </div>
                        <Progress value={progress} className="w-full" />
                      </div>
                    </CardHeader>

                    <CardContent>
                      {/* Current Step */}
                      {section.steps[currentStep] && (
                        <div className="space-y-4">
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center justify-center w-8 h-8 bg-primary text-primary-foreground rounded-full text-sm font-medium">
                              {currentStep + 1}
                            </div>
                            <h3 className="text-lg font-semibold">
                              {section.steps[currentStep].title}
                            </h3>
                          </div>

                          <p className="text-muted-foreground">
                            {section.steps[currentStep].description}
                          </p>

                          <Alert>
                            <MousePointer className="h-4 w-4" />
                            <AlertDescription>
                              <strong>Action:</strong> {section.steps[currentStep].action}
                            </AlertDescription>
                          </Alert>

                          {/* Tips */}
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <div className="flex items-center space-x-2 mb-2">
                              <Lightbulb className="h-4 w-4 text-blue-600" />
                              <span className="font-medium text-blue-900">Pro Tips</span>
                            </div>
                            <ul className="space-y-1 text-sm text-blue-800">
                              {section.steps[currentStep].tips.map((tip, index) => (
                                <li key={index} className="flex items-start space-x-2">
                                  <span className="text-blue-400 mt-1">•</span>
                                  <span>{tip}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Navigation */}
                          <div className="flex justify-between pt-4">
                            <Button
                              variant="outline"
                              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                              disabled={currentStep === 0}
                            >
                              Previous
                            </Button>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                onClick={() => setIsPlaying(!isPlaying)}
                              >
                                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                                {isPlaying ? 'Pause' : 'Play'}
                              </Button>
                              <Button
                                onClick={() => handleStepComplete(section.steps[currentStep].id)}
                                disabled={completedSteps.has(section.steps[currentStep].id)}
                              >
                                {completedSteps.has(section.steps[currentStep].id) ? (
                                  <>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Completed
                                  </>
                                ) : (
                                  <>
                                    Next Step
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                  </>
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                  {/* Step Overview */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Steps Overview</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="space-y-2">
                          {section.steps.map((step, index) => (
                            <div
                              key={step.id}
                              className={`flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-colors ${index === currentStep
                                  ? 'bg-primary/10 border border-primary/20'
                                  : completedSteps.has(step.id)
                                    ? 'bg-green-50 border border-green-200'
                                    : 'hover:bg-muted'
                                }`}
                              onClick={() => setCurrentStep(index)}
                            >
                              <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${completedSteps.has(step.id)
                                  ? 'bg-green-500 text-white'
                                  : index === currentStep
                                    ? 'bg-primary text-primary-foreground'
                                    : 'bg-muted text-muted-foreground'
                                }`}>
                                {completedSteps.has(step.id) ? (
                                  <CheckCircle className="h-3 w-3" />
                                ) : (
                                  index + 1
                                )}
                              </div>
                              <span className={`text-sm ${index === currentStep ? 'font-medium' : ''
                                }`}>
                                {step.title}
                              </span>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>

                  {/* Quick Reference */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Quick Reference</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2 flex items-center">
                          <Keyboard className="h-4 w-4 mr-2" />
                          Keyboard Shortcuts
                        </h4>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <div>Ctrl+S - Save workflow</div>
                          <div>Ctrl+R - Run workflow</div>
                          <div>Delete - Remove selected node</div>
                          <div>Ctrl+D - Duplicate node</div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-2 flex items-center">
                          <Eye className="h-4 w-4 mr-2" />
                          Node Colors
                        </h4>
                        <div className="space-y-1 text-sm">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-blue-500 rounded"></div>
                            <span>AI Agents</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-orange-500 rounded"></div>
                            <span>Tools</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-purple-500 rounded"></div>
                            <span>Conditions</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded"></div>
                            <span>Start/End</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Interactive Examples Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Video className="h-5 w-5" />
              <span>Interactive Examples</span>
            </CardTitle>
            <p className="text-muted-foreground">
              Try these hands-on examples to practice building real workflows
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {interactiveExamples.map((example) => (
                <Card key={example.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">{example.title}</h3>
                        <Badge className={getDifficultyColor(example.difficulty)}>
                          {example.difficulty}
                        </Badge>
                      </div>

                      <p className="text-sm text-muted-foreground">
                        {example.description}
                      </p>

                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Workflow className="h-3 w-3" />
                        <span>{example.nodes.join(' → ')}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{example.estimatedTime}</span>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => startInteractiveDemo(example.id)}
                        >
                          Try Now
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Help Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <HelpCircle className="h-5 w-5" />
              <span>Need More Help?</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-medium">Documentation</h3>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Complete User Manual
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Video className="h-4 w-4 mr-2" />
                    Video Tutorials
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <HelpCircle className="h-4 w-4 mr-2" />
                    FAQ & Troubleshooting
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Community & Support</h3>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Community Forum
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Phone className="h-4 w-4 mr-2" />
                   
                    Contact Support
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Mail className="h-4 w-4 mr-2" />
                   
                    Email Support
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="h-4 w-4 mr-2" />
                   
                    Schedule Demo
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}