import { PrismaService } from '../../prisma/prisma.service';
import { ApixGateway } from '../../apix/apix.gateway';
import { SessionsService } from '../../sessions/sessions.service';
import { NodeExecutorService } from './node-executor.service';
import { VariableResolverService } from './variable-resolver.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
export interface ExecutionContext {
    executionId: string;
    workflowId: string;
    organizationId: string;
    userId: string;
    variables: Record<string, any>;
    nodeStates: Record<string, any>;
    currentNode?: string;
    completedNodes: Set<string>;
    failedNodes: Set<string>;
    pausedNodes: Set<string>;
    startTime: number;
    options: {
        async?: boolean;
        priority?: 'low' | 'normal' | 'high';
        timeout?: number;
        retryPolicy?: {
            maxRetries: number;
            backoffStrategy: 'linear' | 'exponential';
            retryDelay: number;
        };
    };
}
export declare class FlowControllerService {
    private prisma;
    private apixGateway;
    private sessionsService;
    private nodeExecutor;
    private variableResolver;
    private eventEmitter;
    private readonly logger;
    private activeExecutions;
    constructor(prisma: PrismaService, apixGateway: ApixGateway, sessionsService: SessionsService, nodeExecutor: NodeExecutorService, variableResolver: VariableResolverService, eventEmitter: EventEmitter2);
    startExecution(workflowId: string, userId: string, organizationId: string, input?: Record<string, any>, options?: any): Promise<string>;
    private executeWorkflowAsync;
    private executeWorkflow;
    private executeNode;
    private retryNode;
    private continueExecution;
    private findStartNodes;
    private findNextNodes;
    private completeExecution;
    private handleExecutionError;
    cancelExecution(executionId: string, organizationId: string): Promise<void>;
    pauseExecution(executionId: string, organizationId: string): Promise<void>;
    resumeExecution(executionId: string, organizationId: string): Promise<void>;
    getExecutionStatus(executionId: string, organizationId: string): Promise<{
        isActive: boolean;
        currentNode: string;
        completedNodes: string[];
        failedNodes: string[];
        variables: any;
        steps: {
            error: string | null;
            id: string;
            name: string;
            type: string;
            output: import("@prisma/client/runtime/library").JsonValue | null;
            input: import("@prisma/client/runtime/library").JsonValue;
            duration: number | null;
            status: import(".prisma/client").$Enums.ExecutionStatus;
            startedAt: Date;
            completedAt: Date | null;
            stepId: string;
            executionId: string;
        }[];
        error: string | null;
        id: string;
        output: import("@prisma/client/runtime/library").JsonValue | null;
        input: import("@prisma/client/runtime/library").JsonValue;
        sessionId: string;
        duration: number | null;
        status: import(".prisma/client").$Enums.ExecutionStatus;
        startedAt: Date;
        completedAt: Date | null;
        workflowId: string;
    }>;
    private updateExecutionStatus;
    getActiveExecutions(): ExecutionContext[];
    getHealthStatus(): {
        activeExecutions: number;
        status: string;
        uptime: number;
    };
}
