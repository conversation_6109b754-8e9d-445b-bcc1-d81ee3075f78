import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ToolCacheStrategy } from '@prisma/client';
import { MockEventEmitter } from '../../agents/mocks/event-emitter.mock';
export interface CacheOptions {
    ttl?: number;
    strategy?: ToolCacheStrategy;
    dependencies?: string[];
    compress?: boolean;
}
export interface CacheStats {
    hits: number;
    misses: number;
    hitRate: number;
    totalSize: number;
    entryCount: number;
    avgEntrySize: number;
}
export declare class ToolCacheService {
    private prisma;
    private cacheManager;
    private configService;
    private eventEmitter;
    private readonly logger;
    private readonly defaultTTL;
    private readonly maxCacheSize;
    constructor(prisma: PrismaService, cacheManager: Cache, configService: ConfigService, eventEmitter: MockEventEmitter);
    get(toolId: string, input: any): Promise<any>;
    set(toolId: string, input: any, output: any, ttl?: number, options?: CacheOptions): Promise<void>;
    invalidate(toolId: string, inputHash?: string, reason?: string): Promise<number>;
    invalidateByDependency(dependency: string): Promise<number>;
    cleanupExpiredEntries(): Promise<number>;
    optimizeCache(): Promise<void>;
    private evictLeastUsedEntries;
    getCacheStats(toolId?: string): Promise<CacheStats>;
    getToolCacheStats(toolId: string): Promise<any>;
    private updateCacheAnalytics;
    private generateInputHash;
    private generateDependencyHash;
    private calculateEntrySize;
    private isCacheValid;
    private recordCacheHit;
    private recordCacheMiss;
    private clearMemoryCacheForTool;
    clearAllCache(): Promise<number>;
    getCacheHealth(): Promise<any>;
}
