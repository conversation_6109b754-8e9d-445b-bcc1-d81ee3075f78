import { PrismaService } from '../../prisma/prisma.service';
import { Cache } from 'cache-manager';
import { AgentType, AgentStatus, Prisma } from '@prisma/client';
import { SessionMemoryService } from './session-memory.service';
import { SkillExecutorService } from './skill-executor.service';
import { ProviderRouterService } from './provider-router.service';
import { TaskTrackerService } from './task-tracker.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
export interface CreateAgentInstanceDto {
    name: string;
    description?: string;
    templateId?: string;
    type: AgentType;
    config?: any;
    systemPrompt?: string;
    instructions?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: any;
    canCollaborate?: boolean;
    shareContext?: boolean;
    priority?: number;
}
export interface UpdateAgentInstanceDto {
    name?: string;
    description?: string;
    status?: AgentStatus;
    config?: any;
    systemPrompt?: string;
    instructions?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    skills?: string[];
    tools?: string[];
    capabilities?: any;
    canCollaborate?: boolean;
    shareContext?: boolean;
    priority?: number;
}
export interface AgentExecutionContext {
    agentId: string;
    sessionId: string;
    userId?: string;
    input: any;
    context?: any;
    metadata?: any;
}
export interface AgentExecutionResult {
    success: boolean;
    output?: any;
    error?: string;
    metadata?: any;
    tokens?: number;
    cost?: number;
    duration?: number;
}
export declare class AgentOrchestratorService {
    private prisma;
    private cacheManager;
    private sessionMemory;
    private skillExecutor;
    private providerRouter;
    private taskTracker;
    private eventEmitter;
    constructor(prisma: PrismaService, cacheManager: Cache, sessionMemory: SessionMemoryService, skillExecutor: SkillExecutorService, providerRouter: ProviderRouterService, taskTracker: TaskTrackerService, eventEmitter: EventEmitter2);
    createAgentInstance(createDto: CreateAgentInstanceDto, creatorId: string, organizationId: string): Promise<{
        organization: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            domain: string | null;
            slug: string;
            settings: Prisma.JsonValue;
            branding: Prisma.JsonValue;
            maxUsers: number | null;
            features: string[];
            plan: string;
            planExpires: Date | null;
        };
        creator: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string;
            preferences: Prisma.JsonValue;
            email: string;
            password: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
            role: import(".prisma/client").$Enums.Role;
            lastLoginAt: Date | null;
            mfaEnabled: boolean;
            mfaSecret: string | null;
            backupCodes: string[];
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            emailVerified: boolean;
            emailVerificationToken: string | null;
            loginAttempts: number;
            lockoutUntil: Date | null;
        };
        template: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string | null;
            name: string;
            version: string;
            config: Prisma.JsonValue;
            description: string;
            providers: string[];
            tags: string[];
            category: import(".prisma/client").$Enums.SkillCategory;
            isPublic: boolean;
            createdBy: string;
            usage: number;
            systemPrompt: string | null;
            maxTokens: number | null;
            skills: string[];
            instructions: string | null;
            rating: number | null;
            downloads: number;
            featured: boolean;
            changelog: Prisma.JsonValue;
            minTokens: number | null;
            configSchema: Prisma.JsonValue;
        };
    } & {
        provider: string;
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        status: import(".prisma/client").$Enums.AgentStatus;
        type: import(".prisma/client").$Enums.AgentType;
        version: string;
        config: Prisma.JsonValue;
        description: string | null;
        tools: string[];
        timeout: number;
        retryPolicy: Prisma.JsonValue;
        priority: number;
        creatorId: string;
        model: string;
        systemPrompt: string | null;
        maxTokens: number;
        temperature: number;
        skills: string[];
        templateId: string | null;
        instructions: string | null;
        topP: number;
        memoryType: string;
        maxMemorySize: number;
        memoryWindow: number;
        capabilities: Prisma.JsonValue;
        rateLimits: Prisma.JsonValue;
        canCollaborate: boolean;
        shareContext: boolean;
        lastDeployed: Date | null;
        deployConfig: Prisma.JsonValue;
    }>;
    updateAgentInstance(agentId: string, updateDto: UpdateAgentInstanceDto, userId: string, organizationId: string): Promise<{
        organization: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            domain: string | null;
            slug: string;
            settings: Prisma.JsonValue;
            branding: Prisma.JsonValue;
            maxUsers: number | null;
            features: string[];
            plan: string;
            planExpires: Date | null;
        };
        creator: {
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string;
            preferences: Prisma.JsonValue;
            email: string;
            password: string;
            firstName: string;
            lastName: string;
            avatar: string | null;
            role: import(".prisma/client").$Enums.Role;
            lastLoginAt: Date | null;
            mfaEnabled: boolean;
            mfaSecret: string | null;
            backupCodes: string[];
            passwordResetToken: string | null;
            passwordResetExpires: Date | null;
            emailVerified: boolean;
            emailVerificationToken: string | null;
            loginAttempts: number;
            lockoutUntil: Date | null;
        };
        template: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string | null;
            name: string;
            version: string;
            config: Prisma.JsonValue;
            description: string;
            providers: string[];
            tags: string[];
            category: import(".prisma/client").$Enums.SkillCategory;
            isPublic: boolean;
            createdBy: string;
            usage: number;
            systemPrompt: string | null;
            maxTokens: number | null;
            skills: string[];
            instructions: string | null;
            rating: number | null;
            downloads: number;
            featured: boolean;
            changelog: Prisma.JsonValue;
            minTokens: number | null;
            configSchema: Prisma.JsonValue;
        };
    } & {
        provider: string;
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        status: import(".prisma/client").$Enums.AgentStatus;
        type: import(".prisma/client").$Enums.AgentType;
        version: string;
        config: Prisma.JsonValue;
        description: string | null;
        tools: string[];
        timeout: number;
        retryPolicy: Prisma.JsonValue;
        priority: number;
        creatorId: string;
        model: string;
        systemPrompt: string | null;
        maxTokens: number;
        temperature: number;
        skills: string[];
        templateId: string | null;
        instructions: string | null;
        topP: number;
        memoryType: string;
        maxMemorySize: number;
        memoryWindow: number;
        capabilities: Prisma.JsonValue;
        rateLimits: Prisma.JsonValue;
        canCollaborate: boolean;
        shareContext: boolean;
        lastDeployed: Date | null;
        deployConfig: Prisma.JsonValue;
    }>;
    deleteAgentInstance(agentId: string, userId: string, organizationId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    executeAgent(context: AgentExecutionContext): Promise<AgentExecutionResult>;
    private executeStandaloneAgent;
    private executeToolDrivenAgent;
    private executeHybridAgent;
    private executeMultiTaskingAgent;
    private executeMultiProviderAgent;
    private getAgentById;
    private determineExecutionStrategy;
    private trackAgentMetrics;
    getAgentInstances(organizationId: string, filters?: {
        type?: AgentType;
        status?: AgentStatus;
        search?: string;
        creatorId?: string;
        limit?: number;
        offset?: number;
    }): Promise<{
        agents: ({
            _count: {
                sessions: number;
                tasks: number;
                conversations: number;
            };
            creator: {
                id: string;
                email: string;
                firstName: string;
                lastName: string;
            };
            template: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                organizationId: string | null;
                name: string;
                version: string;
                config: Prisma.JsonValue;
                description: string;
                providers: string[];
                tags: string[];
                category: import(".prisma/client").$Enums.SkillCategory;
                isPublic: boolean;
                createdBy: string;
                usage: number;
                systemPrompt: string | null;
                maxTokens: number | null;
                skills: string[];
                instructions: string | null;
                rating: number | null;
                downloads: number;
                featured: boolean;
                changelog: Prisma.JsonValue;
                minTokens: number | null;
                configSchema: Prisma.JsonValue;
            };
        } & {
            provider: string;
            id: string;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string;
            name: string;
            status: import(".prisma/client").$Enums.AgentStatus;
            type: import(".prisma/client").$Enums.AgentType;
            version: string;
            config: Prisma.JsonValue;
            description: string | null;
            tools: string[];
            timeout: number;
            retryPolicy: Prisma.JsonValue;
            priority: number;
            creatorId: string;
            model: string;
            systemPrompt: string | null;
            maxTokens: number;
            temperature: number;
            skills: string[];
            templateId: string | null;
            instructions: string | null;
            topP: number;
            memoryType: string;
            maxMemorySize: number;
            memoryWindow: number;
            capabilities: Prisma.JsonValue;
            rateLimits: Prisma.JsonValue;
            canCollaborate: boolean;
            shareContext: boolean;
            lastDeployed: Date | null;
            deployConfig: Prisma.JsonValue;
        })[];
        total: number;
        hasMore: boolean;
    }>;
    getAgentInstance(agentId: string, organizationId: string): Promise<{
        _count: {
            sessions: number;
            tasks: number;
            collaborations: number;
            conversations: number;
        };
        sessions: {
            id: string;
            userId: string | null;
            context: Prisma.JsonValue;
            memory: Prisma.JsonValue;
            expiresAt: Date | null;
            createdAt: Date;
            updatedAt: Date;
            name: string | null;
            status: string;
            type: string;
            metadata: Prisma.JsonValue;
            startedAt: Date;
            agentId: string;
            maxMessages: number;
            memoryStrategy: string;
            collaborationId: string | null;
            isShared: boolean;
            messageCount: number;
            tokenUsage: Prisma.JsonValue;
            lastActivityAt: Date;
            endedAt: Date | null;
        }[];
        creator: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
        };
        tasks: {
            error: string | null;
            id: string;
            context: Prisma.JsonValue;
            createdAt: Date;
            updatedAt: Date;
            name: string;
            output: Prisma.JsonValue | null;
            status: import(".prisma/client").$Enums.TaskStatus;
            type: string;
            input: Prisma.JsonValue;
            description: string | null;
            sessionId: string | null;
            maxRetries: number;
            priority: number;
            dependencies: string[];
            retryCount: number;
            duration: number | null;
            metadata: Prisma.JsonValue;
            startedAt: Date | null;
            completedAt: Date | null;
            agentId: string;
            errorDetails: Prisma.JsonValue | null;
            parentTaskId: string | null;
            dependents: string[];
        }[];
        template: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            organizationId: string | null;
            name: string;
            version: string;
            config: Prisma.JsonValue;
            description: string;
            providers: string[];
            tags: string[];
            category: import(".prisma/client").$Enums.SkillCategory;
            isPublic: boolean;
            createdBy: string;
            usage: number;
            systemPrompt: string | null;
            maxTokens: number | null;
            skills: string[];
            instructions: string | null;
            rating: number | null;
            downloads: number;
            featured: boolean;
            changelog: Prisma.JsonValue;
            minTokens: number | null;
            configSchema: Prisma.JsonValue;
        };
        metrics: {
            id: string;
            createdAt: Date;
            date: Date;
            successRate: number | null;
            hour: number | null;
            totalTokens: number;
            totalCost: number | null;
            agentId: string;
            messages: number;
            collaborations: number;
            conversations: number;
            inputTokens: number;
            outputTokens: number;
            avgResponseTime: number | null;
            minResponseTime: number | null;
            maxResponseTime: number | null;
            errorRate: number | null;
            satisfactionScore: number | null;
            costPerToken: number | null;
            costPerMessage: number | null;
            tasksCompleted: number;
            tasksAssigned: number;
        }[];
    } & {
        provider: string;
        id: string;
        isActive: boolean;
        createdAt: Date;
        updatedAt: Date;
        organizationId: string;
        name: string;
        status: import(".prisma/client").$Enums.AgentStatus;
        type: import(".prisma/client").$Enums.AgentType;
        version: string;
        config: Prisma.JsonValue;
        description: string | null;
        tools: string[];
        timeout: number;
        retryPolicy: Prisma.JsonValue;
        priority: number;
        creatorId: string;
        model: string;
        systemPrompt: string | null;
        maxTokens: number;
        temperature: number;
        skills: string[];
        templateId: string | null;
        instructions: string | null;
        topP: number;
        memoryType: string;
        maxMemorySize: number;
        memoryWindow: number;
        capabilities: Prisma.JsonValue;
        rateLimits: Prisma.JsonValue;
        canCollaborate: boolean;
        shareContext: boolean;
        lastDeployed: Date | null;
        deployConfig: Prisma.JsonValue;
    }>;
    getAgentMetrics(agentId: string, organizationId: string, dateRange?: {
        from: Date;
        to: Date;
    }): Promise<{
        id: string;
        createdAt: Date;
        date: Date;
        successRate: number | null;
        hour: number | null;
        totalTokens: number;
        totalCost: number | null;
        agentId: string;
        messages: number;
        collaborations: number;
        conversations: number;
        inputTokens: number;
        outputTokens: number;
        avgResponseTime: number | null;
        minResponseTime: number | null;
        maxResponseTime: number | null;
        errorRate: number | null;
        satisfactionScore: number | null;
        costPerToken: number | null;
        costPerMessage: number | null;
        tasksCompleted: number;
        tasksAssigned: number;
    }[]>;
}
