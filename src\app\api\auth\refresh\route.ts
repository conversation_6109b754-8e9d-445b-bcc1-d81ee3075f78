import { NextRequest, NextResponse } from "next/server";

/**
 * 🔐 Frontend Token Refresh API Proxy
 * 
 * This route handles automatic token refresh using httpOnly cookies.
 */

export async function POST(request: NextRequest) {
    try {
        const refreshToken = request.cookies.get('refresh_token')?.value;

        if (!refreshToken) {
            return NextResponse.json(
                { message: "No refresh token found" },
                { status: 401 }
            );
        }

        // Forward refresh request to backend
        const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
        const response = await fetch(`${backendUrl}/auth/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Forwarded-For': request.ip || 'unknown',
                'User-Agent': request.headers.get('user-agent') || 'unknown',
            },
            body: JSON.stringify({ refreshToken }),
        });

        const data = await response.json();

        if (!response.ok) {
            // If refresh fails, clear cookies
            const errorResponse = NextResponse.json(data, { status: response.status });

            const cookieOptions = {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax' as const,
                path: '/',
                maxAge: 0,
            };

            errorResponse.cookies.set('access_token', '', cookieOptions);
            errorResponse.cookies.set('refresh_token', '', cookieOptions);

            return errorResponse;
        }

        // Create response with new tokens
        const nextResponse = NextResponse.json({
            user: data.user,
            expiresAt: data.expiresAt,
            session: data.session,
        });

        // Set new secure httpOnly cookies
        const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax' as const,
            path: '/',
        };

        // New access token (short-lived)
        nextResponse.cookies.set('access_token', data.accessToken, {
            ...cookieOptions,
            maxAge: 15 * 60, // 15 minutes
        });

        // New refresh token (longer-lived)
        nextResponse.cookies.set('refresh_token', data.refreshToken, {
            ...cookieOptions,
            maxAge: 7 * 24 * 60 * 60, // 7 days
        });

        return nextResponse;

    } catch (error) {
        console.error("Token refresh error:", error);

        // Clear cookies on error
        const errorResponse = NextResponse.json(
            { message: "Token refresh failed" },
            { status: 500 }
        );

        const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax' as const,
            path: '/',
            maxAge: 0,
        };

        errorResponse.cookies.set('access_token', '', cookieOptions);
        errorResponse.cookies.set('refresh_token', '', cookieOptions);

        return errorResponse;
    }
}