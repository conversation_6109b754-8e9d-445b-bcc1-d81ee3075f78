{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "setup": "node setup.js", "setup:production": "node setup.js --production", "setup:force": "node setup.js --force", "setup:help": "node setup.js --help", "backend:dev": "cd backend && npm run dev", "backend:build": "cd backend && npm run build", "backend:start": "cd backend && npm run start:prod", "db:studio": "cd backend && npx prisma studio", "db:reset": "cd backend && npm run db:reset"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/platform-socket.io": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.1.5", "@next/font": "^14.2.15", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@reactflow/node-resizer": "^2.2.14", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/jsonwebtoken": "^9.0.10", "@types/pako": "^2.0.3", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.20", "bcryptjs": "^3.0.2", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.23.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "next": "14.2.23", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "pako": "^2.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prettier": "^3.3.3", "prisma": "^6.13.0", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-hook-form": "^7.61.1", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "redis": "^5.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "stripe": "^17.6.0", "tempo-devtools": "^2.0.109", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}