"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Shield,
  LayoutDashboard,
  Users,
  Building2,
  Settings,
  Database,
  BarChart3,
  Activity,
  AlertTriangle,
  Bell,
  Search,
  Menu,
  ChevronLeft,
  ChevronRight,
  LogOut,
  User,
  Moon,
  Sun,
  Monitor,
  Command,
  Zap,
  Key,
  Globe,
  FileText,
  CreditCard,
  Mail,
  Webhook,
} from "lucide-react";
import Link from "next/link";
import { ThemeSwitcher } from "@/components/theme-switcher";
import apiClient from "@/lib/api-client";
import apixClient from "@/lib/apix-client";

interface AdminUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  permissions: string[];
}

interface AdminLayoutProps {
  children: React.ReactNode;
}

const adminNavigationItems = [
  {
    title: "Overview",
    href: "/admin",
    icon: LayoutDashboard,
    badge: null,
    description: "System overview and key metrics",
  },
  {
    title: "Organizations",
    href: "/admin/organizations",
    icon: Building2,
    badge: "12",
    description: "Manage all organizations and tenants",
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: Users,
    badge: "1.2k",
    description: "User management and access control",
  },
  {
    title: "System Health",
    href: "/admin/system",
    icon: Activity,
    badge: null,
    description: "Monitor system performance and health",
  },
  {
    title: "Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
    badge: null,
    description: "Platform-wide analytics and insights",
  },
  {
    title: "Security",
    href: "/admin/security",
    icon: Shield,
    badge: "3",
    description: "Security monitoring and audit logs",
  },
  {
    title: "Database",
    href: "/admin/database",
    icon: Database,
    badge: null,
    description: "Database management and monitoring",
  },
  {
    title: "API Keys",
    href: "/admin/api-keys",
    icon: Key,
    badge: null,
    description: "Manage system-wide API keys",
  },
  {
    title: "Integrations",
    href: "/admin/integrations",
    icon: Webhook,
    badge: "5",
    description: "Third-party integrations and webhooks",
  },
  {
    title: "Billing",
    href: "/admin/billing",
    icon: CreditCard,
    badge: null,
    description: "Billing and subscription management",
  },
  {
    title: "Notifications",
    href: "/admin/notifications",
    icon: Mail,
    badge: null,
    description: "System notifications and alerts",
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: Settings,
    badge: null,
    description: "Global system settings",
  },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [user, setUser] = useState<AdminUser | null>(null);
  const [notifications, setNotifications] = useState(7);
  const [searchOpen, setSearchOpen] = useState(false);
  const [systemStatus, setSystemStatus] = useState({
    health: "healthy" as "healthy" | "warning" | "critical",
    uptime: "99.98%",
    activeUsers: 1247,
    totalOrganizations: 12,
  });

  const router = useRouter();
  const pathname = usePathname();

  // Load admin user data on mount
  useEffect(() => {
    const loadAdminData = async () => {
      try {
        const response = await apiClient.getProfile();
        
        // Check if user has admin permissions
        if (!response.user.role.includes("ADMIN") && !response.user.role.includes("SUPER_ADMIN")) {
          router.push("/dashboard");
          return;
        }

        setUser({
          ...response.user,
          permissions: response.permissions || [],
        });

        // Connect to APIX for real-time admin updates
        if (!apixClient.getConnectionStatus().connected) {
          try {
            const token = localStorage.getItem("authToken");
            if (token) {
              await apixClient.connect(token, "system");
              apixClient.subscribeToSystem();
              apixClient.subscribeToAdminEvents();
            }
          } catch (wsError) {
            console.warn("Admin WebSocket connection failed:", wsError);
          }
        }

        // Load system status
        await loadSystemStatus();
      } catch (error) {
        console.error("Failed to load admin data:", error);
        router.push("/auth/login");
      }
    };

    loadAdminData();
  }, [router]);

  const loadSystemStatus = async () => {
    try {
      const statusResponse = await apiClient.getSystemStatus();
      setSystemStatus(statusResponse.data);
    } catch (error) {
      console.error("Failed to load system status:", error);
      setSystemStatus(prev => ({ ...prev, health: "warning" }));
    }
  };

  // Set up real-time admin notifications
  useEffect(() => {
    const unsubscribeSystemAlert = apixClient.on('admin_system_alert', (event) => {
      setNotifications(prev => prev + 1);
      setSystemStatus(prev => ({ ...prev, health: event.data.severity }));
    });

    const unsubscribeUserActivity = apixClient.on('admin_user_activity', (event) => {
      if (event.data.type === 'login' || event.data.type === 'organization_created') {
        setNotifications(prev => prev + 1);
      }
    });

    const unsubscribeSecurityEvent = apixClient.on('admin_security_event', (event) => {
      setNotifications(prev => prev + 1);
    });

    return () => {
      unsubscribeSystemAlert();
      unsubscribeUserActivity();
      unsubscribeSecurityEvent();
    };
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case "k":
            e.preventDefault();
            setSearchOpen(true);
            break;
          case "b":
            e.preventDefault();
            setSidebarCollapsed(!sidebarCollapsed);
            break;
          case "h":
            e.preventDefault();
            router.push("/admin");
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [sidebarCollapsed, router]);

  const handleLogout = async () => {
    try {
      await apiClient.logout();
    } catch (error) {
      console.error("Admin logout error:", error);
    } finally {
      apiClient.clearToken();
      apixClient.disconnect();
      router.push("/auth/login");
    }
  };

  const AdminSidebarContent = ({ mobile = false }: { mobile?: boolean }) => (
    <div className="flex flex-col h-full">
      {/* Logo and Status */}
      <div className="flex items-center px-4 py-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-red-500/10 rounded-lg">
            <Shield className="h-6 w-6 text-red-500" />
          </div>
          {(!sidebarCollapsed || mobile) && (
            <div>
              <h1 className="text-xl font-bold text-red-500">Admin Panel</h1>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  systemStatus.health === 'healthy' ? 'bg-green-500' :
                  systemStatus.health === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`} />
                <p className="text-xs text-muted-foreground">
                  System {systemStatus.health}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      <Separator />

      {/* Quick Stats */}
      {(!sidebarCollapsed || mobile) && (
        <div className="px-4 py-3 bg-muted/30">
          <div className="grid grid-cols-2 gap-2 text-center">
            <div>
              <p className="text-lg font-bold">{systemStatus.activeUsers}</p>
              <p className="text-xs text-muted-foreground">Active Users</p>
            </div>
            <div>
              <p className="text-lg font-bold">{systemStatus.totalOrganizations}</p>
              <p className="text-xs text-muted-foreground">Organizations</p>
            </div>
          </div>
        </div>
      )}

      <Separator />

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-2">
          {adminNavigationItems.map((item) => {
            const isActive = pathname === item.href;
            return (
              <TooltipProvider key={item.href}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link href={item.href}>
                      <Button
                        variant={isActive ? "secondary" : "ghost"}
                        className={`w-full justify-start ${sidebarCollapsed && !mobile ? "px-2" : ""
                          }`}
                        onClick={() => mobile && setMobileMenuOpen(false)}
                      >
                        <item.icon className="h-5 w-5" />
                        {(!sidebarCollapsed || mobile) && (
                          <>
                            <span className="ml-3">{item.title}</span>
                            {item.badge && (
                              <Badge
                                variant="secondary"
                                className="ml-auto text-xs"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </>
                        )}
                      </Button>
                    </Link>
                  </TooltipTrigger>
                  {sidebarCollapsed && !mobile && (
                    <TooltipContent side="right">
                      <div>
                        <p className="font-medium">{item.title}</p>
                        <p className="text-xs text-muted-foreground">{item.description}</p>
                      </div>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </nav>
      </ScrollArea>

      <Separator />

      {/* User Profile */}
      <div className="p-4">
        {user && (
          <div className="space-y-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className={`w-full ${sidebarCollapsed && !mobile ? "px-2" : "justify-start"
                    }`}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                      alt={`${user.firstName} ${user.lastName}`}
                    />
                    <AvatarFallback>
                      {user.firstName[0]}
                      {user.lastName[0]}
                    </AvatarFallback>
                  </Avatar>
                  {(!sidebarCollapsed || mobile) && (
                    <div className="ml-3 text-left">
                      <p className="text-sm font-medium">
                        {user.firstName} {user.lastName}
                      </p>
                      <p className="text-xs text-red-600 font-medium">
                        System Administrator
                      </p>
                    </div>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push("/dashboard")}>
                  <User className="mr-2 h-4 w-4" />
                  Switch to User Dashboard
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Admin Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {(!sidebarCollapsed || mobile) && (
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>Uptime: {systemStatus.uptime}</span>
                <Badge variant={
                  systemStatus.health === 'healthy' ? 'secondary' :
                  systemStatus.health === 'warning' ? 'outline' : 'destructive'
                } className="text-xs">
                  {systemStatus.health}
                </Badge>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full animate-spin" />
          <span>Loading Admin Panel...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      <aside
        className={`hidden md:flex flex-col border-r bg-card/50 backdrop-blur-sm transition-all duration-300 ${sidebarCollapsed ? "w-16" : "w-72"
          }`}
      >
        <AdminSidebarContent />

        {/* Collapse Toggle */}
        <div className="absolute -right-3 top-6 z-10">
          <Button
            variant="outline"
            size="icon"
            className="h-6 w-6 rounded-full bg-background border shadow-md"
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            {sidebarCollapsed ? (
              <ChevronRight className="h-3 w-3" />
            ) : (
              <ChevronLeft className="h-3 w-3" />
            )}
          </Button>
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
        <SheetContent side="left" className="p-0 w-72">
          <AdminSidebarContent mobile />
        </SheetContent>
      </Sheet>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Bar */}
        <header className="flex items-center justify-between px-6 py-4 border-b bg-card/50 backdrop-blur-sm">
          <div className="flex items-center space-x-4">
            {/* Mobile Menu Button */}
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setMobileMenuOpen(true)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
            </Sheet>

            {/* Admin Badge */}
            <Badge variant="destructive" className="hidden md:flex">
              <Shield className="mr-1 h-3 w-3" />
              Administrator
            </Badge>

            {/* Search */}
            <div className="relative hidden md:block">
              <Button
                variant="outline"
                className="w-64 justify-start text-muted-foreground"
                onClick={() => setSearchOpen(true)}
              >
                <Search className="mr-2 h-4 w-4" />
                Search admin panel...
                <kbd className="ml-auto pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                  <span className="text-xs">⌘</span>K
                </kbd>
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* System Status Indicator */}
            <div className="hidden md:flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                systemStatus.health === 'healthy' ? 'bg-green-500' :
                systemStatus.health === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <span className="text-sm text-muted-foreground capitalize">
                {systemStatus.health}
              </span>
            </div>

            {/* Theme Switcher */}
            <ThemeSwitcher />

            {/* Notifications */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="relative">
                  <Bell className="h-5 w-5" />
                  {notifications > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
                    >
                      {notifications}
                    </Badge>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Admin Notifications</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="space-y-2 p-2">
                  <div className="p-3 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">Security Alert</p>
                    <p className="text-xs text-red-700 dark:text-red-300">
                      Multiple failed login attempts detected
                    </p>
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                      5 minutes ago
                    </p>
                  </div>
                  <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800">
                    <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">System Warning</p>
                    <p className="text-xs text-yellow-700 dark:text-yellow-300">
                      Database connection pool reaching limits
                    </p>
                    <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                      12 minutes ago
                    </p>
                  </div>
                  <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200">New Organization</p>
                    <p className="text-xs text-blue-700 dark:text-blue-300">
                      TechCorp Inc. successfully onboarded
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      1 hour ago
                    </p>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-center">
                  View all admin notifications
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`}
                      alt={`${user.firstName} ${user.lastName}`}
                    />
                    <AvatarFallback>
                      {user.firstName[0]}
                      {user.lastName[0]}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {user.firstName} {user.lastName}
                    </p>
                    <p className="text-xs leading-none text-red-600">
                      System Administrator
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => router.push("/dashboard")}>
                  <Zap className="mr-2 h-4 w-4" />
                  User Dashboard
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Admin Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto bg-background">
          {children}
        </main>
      </div>

      {/* Admin Command Palette */}
      {searchOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg">
            <div className="flex items-center space-x-2">
              <Command className="h-4 w-4" />
              <span className="text-sm font-medium">Admin Quick Actions</span>
            </div>
            <div className="grid gap-2">
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/admin/organizations");
                }}
              >
                <Building2 className="mr-2 h-4 w-4" />
                Manage Organizations
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/admin/users");
                }}
              >
                <Users className="mr-2 h-4 w-4" />
                User Management
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/admin/system");
                }}
              >
                <Activity className="mr-2 h-4 w-4" />
                System Health
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  setSearchOpen(false);
                  router.push("/admin/security");
                }}
              >
                <Shield className="mr-2 h-4 w-4" />
                Security Center
              </Button>
            </div>
            <Button
              variant="outline"
              onClick={() => setSearchOpen(false)}
              className="mt-2"
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}