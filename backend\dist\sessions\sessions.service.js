"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
let SessionsService = class SessionsService {
    constructor(prisma, cacheManager) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
    }
    async createSession(userId, organizationId, sessionData, expirationHours = 24) {
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + expirationHours);
        const session = await this.prisma.session.create({
            data: {
                userId,
                organizationId,
                sessionData: sessionData,
                context: {},
                memory: {},
                expiresAt,
                isActive: true,
            },
        });
        await this.cacheManager.set(`session:${session.id}`, session, expirationHours * 60 * 60 * 1000);
        await this.cacheManager.set(`user_session:${userId}`, session.id, expirationHours * 60 * 60 * 1000);
        return session;
    }
    async getSession(sessionId) {
        let session = await this.cacheManager.get(`session:${sessionId}`);
        if (!session) {
            session = await this.prisma.session.findUnique({
                where: { id: sessionId },
                include: {
                    user: {
                        include: {
                            organization: true,
                        },
                    },
                },
            });
            if (session) {
                await this.cacheManager.set(`session:${sessionId}`, session, 3600000);
            }
        }
        return session;
    }
    async getUserActiveSession(userId) {
        const cachedSessionId = await this.cacheManager.get(`user_session:${userId}`);
        if (cachedSessionId) {
            return this.getSession(cachedSessionId);
        }
        const session = await this.prisma.session.findFirst({
            where: {
                userId,
                isActive: true,
                expiresAt: {
                    gt: new Date(),
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
            include: {
                user: {
                    include: {
                        organization: true,
                    },
                },
            },
        });
        if (session) {
            await this.cacheManager.set(`user_session:${userId}`, session.id, 3600000);
        }
        return session;
    }
    async updateSessionContext(sessionId, context) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const updatedContext = {
            ...session.context,
            ...context,
            lastUpdated: new Date(),
        };
        const updatedSession = await this.prisma.session.update({
            where: { id: sessionId },
            data: {
                context: updatedContext,
                updatedAt: new Date(),
            },
        });
        await this.cacheManager.set(`session:${sessionId}`, updatedSession, 3600000);
        return updatedSession;
    }
    async updateSessionMemory(sessionId, memory) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const updatedMemory = {
            ...session.memory,
            ...memory,
            lastUpdated: new Date(),
        };
        const prunedMemory = this.pruneMemory(updatedMemory);
        const updatedSession = await this.prisma.session.update({
            where: { id: sessionId },
            data: {
                memory: prunedMemory,
                updatedAt: new Date(),
            },
        });
        await this.cacheManager.set(`session:${sessionId}`, updatedSession, 3600000);
        return updatedSession;
    }
    async addConversationMessage(sessionId, role, content, metadata) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const currentMemory = session.memory;
        const conversationHistory = currentMemory.conversationHistory || [];
        const newMessage = {
            role,
            content,
            timestamp: Date.now(),
            metadata,
        };
        conversationHistory.push(newMessage);
        const trimmedHistory = conversationHistory.slice(-100);
        return this.updateSessionMemory(sessionId, {
            conversationHistory: trimmedHistory,
        });
    }
    async setWorkflowState(sessionId, workflowId, state) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const currentMemory = session.memory;
        const workflowState = currentMemory.workflowState || {};
        workflowState[workflowId] = {
            ...state,
            lastUpdated: Date.now(),
        };
        return this.updateSessionMemory(sessionId, {
            workflowState,
        });
    }
    async getWorkflowState(sessionId, workflowId) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const currentMemory = session.memory;
        return currentMemory.workflowState?.[workflowId] || null;
    }
    async setAgentMemory(sessionId, agentId, memory) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const currentMemory = session.memory;
        const agentMemory = currentMemory.agentMemory || {};
        agentMemory[agentId] = {
            ...memory,
            lastUpdated: Date.now(),
        };
        return this.updateSessionMemory(sessionId, {
            agentMemory,
        });
    }
    async getAgentMemory(sessionId, agentId) {
        const session = await this.getSession(sessionId);
        if (!session) {
            throw new common_1.NotFoundException('Session not found');
        }
        const currentMemory = session.memory;
        return currentMemory.agentMemory?.[agentId] || null;
    }
    async invalidateSession(sessionId) {
        await this.prisma.session.update({
            where: { id: sessionId },
            data: {
                isActive: false,
                updatedAt: new Date(),
            },
        });
        await this.cacheManager.del(`session:${sessionId}`);
    }
    async invalidateUserSessions(userId) {
        await this.prisma.session.updateMany({
            where: {
                userId,
                isActive: true,
            },
            data: {
                isActive: false,
                updatedAt: new Date(),
            },
        });
        await this.cacheManager.del(`user_session:${userId}`);
    }
    async getUserSessions(userId, includeInactive = false) {
        const whereClause = { userId };
        if (!includeInactive) {
            whereClause.isActive = true;
            whereClause.expiresAt = { gt: new Date() };
        }
        return this.prisma.session.findMany({
            where: whereClause,
            orderBy: {
                createdAt: 'desc',
            },
            select: {
                id: true,
                sessionData: true,
                isActive: true,
                createdAt: true,
                updatedAt: true,
                expiresAt: true,
            },
        });
    }
    async getOrganizationSessions(organizationId, limit = 100) {
        return this.prisma.session.findMany({
            where: {
                organizationId,
                isActive: true,
                expiresAt: {
                    gt: new Date(),
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
            take: limit,
            include: {
                user: {
                    select: {
                        id: true,
                        email: true,
                        firstName: true,
                        lastName: true,
                    },
                },
            },
        });
    }
    async cleanupExpiredSessions() {
        const expiredSessions = await this.prisma.session.findMany({
            where: {
                OR: [
                    { expiresAt: { lt: new Date() } },
                    {
                        isActive: false,
                        updatedAt: { lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
                    },
                ],
            },
            select: { id: true },
        });
        if (expiredSessions.length > 0) {
            const sessionIds = expiredSessions.map(s => s.id);
            await this.prisma.session.deleteMany({
                where: {
                    id: { in: sessionIds },
                },
            });
            for (const sessionId of sessionIds) {
                await this.cacheManager.del(`session:${sessionId}`);
            }
            return sessionIds.length;
        }
        return 0;
    }
    async getSessionAnalytics(organizationId, days = 30) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const sessions = await this.prisma.session.findMany({
            where: {
                organizationId,
                createdAt: {
                    gte: startDate,
                },
            },
            select: {
                id: true,
                createdAt: true,
                updatedAt: true,
                sessionData: true,
                isActive: true,
            },
        });
        const totalSessions = sessions.length;
        const activeSessions = sessions.filter(s => s.isActive).length;
        const avgSessionDuration = sessions.reduce((acc, session) => {
            const duration = session.updatedAt.getTime() - session.createdAt.getTime();
            return acc + duration;
        }, 0) / totalSessions;
        const dailyStats = sessions.reduce((acc, session) => {
            const day = session.createdAt.toISOString().split('T')[0];
            if (!acc[day]) {
                acc[day] = { count: 0, active: 0 };
            }
            acc[day].count++;
            if (session.isActive) {
                acc[day].active++;
            }
            return acc;
        }, {});
        return {
            totalSessions,
            activeSessions,
            avgSessionDuration: Math.round(avgSessionDuration / 1000 / 60),
            dailyStats,
        };
    }
    pruneMemory(memory) {
        const prunedMemory = { ...memory };
        if (prunedMemory.conversationHistory && prunedMemory.conversationHistory.length > 100) {
            prunedMemory.conversationHistory = prunedMemory.conversationHistory.slice(-100);
        }
        if (prunedMemory.workflowState) {
            const workflowEntries = Object.entries(prunedMemory.workflowState);
            if (workflowEntries.length > 10) {
                const sortedEntries = workflowEntries.sort((a, b) => b[1].lastUpdated - a[1].lastUpdated);
                prunedMemory.workflowState = Object.fromEntries(sortedEntries.slice(0, 10));
            }
        }
        if (prunedMemory.agentMemory) {
            const agentEntries = Object.entries(prunedMemory.agentMemory);
            if (agentEntries.length > 5) {
                const sortedEntries = agentEntries.sort((a, b) => b[1].lastUpdated - a[1].lastUpdated);
                prunedMemory.agentMemory = Object.fromEntries(sortedEntries.slice(0, 5));
            }
        }
        if (prunedMemory.temporaryData) {
            const oneHourAgo = Date.now() - 3600000;
            const filteredTempData = Object.entries(prunedMemory.temporaryData).reduce((acc, [key, value]) => {
                if (value.timestamp && value.timestamp > oneHourAgo) {
                    acc[key] = value;
                }
                return acc;
            }, {});
            prunedMemory.temporaryData = filteredTempData;
        }
        return prunedMemory;
    }
};
exports.SessionsService = SessionsService;
exports.SessionsService = SessionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object])
], SessionsService);
//# sourceMappingURL=sessions.service.js.map