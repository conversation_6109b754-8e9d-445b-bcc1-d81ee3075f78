"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ApixGateway_1;
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApixGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const prisma_service_1 = require("../prisma/prisma.service");
const event_emitter_1 = require("@nestjs/event-emitter");
const pako = require("pako");
let ApixGateway = ApixGateway_1 = class ApixGateway {
    constructor(jwtService, prisma, eventEmitter) {
        this.jwtService = jwtService;
        this.prisma = prisma;
        this.eventEmitter = eventEmitter;
        this.logger = new common_1.Logger(ApixGateway_1.name);
        this.connectedClients = new Map();
        this.userSockets = new Map();
        this.organizationSockets = new Map();
        this.roomSubscriptions = new Map();
        this.compressionThreshold = 1024;
        this.eventHistory = new Map();
        this.maxHistorySize = 1000;
    }
    afterInit(server) {
        this.logger.log('APIX Gateway initialized with production features');
        server.engine.generateId = () => {
            return `apix_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        };
        server.engine.compression = true;
        server.engine.perMessageDeflate = {
            threshold: this.compressionThreshold,
            concurrencyLimit: 10,
            memLevel: 7,
        };
    }
    async handleConnection(client) {
        try {
            this.logger.log(`Client attempting connection: ${client.id}`);
            const token = client.handshake.auth?.token;
            const organizationId = client.handshake.auth?.organizationId;
            if (!token || !organizationId) {
                this.logger.warn(`Unauthenticated connection attempt: ${client.id}`);
                client.disconnect();
                return;
            }
            const payload = await this.jwtService.verifyAsync(token);
            const user = await this.prisma.user.findUnique({
                where: { id: payload.sub },
                include: { organization: true },
            });
            if (!user || user.organizationId !== organizationId) {
                this.logger.warn(`Invalid user or organization: ${client.id}`);
                client.disconnect();
                return;
            }
            client.userId = user.id;
            client.organizationId = organizationId;
            client.user = user;
            this.connectedClients.set(client.id, client);
            if (!this.userSockets.has(user.id)) {
                this.userSockets.set(user.id, new Set());
            }
            this.userSockets.get(user.id).add(client.id);
            if (!this.organizationSockets.has(organizationId)) {
                this.organizationSockets.set(organizationId, new Set());
            }
            this.organizationSockets.get(organizationId).add(client.id);
            client.join(`organization:${organizationId}`);
            client.emit('apix:connected', {
                clientId: client.id,
                userId: user.id,
                organizationId,
                features: {
                    compression: true,
                    latencyTracking: true,
                    eventReplay: true,
                    maxRetryAttempts: 5,
                    heartbeatInterval: 30000,
                },
                serverTime: Date.now(),
            });
            const history = this.eventHistory.get(organizationId);
            if (history && history.length > 0) {
                client.emit('apix:replay', {
                    events: history.slice(-50),
                    totalCount: history.length,
                    timestamp: Date.now(),
                });
            }
            this.logger.log(`Client connected successfully: ${client.id} (User: ${user.id}, Org: ${organizationId})`);
            this.eventEmitter.emit('client.connected', {
                clientId: client.id,
                userId: user.id,
                organizationId,
                timestamp: Date.now(),
            });
        }
        catch (error) {
            this.logger.error(`Connection authentication failed: ${error.message}`, error.stack);
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        this.logger.log(`Client disconnected: ${client.id}`);
        this.connectedClients.delete(client.id);
        if (client.userId) {
            const userSockets = this.userSockets.get(client.userId);
            if (userSockets) {
                userSockets.delete(client.id);
                if (userSockets.size === 0) {
                    this.userSockets.delete(client.userId);
                }
            }
        }
        if (client.organizationId) {
            const orgSockets = this.organizationSockets.get(client.organizationId);
            if (orgSockets) {
                orgSockets.delete(client.id);
                if (orgSockets.size === 0) {
                    this.organizationSockets.delete(client.organizationId);
                }
            }
        }
        for (const [roomId, socketIds] of this.roomSubscriptions.entries()) {
            socketIds.delete(client.id);
            if (socketIds.size === 0) {
                this.roomSubscriptions.delete(roomId);
            }
        }
        this.eventEmitter.emit('client.disconnected', {
            clientId: client.id,
            userId: client.userId,
            organizationId: client.organizationId,
            timestamp: Date.now(),
        });
    }
    async handleEvent(client, event) {
        try {
            event.organizationId = client.organizationId;
            event.timestamp = new Date().toISOString();
            if (event.compressed && event.data) {
                try {
                    const decompressed = pako.inflate(event.data, { to: 'string' });
                    event.data = JSON.parse(decompressed);
                    event.compressed = false;
                }
                catch (error) {
                    this.logger.error('Event decompression failed', error);
                    return;
                }
            }
            this.storeEventInHistory(client.organizationId, event);
            if (event.channel) {
                this.server.to(event.channel).emit(event.type, event.data);
            }
            else {
                this.server.to(`organization:${client.organizationId}`).emit(event.type, event.data);
            }
            this.eventEmitter.emit(`apix.${event.type}`, {
                ...event,
                clientId: client.id,
                userId: client.userId,
            });
        }
        catch (error) {
            this.logger.error(`Event handling failed: ${error.message}`, error.stack);
            client.emit('apix:error', {
                message: 'Event processing failed',
                originalEvent: event.type,
            });
        }
    }
    handleJoinRoom(client, roomId) {
        client.join(roomId);
        if (!this.roomSubscriptions.has(roomId)) {
            this.roomSubscriptions.set(roomId, new Set());
        }
        this.roomSubscriptions.get(roomId).add(client.id);
        this.logger.log(`Client ${client.id} joined room: ${roomId}`);
        client.emit('room:joined', { roomId, timestamp: Date.now() });
    }
    handleLeaveRoom(client, roomId) {
        client.leave(roomId);
        const roomSockets = this.roomSubscriptions.get(roomId);
        if (roomSockets) {
            roomSockets.delete(client.id);
            if (roomSockets.size === 0) {
                this.roomSubscriptions.delete(roomId);
            }
        }
        this.logger.log(`Client ${client.id} left room: ${roomId}`);
        client.emit('room:left', { roomId, timestamp: Date.now() });
    }
    handleSubscribe(client, data) {
        for (const channel of data.channels) {
            client.join(channel);
            if (!this.roomSubscriptions.has(channel)) {
                this.roomSubscriptions.set(channel, new Set());
            }
            this.roomSubscriptions.get(channel).add(client.id);
        }
        client.emit('apix:subscribed', {
            channels: data.channels,
            timestamp: Date.now(),
        });
    }
    handleUnsubscribe(client, data) {
        for (const channel of data.channels) {
            client.leave(channel);
            const channelSockets = this.roomSubscriptions.get(channel);
            if (channelSockets) {
                channelSockets.delete(client.id);
                if (channelSockets.size === 0) {
                    this.roomSubscriptions.delete(channel);
                }
            }
        }
        client.emit('apix:unsubscribed', {
            channels: data.channels,
            timestamp: Date.now(),
        });
    }
    handleRequestReplay(client, data) {
        const history = this.eventHistory.get(client.organizationId);
        if (!history) {
            client.emit('apix:replay', { events: [], totalCount: 0, timestamp: Date.now() });
            return;
        }
        let events = history;
        if (data.since) {
            const sinceTime = new Date(data.since).getTime();
            events = history.filter(event => new Date(event.timestamp).getTime() > sinceTime);
        }
        if (data.limit) {
            events = events.slice(-data.limit);
        }
        client.emit('apix:replay', {
            events,
            totalCount: history.length,
            timestamp: Date.now(),
        });
    }
    handleHeartbeat(client, data) {
        client.emit('apix:heartbeat', {
            clientTimestamp: data.timestamp,
            serverTimestamp: Date.now(),
            latency: Date.now() - data.timestamp,
        });
    }
    async emitToUser(userId, eventType, data, options = {}) {
        const userSockets = this.userSockets.get(userId);
        if (!userSockets || userSockets.size === 0) {
            this.logger.warn(`No active sockets for user: ${userId}`);
            return;
        }
        const event = this.createEvent(eventType, data, options);
        for (const socketId of userSockets) {
            const socket = this.connectedClients.get(socketId);
            if (socket) {
                this.emitToSocket(socket, event);
            }
        }
    }
    async emitToOrganization(organizationId, eventType, data, options = {}) {
        const event = this.createEvent(eventType, data, options);
        event.organizationId = organizationId;
        this.storeEventInHistory(organizationId, event);
        this.server.to(`organization:${organizationId}`).emit(eventType, data);
    }
    async emitToRoom(roomId, eventType, data, options = {}) {
        const event = this.createEvent(eventType, data, options);
        this.server.to(roomId).emit(eventType, data);
    }
    async emitWorkflowEvent(organizationId, workflowId, eventType, data, options = {}) {
        const enrichedData = {
            ...data,
            workflowId,
            organizationId,
            timestamp: Date.now(),
        };
        await this.emitToRoom(`workflow:${workflowId}`, eventType, enrichedData, options);
        await this.emitToOrganization(organizationId, eventType, enrichedData, options);
    }
    async emitAgentEvent(organizationId, agentId, eventType, data, options = {}) {
        const enrichedData = {
            ...data,
            agentId,
            organizationId,
            timestamp: Date.now(),
        };
        await this.emitToRoom(`agent:${agentId}`, eventType, enrichedData, options);
        await this.emitToOrganization(organizationId, eventType, enrichedData, options);
    }
    async emitToolEvent(organizationId, toolId, eventType, data, options = {}) {
        const enrichedData = {
            ...data,
            toolId,
            organizationId,
            timestamp: Date.now(),
        };
        await this.emitToRoom(`tool:${toolId}`, eventType, enrichedData, options);
        await this.emitToOrganization(organizationId, eventType, enrichedData, options);
    }
    async emitSystemEvent(eventType, data, options = {}) {
        const event = this.createEvent(eventType, data, options);
        this.server.emit(eventType, data);
        for (const organizationId of this.organizationSockets.keys()) {
            this.storeEventInHistory(organizationId, { ...event, organizationId });
        }
    }
    createEvent(eventType, data, options = {}) {
        let processedData = data;
        let compressed = false;
        if (options.compress || this.shouldCompress(data)) {
            try {
                const jsonString = typeof data === 'string' ? data : JSON.stringify(data);
                const compressedData = pako.deflate(jsonString);
                processedData = Array.from(compressedData);
                compressed = true;
            }
            catch (error) {
                this.logger.warn('Event compression failed, sending uncompressed', error);
            }
        }
        return {
            type: eventType,
            data: processedData,
            timestamp: new Date().toISOString(),
            compressed,
            priority: options.priority || 'normal',
            metadata: {
                serverTimestamp: Date.now(),
                originalSize: JSON.stringify(data).length,
                compressedSize: compressed ? processedData.length : undefined,
            },
        };
    }
    shouldCompress(data) {
        try {
            const size = JSON.stringify(data).length;
            return size > this.compressionThreshold;
        }
        catch {
            return false;
        }
    }
    emitToSocket(socket, event) {
        if (event.compressed) {
            socket.compress(true).emit(event.type, event.data);
        }
        else {
            socket.emit(event.type, event.data);
        }
    }
    storeEventInHistory(organizationId, event) {
        if (!this.eventHistory.has(organizationId)) {
            this.eventHistory.set(organizationId, []);
        }
        const history = this.eventHistory.get(organizationId);
        history.push(event);
        if (history.length > this.maxHistorySize) {
            history.shift();
        }
    }
    async handleWorkflowStarted(payload) {
        await this.emitWorkflowEvent(payload.organizationId, payload.workflowId, 'workflow_started', payload, { priority: 'high' });
    }
    async handleWorkflowCompleted(payload) {
        await this.emitWorkflowEvent(payload.organizationId, payload.workflowId, 'workflow_completed', payload, { priority: 'high' });
    }
    async handleWorkflowFailed(payload) {
        await this.emitWorkflowEvent(payload.organizationId, payload.workflowId, 'workflow_failed', payload, { priority: 'high' });
    }
    async handleAgentThinking(payload) {
        await this.emitAgentEvent(payload.organizationId, payload.agentId, 'agent_thinking', payload);
    }
    async handleAgentResponse(payload) {
        await this.emitAgentEvent(payload.organizationId, payload.agentId, 'agent_response', payload, { compress: true });
    }
    async handleToolStarted(payload) {
        await this.emitToolEvent(payload.organizationId, payload.toolId, 'tool_call_start', payload);
    }
    async handleToolCompleted(payload) {
        await this.emitToolEvent(payload.organizationId, payload.toolId, 'tool_call_result', payload, { compress: true });
    }
    async handleToolFailed(payload) {
        await this.emitToolEvent(payload.organizationId, payload.toolId, 'tool_call_error', payload, { priority: 'high' });
    }
    async handleSystemAlert(payload) {
        await this.emitSystemEvent('system_alert', payload, { priority: 'high' });
    }
    async handleSystemMaintenance(payload) {
        await this.emitSystemEvent('system_maintenance', payload, { priority: 'high' });
    }
    getConnectionStats() {
        return {
            totalConnections: this.connectedClients.size,
            userConnections: this.userSockets.size,
            organizationConnections: this.organizationSockets.size,
            activeRooms: this.roomSubscriptions.size,
            eventHistorySize: Array.from(this.eventHistory.values()).reduce((sum, history) => sum + history.length, 0),
        };
    }
    getOrganizationConnections(organizationId) {
        const sockets = this.organizationSockets.get(organizationId);
        return sockets ? Array.from(sockets).map(socketId => {
            const socket = this.connectedClients.get(socketId);
            return socket ? {
                socketId,
                userId: socket.userId,
                connectedAt: socket.handshake.time,
            } : null;
        }).filter(Boolean) : [];
    }
    async broadcastToOrganization(organizationId, message, data) {
        await this.emitToOrganization(organizationId, 'broadcast', {
            message,
            data,
            timestamp: Date.now(),
        });
    }
    async cleanup() {
        this.logger.log('Cleaning up APIX Gateway...');
        this.server.emit('system_maintenance', {
            type: 'shutdown',
            message: 'Server is shutting down',
            timestamp: Date.now(),
        });
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.server.disconnectSockets(true);
        this.connectedClients.clear();
        this.userSockets.clear();
        this.organizationSockets.clear();
        this.roomSubscriptions.clear();
        this.eventHistory.clear();
        this.logger.log('APIX Gateway cleanup completed');
    }
};
exports.ApixGateway = ApixGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], ApixGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:event'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleEvent", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('join:room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ApixGateway.prototype, "handleJoinRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave:room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], ApixGateway.prototype, "handleLeaveRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:subscribe'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], ApixGateway.prototype, "handleSubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:unsubscribe'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], ApixGateway.prototype, "handleUnsubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:request_replay'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], ApixGateway.prototype, "handleRequestReplay", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('apix:heartbeat'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], ApixGateway.prototype, "handleHeartbeat", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.started'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleWorkflowStarted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleWorkflowCompleted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('workflow.failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleWorkflowFailed", null);
__decorate([
    (0, event_emitter_1.OnEvent)('agent.thinking'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleAgentThinking", null);
__decorate([
    (0, event_emitter_1.OnEvent)('agent.response'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleAgentResponse", null);
__decorate([
    (0, event_emitter_1.OnEvent)('tool.started'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleToolStarted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('tool.completed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleToolCompleted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('tool.failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleToolFailed", null);
__decorate([
    (0, event_emitter_1.OnEvent)('system.alert'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleSystemAlert", null);
__decorate([
    (0, event_emitter_1.OnEvent)('system.maintenance'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleSystemMaintenance", null);
exports.ApixGateway = ApixGateway = ApixGateway_1 = __decorate([
    (0, common_1.Injectable)(),
    (0, websockets_1.WebSocketGateway)({
        namespace: '/apix',
        cors: {
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true,
        },
        transports: ['websocket', 'polling'],
    }),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        prisma_service_1.PrismaService, typeof (_a = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _a : Object])
], ApixGateway);
//# sourceMappingURL=apix.gateway.js.map