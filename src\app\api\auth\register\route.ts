import { NextRequest, NextResponse } from "next/server";

/**
 * 🔐 Frontend Registration API Proxy
 * 
 * This route proxies registration requests to the backend API
 * and handles proper token management with httpOnly cookies.
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      firstName,
      lastName,
      email,
      password,
      company,
      subscribeNewsletter
    } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !password || !company) {
      return NextResponse.json(
        { message: "All fields are required" },
        { status: 400 }
      );
    }

    // Generate organization slug from company name
    const organizationSlug = company
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    // Forward request to backend
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
    const response = await fetch(`${backendUrl}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Forwarded-For': request.ip || 'unknown',
        'User-Agent': request.headers.get('user-agent') || 'unknown',
      },
      body: JSON.stringify({
        firstName,
        lastName,
        email,
        password,
        organizationName: company,
        organizationSlug,
        subscribeNewsletter: subscribeNewsletter || false,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    // Create response with secure cookies
    const nextResponse = NextResponse.json({
      user: data.user,
      organization: data.organization,
      expiresAt: data.expiresAt,
      session: data.session,
    }, { status: 201 });

    // Set secure httpOnly cookies for tokens
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
    };

    // Access token (short-lived)
    nextResponse.cookies.set('access_token', data.accessToken, {
      ...cookieOptions,
      maxAge: 15 * 60, // 15 minutes
    });

    // Refresh token (longer-lived)
    nextResponse.cookies.set('refresh_token', data.refreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60, // 7 days
    });

    return nextResponse;

  } catch (error) {
    console.error("Registration proxy error:", error);
    return NextResponse.json(
      { message: "Authentication service unavailable" },
      { status: 503 }
    );
  }
}