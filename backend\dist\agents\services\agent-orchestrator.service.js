"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentOrchestratorService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const session_memory_service_1 = require("./session-memory.service");
const skill_executor_service_1 = require("./skill-executor.service");
const provider_router_service_1 = require("./provider-router.service");
const task_tracker_service_1 = require("./task-tracker.service");
const event_emitter_1 = require("@nestjs/event-emitter");
let AgentOrchestratorService = class AgentOrchestratorService {
    constructor(prisma, cacheManager, sessionMemory, skillExecutor, providerRouter, taskTracker, eventEmitter) {
        this.prisma = prisma;
        this.cacheManager = cacheManager;
        this.sessionMemory = sessionMemory;
        this.skillExecutor = skillExecutor;
        this.providerRouter = providerRouter;
        this.taskTracker = taskTracker;
        this.eventEmitter = eventEmitter;
    }
    async createAgentInstance(createDto, creatorId, organizationId) {
        if (createDto.templateId) {
            const template = await this.prisma.agentTemplate.findFirst({
                where: {
                    id: createDto.templateId,
                    OR: [
                        { isPublic: true },
                        { organizationId },
                        { createdBy: creatorId },
                    ],
                },
            });
            if (!template) {
                throw new common_1.NotFoundException('Agent template not found or not accessible');
            }
            createDto.config = {
                ...(template.config || {}),
                ...(createDto.config || {}),
            };
            if (!createDto.systemPrompt && template.systemPrompt) {
                createDto.systemPrompt = template.systemPrompt;
            }
            if (!createDto.instructions && template.instructions) {
                createDto.instructions = template.instructions;
            }
            if (!createDto.skills?.length && template.skills?.length) {
                createDto.skills = template.skills;
            }
        }
        const agentInstance = await this.prisma.agentInstance.create({
            data: {
                name: createDto.name,
                description: createDto.description,
                templateId: createDto.templateId,
                type: createDto.type,
                status: client_1.AgentStatus.ACTIVE,
                config: createDto.config || {},
                systemPrompt: createDto.systemPrompt,
                instructions: createDto.instructions,
                provider: createDto.provider || 'openai',
                model: createDto.model || 'gpt-4',
                temperature: createDto.temperature ?? 0.7,
                maxTokens: createDto.maxTokens || 2000,
                skills: createDto.skills || [],
                tools: createDto.tools || [],
                capabilities: createDto.capabilities || {},
                canCollaborate: createDto.canCollaborate ?? false,
                shareContext: createDto.shareContext ?? false,
                priority: createDto.priority ?? 1,
                creatorId,
                organizationId,
            },
            include: {
                template: true,
                creator: true,
                organization: true,
            },
        });
        await this.sessionMemory.initializeAgentMemory(agentInstance.id, {
            type: agentInstance.type,
            systemPrompt: agentInstance.systemPrompt,
            instructions: agentInstance.instructions,
            capabilities: agentInstance.capabilities,
        });
        await this.cacheManager.set(`agent:${agentInstance.id}`, agentInstance, 300000);
        this.eventEmitter.emit('agent.created', {
            agentId: agentInstance.id,
            organizationId,
            creatorId,
            type: agentInstance.type,
            timestamp: Date.now(),
        });
        return agentInstance;
    }
    async updateAgentInstance(agentId, updateDto, userId, organizationId) {
        const agent = await this.prisma.agentInstance.findFirst({
            where: {
                id: agentId,
                organizationId,
            },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Agent instance not found');
        }
        const updatedAgent = await this.prisma.agentInstance.update({
            where: { id: agentId },
            data: {
                ...updateDto,
                updatedAt: new Date(),
            },
            include: {
                template: true,
                creator: true,
                organization: true,
            },
        });
        await this.cacheManager.set(`agent:${agentId}`, updatedAgent, 300000);
        if (updateDto.systemPrompt || updateDto.instructions || updateDto.capabilities) {
            await this.sessionMemory.updateAgentMemory(agentId, {
                systemPrompt: updatedAgent.systemPrompt,
                instructions: updatedAgent.instructions,
                capabilities: updatedAgent.capabilities,
            });
        }
        this.eventEmitter.emit('agent.updated', {
            agentId,
            organizationId,
            userId,
            changes: Object.keys(updateDto),
            timestamp: Date.now(),
        });
        return updatedAgent;
    }
    async deleteAgentInstance(agentId, userId, organizationId) {
        const agent = await this.prisma.agentInstance.findFirst({
            where: {
                id: agentId,
                organizationId,
            },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Agent instance not found');
        }
        const activeSessions = await this.prisma.agentSessionNew.count({
            where: {
                agentId,
                status: 'active',
            },
        });
        const activeTasks = await this.prisma.agentTask.count({
            where: {
                agentId,
                status: {
                    in: ['PENDING', 'RUNNING'],
                },
            },
        });
        if (activeSessions > 0 || activeTasks > 0) {
            throw new common_1.BadRequestException('Cannot delete agent with active sessions or tasks');
        }
        await this.prisma.agentInstance.update({
            where: { id: agentId },
            data: {
                status: client_1.AgentStatus.ARCHIVED,
                isActive: false,
            },
        });
        await this.cacheManager.del(`agent:${agentId}`);
        await this.sessionMemory.clearAgentMemory(agentId);
        this.eventEmitter.emit('agent.deleted', {
            agentId,
            organizationId,
            userId,
            timestamp: Date.now(),
        });
        return { success: true, message: 'Agent instance archived successfully' };
    }
    async executeAgent(context) {
        const startTime = Date.now();
        try {
            const agent = await this.getAgentById(context.agentId);
            if (!agent || agent.status !== client_1.AgentStatus.ACTIVE) {
                throw new common_1.BadRequestException('Agent is not active or not found');
            }
            const session = await this.sessionMemory.getOrCreateSession(context.agentId, context.sessionId, context.userId);
            const executionContext = {
                agent,
                session,
                input: context.input,
                context: context.context || {},
                metadata: context.metadata || {},
            };
            const provider = await this.providerRouter.getProvider(agent.provider, agent.organizationId);
            let result;
            switch (agent.type) {
                case client_1.AgentType.STANDALONE:
                    result = await this.executeStandaloneAgent(executionContext, provider);
                    break;
                case client_1.AgentType.TOOL_DRIVEN:
                    result = await this.executeToolDrivenAgent(executionContext, provider);
                    break;
                case client_1.AgentType.HYBRID:
                    result = await this.executeHybridAgent(executionContext, provider);
                    break;
                case client_1.AgentType.MULTI_TASKING:
                    result = await this.executeMultiTaskingAgent(executionContext, provider);
                    break;
                case client_1.AgentType.MULTI_PROVIDER:
                    result = await this.executeMultiProviderAgent(executionContext);
                    break;
                default:
                    throw new common_1.BadRequestException(`Unsupported agent type: ${agent.type}`);
            }
            result.duration = Date.now() - startTime;
            await this.sessionMemory.updateSessionMemory(context.sessionId, {
                lastInput: context.input,
                lastOutput: result.output,
                lastActivity: new Date(),
                messageCount: session.messageCount + 1,
                tokenUsage: {
                    ...session.tokenUsage,
                    total: (session.tokenUsage?.total || 0) + (result.tokens || 0),
                },
            });
            await this.trackAgentMetrics(context.agentId, result);
            this.eventEmitter.emit('agent.executed', {
                agentId: context.agentId,
                sessionId: context.sessionId,
                userId: context.userId,
                success: result.success,
                duration: result.duration,
                tokens: result.tokens,
                timestamp: Date.now(),
            });
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const result = {
                success: false,
                error: error.message,
                duration,
            };
            await this.trackAgentMetrics(context.agentId, result);
            this.eventEmitter.emit('agent.error', {
                agentId: context.agentId,
                sessionId: context.sessionId,
                error: error.message,
                duration,
                timestamp: Date.now(),
            });
            return result;
        }
    }
    async executeStandaloneAgent(context, provider) {
        const { agent, session, input } = context;
        const messages = await this.sessionMemory.getConversationHistory(session.id, agent.memoryWindow);
        const conversationMessages = [];
        if (agent.systemPrompt) {
            conversationMessages.push({
                role: 'system',
                content: agent.systemPrompt,
            });
        }
        conversationMessages.push(...messages);
        conversationMessages.push({
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
        });
        const response = await provider.chat({
            messages: conversationMessages,
            model: agent.model,
            temperature: agent.temperature,
            max_tokens: agent.maxTokens,
            top_p: agent.topP,
        });
        await this.sessionMemory.addMessage(session.id, {
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
            type: 'text',
            metadata: context.metadata,
        });
        await this.sessionMemory.addMessage(session.id, {
            role: 'assistant',
            content: response.content,
            type: 'text',
            tokens: response.tokens,
            cost: response.cost,
        });
        return {
            success: true,
            output: response.content,
            tokens: response.tokens,
            cost: response.cost,
            metadata: {
                model: agent.model,
                provider: agent.provider,
                messageCount: conversationMessages.length,
            },
        };
    }
    async executeToolDrivenAgent(context, provider) {
        const { agent, session, input } = context;
        const tools = await this.skillExecutor.getAgentTools(agent.id);
        const messages = await this.sessionMemory.getConversationHistory(session.id, agent.memoryWindow);
        const conversationMessages = [];
        if (agent.systemPrompt) {
            conversationMessages.push({
                role: 'system',
                content: agent.systemPrompt,
            });
        }
        conversationMessages.push(...messages);
        conversationMessages.push({
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
        });
        const response = await provider.chatWithTools({
            messages: conversationMessages,
            tools: tools.map(tool => tool.definition),
            model: agent.model,
            temperature: agent.temperature,
            max_tokens: agent.maxTokens,
        });
        let finalOutput = response.content;
        let totalTokens = response.tokens || 0;
        let totalCost = response.cost || 0;
        if (response.tool_calls?.length > 0) {
            for (const toolCall of response.tool_calls) {
                const toolResult = await this.skillExecutor.executeSkill(agent.id, toolCall.function.name, JSON.parse(toolCall.function.arguments));
                conversationMessages.push({
                    role: 'tool',
                    content: JSON.stringify(toolResult),
                    tool_call_id: toolCall.id,
                });
                const finalResponse = await provider.chat({
                    messages: conversationMessages,
                    model: agent.model,
                    temperature: agent.temperature,
                    max_tokens: agent.maxTokens,
                });
                finalOutput = finalResponse.content;
                totalTokens += finalResponse.tokens || 0;
                totalCost += finalResponse.cost || 0;
            }
        }
        await this.sessionMemory.addMessage(session.id, {
            role: 'user',
            content: typeof input === 'string' ? input : JSON.stringify(input),
            type: 'text',
            metadata: context.metadata,
        });
        await this.sessionMemory.addMessage(session.id, {
            role: 'assistant',
            content: finalOutput,
            type: 'text',
            tokens: totalTokens,
            cost: totalCost,
            metadata: {
                tool_calls: response.tool_calls?.length || 0,
            },
        });
        return {
            success: true,
            output: finalOutput,
            tokens: totalTokens,
            cost: totalCost,
            metadata: {
                model: agent.model,
                provider: agent.provider,
                tool_calls: response.tool_calls?.length || 0,
                tools_used: response.tool_calls?.map(tc => tc.function.name) || [],
            },
        };
    }
    async executeHybridAgent(context, provider) {
        const { agent, session, input } = context;
        const strategy = await this.determineExecutionStrategy(agent, input);
        switch (strategy) {
            case 'chat':
                return this.executeStandaloneAgent(context, provider);
            case 'tool':
                return this.executeToolDrivenAgent(context, provider);
            case 'hybrid':
                const chatResult = await this.executeStandaloneAgent(context, provider);
                const toolResult = await this.executeToolDrivenAgent(context, provider);
                return {
                    success: true,
                    output: {
                        chat: chatResult.output,
                        tool: toolResult.output,
                        combined: `${chatResult.output}\n\nTool Result: ${toolResult.output}`,
                    },
                    tokens: (chatResult.tokens || 0) + (toolResult.tokens || 0),
                    cost: (chatResult.cost || 0) + (toolResult.cost || 0),
                    metadata: {
                        strategy: 'hybrid',
                        chat_metadata: chatResult.metadata,
                        tool_metadata: toolResult.metadata,
                    },
                };
            default:
                return this.executeStandaloneAgent(context, provider);
        }
    }
    async executeMultiTaskingAgent(context, provider) {
        const { agent, session, input } = context;
        const tasks = await this.taskTracker.analyzeAndCreateTasks(agent.id, session.id, input);
        const results = [];
        let totalTokens = 0;
        let totalCost = 0;
        const maxConcurrent = agent.capabilities?.maxConcurrentTasks || 3;
        for (let i = 0; i < tasks.length; i += maxConcurrent) {
            const batch = tasks.slice(i, i + maxConcurrent);
            const batchPromises = batch.map(async (task) => {
                const taskContext = {
                    ...context,
                    input: task.input,
                    metadata: { ...context.metadata, taskId: task.id },
                };
                return this.executeStandaloneAgent(taskContext, provider);
            });
            const batchResults = await Promise.allSettled(batchPromises);
            batchResults.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    results.push({
                        taskId: batch[index].id,
                        success: true,
                        output: result.value.output,
                        tokens: result.value.tokens,
                        cost: result.value.cost,
                    });
                    totalTokens += result.value.tokens || 0;
                    totalCost += result.value.cost || 0;
                }
                else {
                    results.push({
                        taskId: batch[index].id,
                        success: false,
                        error: result.reason?.message || 'Unknown error',
                    });
                }
            });
        }
        const combinedOutput = results
            .filter(r => r.success)
            .map(r => r.output)
            .join('\n\n');
        return {
            success: results.some(r => r.success),
            output: combinedOutput,
            tokens: totalTokens,
            cost: totalCost,
            metadata: {
                tasks: results,
                total_tasks: tasks.length,
                successful_tasks: results.filter(r => r.success).length,
                failed_tasks: results.filter(r => !r.success).length,
            },
        };
    }
    async executeMultiProviderAgent(context) {
        const { agent } = context;
        const providers = await this.providerRouter.getAvailableProviders(agent.organizationId);
        const selectedProvider = await this.providerRouter.selectBestProvider(providers, {
            task: context.input,
            requirements: agent.capabilities?.providerRequirements || {},
        });
        return this.executeStandaloneAgent(context, selectedProvider);
    }
    async getAgentById(agentId) {
        let agent = await this.cacheManager.get(`agent:${agentId}`);
        if (!agent) {
            agent = await this.prisma.agentInstance.findUnique({
                where: { id: agentId },
                include: {
                    template: true,
                    organization: true,
                    creator: true,
                },
            });
            if (agent) {
                await this.cacheManager.set(`agent:${agentId}`, agent, 300000);
            }
        }
        return agent;
    }
    async determineExecutionStrategy(agent, input) {
        const inputText = typeof input === 'string' ? input : JSON.stringify(input);
        const toolKeywords = ['calculate', 'search', 'fetch', 'analyze', 'process'];
        const hasToolKeywords = toolKeywords.some(keyword => inputText.toLowerCase().includes(keyword));
        const chatKeywords = ['what', 'how', 'why', 'explain', 'tell me'];
        const hasChatKeywords = chatKeywords.some(keyword => inputText.toLowerCase().includes(keyword));
        if (hasToolKeywords && hasChatKeywords) {
            return 'hybrid';
        }
        else if (hasToolKeywords) {
            return 'tool';
        }
        else {
            return 'chat';
        }
    }
    async trackAgentMetrics(agentId, result) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        await this.prisma.agentMetrics.upsert({
            where: {
                agentId_date_hour: {
                    agentId,
                    date: today,
                    hour: null,
                },
            },
            update: {
                conversations: { increment: 1 },
                messages: { increment: 1 },
                totalTokens: { increment: result.tokens || 0 },
                totalCost: { increment: result.cost || 0 },
                successRate: result.success ? 1 : 0,
                errorRate: result.success ? 0 : 1,
            },
            create: {
                agentId,
                date: today,
                conversations: 1,
                messages: 1,
                totalTokens: result.tokens || 0,
                totalCost: result.cost || 0,
                successRate: result.success ? 1 : 0,
                errorRate: result.success ? 0 : 1,
            },
        });
    }
    async getAgentInstances(organizationId, filters) {
        const where = {
            organizationId,
        };
        if (filters?.type) {
            where.type = filters.type;
        }
        if (filters?.status) {
            where.status = filters.status;
        }
        if (filters?.creatorId) {
            where.creatorId = filters.creatorId;
        }
        if (filters?.search) {
            where.OR = [
                { name: { contains: filters.search, mode: 'insensitive' } },
                { description: { contains: filters.search, mode: 'insensitive' } },
            ];
        }
        const [agents, total] = await Promise.all([
            this.prisma.agentInstance.findMany({
                where,
                include: {
                    template: true,
                    creator: {
                        select: { id: true, firstName: true, lastName: true, email: true },
                    },
                    _count: {
                        select: {
                            sessions: true,
                            tasks: true,
                            conversations: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                take: filters?.limit || 20,
                skip: filters?.offset || 0,
            }),
            this.prisma.agentInstance.count({ where }),
        ]);
        return {
            agents,
            total,
            hasMore: (filters?.offset || 0) + agents.length < total,
        };
    }
    async getAgentInstance(agentId, organizationId) {
        const agent = await this.prisma.agentInstance.findFirst({
            where: { id: agentId, organizationId },
            include: {
                template: true,
                creator: {
                    select: { id: true, firstName: true, lastName: true, email: true },
                },
                sessions: {
                    take: 5,
                    orderBy: { createdAt: 'desc' },
                },
                tasks: {
                    take: 10,
                    orderBy: { createdAt: 'desc' },
                },
                metrics: {
                    take: 30,
                    orderBy: { date: 'desc' },
                },
                _count: {
                    select: {
                        sessions: true,
                        tasks: true,
                        conversations: true,
                        collaborations: true,
                    },
                },
            },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Agent instance not found');
        }
        return agent;
    }
    async getAgentMetrics(agentId, organizationId, dateRange) {
        const agent = await this.prisma.agentInstance.findFirst({
            where: { id: agentId, organizationId },
        });
        if (!agent) {
            throw new common_1.NotFoundException('Agent instance not found');
        }
        const where = { agentId };
        if (dateRange) {
            where.date = {
                gte: dateRange.from,
                lte: dateRange.to,
            };
        }
        return this.prisma.agentMetrics.findMany({
            where,
            orderBy: { date: 'desc' },
        });
    }
};
exports.AgentOrchestratorService = AgentOrchestratorService;
exports.AgentOrchestratorService = AgentOrchestratorService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService, Object, session_memory_service_1.SessionMemoryService,
        skill_executor_service_1.SkillExecutorService,
        provider_router_service_1.ProviderRouterService,
        task_tracker_service_1.TaskTrackerService, typeof (_a = typeof event_emitter_1.EventEmitter2 !== "undefined" && event_emitter_1.EventEmitter2) === "function" ? _a : Object])
], AgentOrchestratorService);
//# sourceMappingURL=agent-orchestrator.service.js.map