import { NextRequest, NextResponse } from "next/server";

/**
 * 🔐 Frontend Logout API Proxy
 * 
 * This route handles logout by clearing cookies and notifying the backend.
 */

export async function POST(request: NextRequest) {
    try {
        const refreshToken = request.cookies.get('refresh_token')?.value;

        // Notify backend of logout (if we have a refresh token)
        if (refreshToken) {
            const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

            try {
                await fetch(`${backendUrl}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${request.cookies.get('access_token')?.value}`,
                    },
                    body: JSON.stringify({ refreshToken }),
                });
            } catch (error) {
                console.error("Backend logout failed:", error);
                // Continue with frontend logout even if backend fails
            }
        }

        // Create response and clear cookies
        const response = NextResponse.json({ message: "Logged out successfully" });

        // Clear authentication cookies
        const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax' as const,
            path: '/',
            maxAge: 0, // Expire immediately
        };

        response.cookies.set('access_token', '', cookieOptions);
        response.cookies.set('refresh_token', '', cookieOptions);

        return response;

    } catch (error) {
        console.error("Logout error:", error);

        // Still clear cookies even if there's an error
        const response = NextResponse.json(
            { message: "Logout completed with errors" },
            { status: 500 }
        );

        const cookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax' as const,
            path: '/',
            maxAge: 0,
        };

        response.cookies.set('access_token', '', cookieOptions);
        response.cookies.set('refresh_token', '', cookieOptions);

        return response;
    }
}