/**
 * Retry mechanism for API calls with exponential backoff
 */

interface RetryOptions {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
    retryCondition?: (error: any) => boolean;
}

const defaultRetryCondition = (error: any): boolean => {
    // Retry on network errors, timeouts, and 5xx server errors
    if (!error.response) return true; // Network error
    const status = error.response?.status;
    return status >= 500 || status === 408 || status === 429; // Server error, timeout, or rate limit
};

export async function withRetry<T>(
    fn: () => Promise<T>,
    options: RetryOptions = {}
): Promise<T> {
    const {
        maxAttempts = 3,
        baseDelay = 1000,
        maxDelay = 10000,
        backoffFactor = 2,
        retryCondition = defaultRetryCondition,
    } = options;

    let lastError: any;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error;

            // Don't retry if we've reached max attempts or if retry condition is not met
            if (attempt === maxAttempts || !retryCondition(error)) {
                throw error;
            }

            // Calculate delay with exponential backoff
            const delay = Math.min(
                baseDelay * Math.pow(backoffFactor, attempt - 1),
                maxDelay
            );

            // Add jitter to prevent thundering herd
            const jitteredDelay = delay + Math.random() * 1000;

            console.log(`Retry attempt ${attempt}/${maxAttempts} after ${jitteredDelay}ms delay`);

            await new Promise(resolve => setTimeout(resolve, jitteredDelay));
        }
    }

    throw lastError;
}

/**
 * Enhanced API client with automatic retry
 */
export function createRetryClient(baseClient: any, retryOptions?: RetryOptions) {
    return {
        get: (url: string, config?: any) =>
            withRetry(() => baseClient.get(url, config), retryOptions),

        post: (url: string, data?: any, config?: any) =>
            withRetry(() => baseClient.post(url, data, config), retryOptions),

        put: (url: string, data?: any, config?: any) =>
            withRetry(() => baseClient.put(url, data, config), retryOptions),

        delete: (url: string, config?: any) =>
            withRetry(() => baseClient.delete(url, config), retryOptions),

        patch: (url: string, data?: any, config?: any) =>
            withRetry(() => baseClient.patch(url, data, config), retryOptions),
    };
}