import { Modu<PERSON> } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager';
import { ConfigModule } from '@nestjs/config';
import { ToolsController } from './tools.controller';
import { ToolsService } from './tools.service';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';
import { ToolManagerService } from './services/tool-manager.service';
import { ToolExecutionService } from './services/tool-execution.service';
import { ToolCacheService } from './services/tool-cache.service';
import { ApixModule } from '../apix/apix.module';

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    CacheModule.register(),
    ConfigModule,
    ApixModule,
  ],
  controllers: [ToolsController],
  providers: [
    ToolsService,
    ToolManagerService,
    ToolExecutionService,
    ToolCacheService,
  ],
  exports: [
    ToolsService,
    ToolManagerService,
    ToolExecutionService,
    ToolCacheService,
  ],
})
export class ToolsModule { }