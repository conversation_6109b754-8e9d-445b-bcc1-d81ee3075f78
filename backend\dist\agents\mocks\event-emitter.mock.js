"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockEventEmitter = void 0;
class MockEventEmitter {
    emit(event, data) {
        console.log(`Event emitted: ${event}`, data);
        return true;
    }
    on(event, listener) {
        console.log(`Event listener registered: ${event}`);
        return this;
    }
    once(event, listener) {
        console.log(`One-time event listener registered: ${event}`);
        return this;
    }
    removeListener(event, listener) {
        console.log(`Event listener removed: ${event}`);
        return this;
    }
    removeAllListeners(event) {
        console.log(`All event listeners removed: ${event || 'all'}`);
        return this;
    }
}
exports.MockEventEmitter = MockEventEmitter;
//# sourceMappingURL=event-emitter.mock.js.map