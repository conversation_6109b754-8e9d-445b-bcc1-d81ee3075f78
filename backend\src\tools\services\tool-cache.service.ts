import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { ConfigService } from '@nestjs/config';
import { ToolCacheStrategy } from '@prisma/client';
import { ApixGateway } from '../../apix/apix.gateway';
import * as crypto from 'crypto';

export interface CacheOptions {
    ttl?: number;
    strategy?: ToolCacheStrategy;
    dependencies?: string[];
    compress?: boolean;
}

export interface CacheStats {
    hits: number;
    misses: number;
    hitRate: number;
    totalSize: number;
    entryCount: number;
    avgEntrySize: number;
}

@Injectable()
export class ToolCacheService {
    private readonly logger = new Logger(ToolCacheService.name);
    private readonly defaultTTL: number;
    private readonly maxCacheSize: number;

    constructor(
        private prisma: PrismaService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        private configService: ConfigService,
        private apixGateway: ApixGateway,
    ) {
        this.defaultTTL = this.configService.get<number>('TOOL_CACHE_TTL', 3600);
        this.maxCacheSize = this.configService.get<number>('TOOL_MAX_CACHE_SIZE', 100 * 1024 * 1024); // 100MB
    }

    // ============================================================================
    // MAIN CACHE OPERATIONS
    // ============================================================================

    async get(toolId: string, input: any): Promise<any> {
        const inputHash = this.generateInputHash(input);
        const cacheKey = `tool_cache:${toolId}:${inputHash}`;

        try {
            // Try memory cache first
            const memoryCached = await this.cacheManager.get(cacheKey);
            if (memoryCached) {
                await this.recordCacheHit(toolId, inputHash, 'memory');
                return (memoryCached as any).output;
            }

            // Try database cache
            const dbCached = await this.prisma.toolCache.findUnique({
                where: {
                    toolId_inputHash: {
                        toolId,
                        inputHash,
                    },
                },
            });

            if (dbCached && this.isCacheValid(dbCached)) {
                // Update hit count and last accessed
                await this.prisma.toolCache.update({
                    where: { id: dbCached.id },
                    data: {
                        hits: { increment: 1 },
                        lastAccessed: new Date(),
                    },
                });

                // Store in memory cache for faster access
                await this.cacheManager.set(cacheKey, dbCached, this.defaultTTL * 1000);

                await this.recordCacheHit(toolId, inputHash, 'database');
                return dbCached.output;
            }

            // Cache miss
            await this.recordCacheMiss(toolId, inputHash, 'not_found');
            return null;

        } catch (error) {
            this.logger.error(`Cache get error for tool ${toolId}:`, error);
            await this.recordCacheMiss(toolId, inputHash, 'error');
            return null;
        }
    }

    async set(
        toolId: string,
        input: any,
        output: any,
        ttl?: number,
        options?: CacheOptions,
    ): Promise<void> {
        const inputHash = this.generateInputHash(input);
        const cacheKey = `tool_cache:${toolId}:${inputHash}`;
        const finalTTL = ttl || this.defaultTTL;
        const expiresAt = new Date(Date.now() + finalTTL * 1000);

        try {
            // Get tool to check cache strategy
            const tool = await this.prisma.toolDefinition.findUnique({
                where: { id: toolId },
                select: { cacheStrategy: true, cacheTTL: true },
            });

            if (!tool || tool.cacheStrategy === ToolCacheStrategy.NONE) {
                return;
            }

            // Calculate cache entry size
            const entrySize = this.calculateEntrySize(input, output);

            // Check if cache is too large
            if (entrySize > this.maxCacheSize / 10) { // Don't cache entries larger than 10% of max size
                this.logger.warn(`Cache entry too large for tool ${toolId}: ${entrySize} bytes`);
                return;
            }

            // Prepare cache data
            const cacheData = {
                toolId,
                inputHash,
                input,
                output,
                strategy: options?.strategy || tool.cacheStrategy,
                ttl: finalTTL,
                dependencies: options?.dependencies || [],
                dependencyHash: options?.dependencies ? this.generateDependencyHash(options.dependencies) : null,
                size: entrySize,
                expiresAt,
            };

            // Store in database
            await this.prisma.toolCache.upsert({
                where: {
                    toolId_inputHash: {
                        toolId,
                        inputHash,
                    },
                },
                update: {
                    output: cacheData.output,
                    ttl: cacheData.ttl,
                    dependencies: cacheData.dependencies,
                    dependencyHash: cacheData.dependencyHash,
                    size: cacheData.size,
                    lastAccessed: new Date(),
                    expiresAt: cacheData.expiresAt,
                    isValid: true,
                },
                create: cacheData,
            });

            // Store in memory cache
            await this.cacheManager.set(cacheKey, cacheData, finalTTL * 1000);

            // Emit real-time cache event
            await this.apixGateway.emitToolEvent(
                'system', // Cache events are system-wide
                toolId,
                'tool.cache.set',
                {
                    toolId,
                    inputHash,
                    size: entrySize,
                    ttl: finalTTL,
                },
                { priority: 'low' }
            );

            // Clean up expired entries periodically
            if (Math.random() < 0.01) { // 1% chance to trigger cleanup
                this.cleanupExpiredEntries().catch(error =>
                    this.logger.error('Cache cleanup error:', error)
                );
            }

        } catch (error) {
            this.logger.error(`Cache set error for tool ${toolId}:`, error);
        }
    }

    async invalidate(toolId: string, inputHash?: string, reason = 'manual'): Promise<number> {
        let invalidatedCount = 0;

        try {
            if (inputHash) {
                // Invalidate specific cache entry
                const result = await this.prisma.toolCache.updateMany({
                    where: {
                        toolId,
                        inputHash,
                        isValid: true,
                    },
                    data: {
                        isValid: false,
                        invalidatedBy: reason,
                        invalidatedAt: new Date(),
                    },
                });

                invalidatedCount = result.count;

                // Remove from memory cache
                const cacheKey = `tool_cache:${toolId}:${inputHash}`;
                await this.cacheManager.del(cacheKey);

            } else {
                // Invalidate all cache entries for the tool
                const result = await this.prisma.toolCache.updateMany({
                    where: {
                        toolId,
                        isValid: true,
                    },
                    data: {
                        isValid: false,
                        invalidatedBy: reason,
                        invalidatedAt: new Date(),
                    },
                });

                invalidatedCount = result.count;

                // Clear memory cache entries for this tool
                await this.clearMemoryCacheForTool(toolId);
            }

            this.logger.log(`Invalidated ${invalidatedCount} cache entries for tool ${toolId}`);

            // Emit real-time invalidation event
            await this.apixGateway.emitToolEvent(
                'system',
                toolId,
                'tool.cache.invalidated',
                {
                    toolId,
                    inputHash,
                    reason,
                    count: invalidatedCount,
                },
                { priority: 'normal' }
            );

        } catch (error) {
            this.logger.error(`Cache invalidation error for tool ${toolId}:`, error);
        }

        return invalidatedCount;
    }

    async invalidateByDependency(dependency: string): Promise<number> {
        try {
            const dependencyHash = this.generateDependencyHash([dependency]);

            const result = await this.prisma.toolCache.updateMany({
                where: {
                    dependencyHash,
                    isValid: true,
                },
                data: {
                    isValid: false,
                    invalidatedBy: `dependency_${dependency}`,
                    invalidatedAt: new Date(),
                },
            });

            this.logger.log(`Invalidated ${result.count} cache entries due to dependency: ${dependency}`);

            return result.count;

        } catch (error) {
            this.logger.error(`Dependency invalidation error for ${dependency}:`, error);
            return 0;
        }
    }

    // ============================================================================
    // CACHE MANAGEMENT
    // ============================================================================

    async cleanupExpiredEntries(): Promise<number> {
        try {
            const now = new Date();

            const expiredEntries = await this.prisma.toolCache.findMany({
                where: {
                    OR: [
                        { expiresAt: { lt: now } },
                        { isValid: false },
                    ],
                },
                select: { id: true, toolId: true, inputHash: true },
            });

            if (expiredEntries.length === 0) {
                return 0;
            }

            // Delete expired entries
            const deleteResult = await this.prisma.toolCache.deleteMany({
                where: {
                    id: {
                        in: expiredEntries.map(entry => entry.id),
                    },
                },
            });

            // Clear from memory cache
            for (const entry of expiredEntries) {
                const cacheKey = `tool_cache:${entry.toolId}:${entry.inputHash}`;
                await this.cacheManager.del(cacheKey);
            }

            this.logger.log(`Cleaned up ${deleteResult.count} expired cache entries`);

            return deleteResult.count;

        } catch (error) {
            this.logger.error('Cache cleanup error:', error);
            return 0;
        }
    }

    async optimizeCache(): Promise<void> {
        try {
            // Get cache statistics
            const stats = await this.getCacheStats();

            if (stats.totalSize > this.maxCacheSize * 0.8) { // 80% of max size
                await this.evictLeastUsedEntries();
            }

            // Update cache analytics
            await this.updateCacheAnalytics();

        } catch (error) {
            this.logger.error('Cache optimization error:', error);
        }
    }

    private async evictLeastUsedEntries(): Promise<number> {
        const entriesToEvict = await this.prisma.toolCache.findMany({
            where: { isValid: true },
            orderBy: [
                { hits: 'asc' },
                { lastAccessed: 'asc' },
            ],
            take: 100, // Evict 100 least used entries
            select: { id: true, toolId: true, inputHash: true },
        });

        if (entriesToEvict.length === 0) {
            return 0;
        }

        const deleteResult = await this.prisma.toolCache.deleteMany({
            where: {
                id: {
                    in: entriesToEvict.map(entry => entry.id),
                },
            },
        });

        // Clear from memory cache
        for (const entry of entriesToEvict) {
            const cacheKey = `tool_cache:${entry.toolId}:${entry.inputHash}`;
            await this.cacheManager.del(cacheKey);
        }

        this.logger.log(`Evicted ${deleteResult.count} least used cache entries`);

        return deleteResult.count;
    }

    // ============================================================================
    // CACHE ANALYTICS
    // ============================================================================

    async getCacheStats(toolId?: string): Promise<CacheStats> {
        const where = toolId ? { toolId } : {};

        const [
            totalEntries,
            totalSize,
            hitStats,
        ] = await Promise.all([
            this.prisma.toolCache.count({ where: { ...where, isValid: true } }),
            this.prisma.toolCache.aggregate({
                where: { ...where, isValid: true },
                _sum: { size: true },
            }),
            this.prisma.toolCache.aggregate({
                where: { ...where, isValid: true },
                _sum: { hits: true },
            }),
        ]);

        const totalHits = hitStats._sum.hits || 0;
        const estimatedRequests = totalHits + (totalEntries * 0.1); // Estimate misses
        const hitRate = estimatedRequests > 0 ? totalHits / estimatedRequests : 0;

        return {
            hits: totalHits,
            misses: Math.max(0, estimatedRequests - totalHits),
            hitRate,
            totalSize: totalSize._sum.size || 0,
            entryCount: totalEntries,
            avgEntrySize: totalEntries > 0 ? (totalSize._sum.size || 0) / totalEntries : 0,
        };
    }

    async getToolCacheStats(toolId: string): Promise<any> {
        const [
            basicStats,
            strategyStats,
            recentActivity,
        ] = await Promise.all([
            this.getCacheStats(toolId),
            this.prisma.toolCache.groupBy({
                by: ['strategy'],
                where: { toolId, isValid: true },
                _count: true,
                _sum: { hits: true, size: true },
            }),
            this.prisma.toolCache.findMany({
                where: { toolId, isValid: true },
                orderBy: { lastAccessed: 'desc' },
                take: 10,
                select: {
                    inputHash: true,
                    hits: true,
                    lastAccessed: true,
                    size: true,
                },
            }),
        ]);

        return {
            ...basicStats,
            byStrategy: Object.fromEntries(
                strategyStats.map(stat => [
                    stat.strategy,
                    {
                        count: stat._count,
                        hits: stat._sum.hits || 0,
                        size: stat._sum.size || 0,
                    },
                ])
            ),
            recentActivity,
        };
    }

    private async updateCacheAnalytics(): Promise<void> {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const tools = await this.prisma.toolDefinition.findMany({
            where: { isActive: true },
            select: { id: true },
        });

        for (const tool of tools) {
            const stats = await this.getCacheStats(tool.id);

            await this.prisma.toolAnalytics.upsert({
                where: {
                    toolId_date_hour: {
                        toolId: tool.id,
                        date: today,
                        hour: null,
                    },
                },
                update: {
                    cacheHits: stats.hits,
                    cacheMisses: stats.misses,
                    cacheHitRate: stats.hitRate,
                },
                create: {
                    toolId: tool.id,
                    date: today,
                    cacheHits: stats.hits,
                    cacheMisses: stats.misses,
                    cacheHitRate: stats.hitRate,
                },
            });
        }
    }

    // ============================================================================
    // HELPER METHODS
    // ============================================================================

    private generateInputHash(input: any): string {
        // Create deterministic hash of input
        const inputString = JSON.stringify(input, Object.keys(input).sort());
        return crypto.createHash('sha256').update(inputString).digest('hex');
    }

    private generateDependencyHash(dependencies: string[]): string {
        const depString = dependencies.sort().join('|');
        return crypto.createHash('sha256').update(depString).digest('hex');
    }

    private calculateEntrySize(input: any, output: any): number {
        const inputSize = JSON.stringify(input).length;
        const outputSize = JSON.stringify(output).length;
        return inputSize + outputSize;
    }

    private isCacheValid(cacheEntry: any): boolean {
        if (!cacheEntry.isValid) {
            return false;
        }

        if (cacheEntry.expiresAt && new Date() > cacheEntry.expiresAt) {
            return false;
        }

        return true;
    }

    private async recordCacheHit(toolId: string, inputHash: string, source: string): Promise<void> {
        await this.apixGateway.emitToolEvent(
            'system',
            toolId,
            'tool.cache.hit',
            {
                toolId,
                inputHash,
                source,
                savedTime: 100, // Estimate saved time in ms
            },
            { priority: 'low' }
        );
    }

    private async recordCacheMiss(toolId: string, inputHash: string, reason: string): Promise<void> {
        await this.apixGateway.emitToolEvent(
            'system',
            toolId,
            'tool.cache.miss',
            {
                toolId,
                inputHash,
                reason,
            },
            { priority: 'low' }
        );
    }

    private async clearMemoryCacheForTool(toolId: string): Promise<void> {
        // Get all cache entries for the tool to clear from memory
        const entries = await this.prisma.toolCache.findMany({
            where: { toolId },
            select: { inputHash: true },
        });

        for (const entry of entries) {
            const cacheKey = `tool_cache:${toolId}:${entry.inputHash}`;
            await this.cacheManager.del(cacheKey);
        }
    }

    // ============================================================================
    // PUBLIC MANAGEMENT METHODS
    // ============================================================================

    async clearAllCache(): Promise<number> {
        try {
            const result = await this.prisma.toolCache.deleteMany({});

            // Clear memory cache
            // Note: This is a simplified approach. In production, you might want to use cache tags
            // await this.cacheManager.reset(); // This method may not be available in all cache implementations

            this.logger.log(`Cleared all cache: ${result.count} entries`);

            return result.count;

        } catch (error) {
            this.logger.error('Clear all cache error:', error);
            return 0;
        }
    }

    async getCacheHealth(): Promise<any> {
        try {
            const [
                totalStats,
                oldestEntry,
                newestEntry,
                topTools,
            ] = await Promise.all([
                this.getCacheStats(),
                this.prisma.toolCache.findFirst({
                    where: { isValid: true },
                    orderBy: { lastAccessed: 'asc' },
                    select: { lastAccessed: true },
                }),
                this.prisma.toolCache.findFirst({
                    where: { isValid: true },
                    orderBy: { lastAccessed: 'desc' },
                    select: { lastAccessed: true },
                }),
                this.prisma.toolCache.groupBy({
                    by: ['toolId'],
                    where: { isValid: true },
                    _count: true,
                    _sum: { hits: true },
                    orderBy: { _sum: { hits: 'desc' } },
                    take: 5,
                }),
            ]);

            return {
                ...totalStats,
                health: {
                    status: totalStats.hitRate > 0.5 ? 'healthy' : 'needs_attention',
                    oldestEntry: oldestEntry?.lastAccessed,
                    newestEntry: newestEntry?.lastAccessed,
                    topCachedTools: topTools.map(tool => ({
                        toolId: tool.toolId,
                        entries: tool._count,
                        hits: tool._sum.hits || 0,
                    })),
                },
            };

        } catch (error) {
            this.logger.error('Cache health check error:', error);
            return {
                status: 'error',
                error: error.message,
            };
        }
    }
}